#pragma once
#include<windows.h>
#include <TlHelp32.h>
#include <stdio.h>
#include <vector>
#include <unordered_map>

// Page Guard Hook 结构
struct PageGuardHookInfo {
	PVOID targetAddress;      // 目标函数地址
	PVOID hookFunction;       // Hook 函数地址
	PVOID* originalFunction;  // 原始函数指针存储位置
	DWORD originalProtect;    // 原始内存保护属性
	BYTE originalBytes[16];   // 原始字节备份
	SIZE_T pageSize;          // 页面大小
	bool isActive;            // Hook 是否激活
};

class HardBreakHook {
public:
	PVOID exceptionHandle = nullptr;   //异常处理句柄                        
	uintptr_t dr0 = 0;
	uintptr_t dr1 = 0;
	uintptr_t dr2 = 0;
	uintptr_t dr3 = 0;
	uintptr_t dr7 = 0;
	DWORD removeThread[0x20] = { 0 };
	PVECTORED_EXCEPTION_HANDLER exceptionHandleFunction = nullptr;
	
	// Page Guard Hook 静态成员
	static std::vector<PageGuardHookInfo> pageGuardHooks;
	static PVOID pageGuardExceptionHandle;
	static bool pageGuardInitialized;
	
	// INT3 Hook 静态成员
	static std::vector<PageGuardHookInfo> int3Hooks;
	static PVOID int3ExceptionHandle;
	static bool int3Initialized;

	// 默认构造函数
	HardBreakHook() {}

	// 参数构造函数
	HardBreakHook(uintptr_t d0, uintptr_t d1, uintptr_t d2, uintptr_t d3, uintptr_t d7,
		PVECTORED_EXCEPTION_HANDLER exceptionHandleFunc, DWORD* remove)
		: dr0(d0), dr1(d1), dr2(d2), dr3(d3), dr7(d7), exceptionHandleFunction(exceptionHandleFunc) {
		memcpy(removeThread, remove, sizeof(remove));
		exceptionHandle = AddVectoredExceptionHandler(1, exceptionHandleFunction);//1表示添加到异常处理链前端
		hook();
	}
 
	// 检查线程是否已被排除
	BOOL isHookThread(DWORD threadId);

	// 设置异常处理函数
	void setExceptionHandlingFunction(PVECTORED_EXCEPTION_HANDLER func);

	// 安装Hook
	void hook();

	// 卸载Hook
	void unHook();
	
	// Page Guard Hook 相关函数
	static LONG WINAPI PageGuardExceptionHandler(PEXCEPTION_POINTERS pExceptionInfo);
	static bool InitPageGuardHook();
	static bool InstallPageGuardHook(PVOID targetAddress, PVOID hookFunction, PVOID* originalFunction);
	static bool RemovePageGuardHook(PVOID targetAddress);
	static void CleanupPageGuardHooks();
	static PageGuardHookInfo* FindPageGuardHook(PVOID address);
	
	// INT3 Hook 相关函数
	static LONG WINAPI Int3ExceptionHandler(PEXCEPTION_POINTERS pExceptionInfo);
	static bool InitInt3Hook();
	static bool InstallInt3Hook(PVOID targetAddress, PVOID hookFunction, PVOID* originalFunction);
	static bool RemoveInt3Hook(PVOID targetAddress);
	static void CleanupInt3Hooks();
	static PageGuardHookInfo* FindInt3Hook(PVOID address);
};