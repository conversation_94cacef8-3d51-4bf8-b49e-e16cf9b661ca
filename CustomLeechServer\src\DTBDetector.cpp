#include <Windows.h>
#include <stdio.h>
#include <stdlib.h>
#include "../include/DriverInterface.h"
#include "../include/DTBDetector.h"
#include "../include/VMP.h"

// DTB检测器类
class DTBDetector {
private:
    DriverInterface* m_pDriver;
    
public:
    DTBDetector(DriverInterface* pDriver) : m_pDriver(pDriver) {}
    
    // 检查X64系统的DTB有效性
    BOOL ValidateDTB_X64(QWORD paDTB, PBYTE pbPage) {
        VMProtectBegin("DTBDetector::ValidateDTB_X64");
        DWORD cKernelValid = 0;
        DWORD cUserZero = 0, cKernelZero = 0;
        PQWORD ptes = (PQWORD)pbPage;
        BOOL fSelfRef = FALSE;
        
        // 检查用户模式页表
        if ((ptes[0] & 1) && ((ptes[0] & 0x0000fffffffff000) > 0x100000000000ULL)) {
            return FALSE;
        }
        
        // 统计用户空间零页表项
        for (DWORD i = 0; i < 256; i++) {
            if (ptes[i] == 0) cUserZero++;
        }
        
        // 统计内核空间页表项
        for (DWORD i = 256; i < 512; i++) {
            if (ptes[i] == 0) {
                cKernelZero++;
            }
            // 检查有效的内核页表项
            if (((ptes[i] & 0x8000000000000087) == 0x03) && 
                ((ptes[i] & 0x0000fffffffff000) < 0x100000000000ULL)) {
                cKernelValid++;
            }
            // 检查自引用条目
            if ((ptes[i] & 0x0000fffffffff083) == (paDTB | 0x03)) {
                fSelfRef = TRUE;
            }
        }
        
        // 需要满足：自引用存在，有足够的内核页表项，有足够的零页表项
        BOOL result = fSelfRef && (cKernelValid >= 6) && (cUserZero > 0x40) && (cKernelZero > 0x40);
        VMProtectEnd();
        return result;
    }
    
    // 检查X86 PAE系统的DTB有效性
    BOOL ValidateDTB_X86PAE(QWORD paDTB, PBYTE pbPage) {
        VMProtectBegin("DTBDetector::ValidateDTB_X86PAE");
        // PDPT必须有4个连续的有效条目
        for (QWORD i = 0; i < 0x1000; i += 8) {
            if ((i < 0x20) && (*(PQWORD)(pbPage + i) != (paDTB + (i << 9) + 0x1001))) {
                return FALSE;
            } else if ((i >= 0x20) && *(PQWORD)(pbPage + i)) {
                return FALSE;
            }
        }
        VMProtectEnd();
        return TRUE;
    }
    
    // 检查X86系统的DTB有效性
    BOOL ValidateDTB_X86(QWORD paDTB, PBYTE pbPage) {
        VMProtectBegin("DTBDetector::ValidateDTB_X86");
        DWORD c;
        DWORD i;
        // 检查自引用条目
        if ((*(PDWORD)(pbPage + 0xc00) & 0xfffff003) != (DWORD)(paDTB | 0x03)) {
            return FALSE;
        }
        // 检查用户模式页表
        if (*pbPage != 0x67) {
            return FALSE;
        }
        // 统计supervisor条目
        for (c = 0, i = 0x800; i < 0x1000; i += 4) {
            if ((*(pbPage + i) == 0x63) || (*(pbPage + i) == 0xe3)) {
                c++;
            }
            if (c > 16) return TRUE;
        }
        VMProtectEnd();
        return FALSE;
    }
    
    // 检测X64 Low Stub以快速定位DTB
    QWORD DetectLowStub() {
        VMProtectBeginVirtualization("DTBDetector::DetectLowStub");
        // 注意：1MB 局部数组会导致默认线程栈溢出，改为堆分配
        BYTE *pbLowStub = (BYTE*)malloc(0x100000);
        if (!pbLowStub) {
            printf("[DTB] Failed to allocate low stub buffer\n");
            return 0;
        }
        ZeroMemory(pbLowStub, 0x100000);

        QWORD qwDTB = 0;
        printf("[DTB] Scanning low 1MB for X64 low stub...\n");

        // 读取低1MB内存（跳过保留区域）
        if (!m_pDriver->ReadPhysicalMemory(0x1000, 0x9f000, pbLowStub + 0x1000)) {
            printf("[DTB] Failed to read low memory\n");
            free(pbLowStub);
            return 0;
        }

        // 扫描Low Stub特征
        for (DWORD o = 0x1000; o < 0x100000; o += 0x1000) {
            // 检查特征字节
            if (0x00000001000600E9 != (0xffffffffffff00ff & *(PQWORD)(pbLowStub + o + 0x000))) continue;
            if (0xfffff80000000000 != (0xfffff80000000003 & *(PQWORD)(pbLowStub + o + 0x070))) continue;
            if (0xffffff0000000fff & *(PQWORD)(pbLowStub + o + 0x0a0)) continue;

            // 找到DTB
            qwDTB = *(PQWORD)(pbLowStub + o + 0x0a0);
            printf("[DTB] Found X64 low stub at offset 0x%lx, DTB=0x%llx\n", (unsigned long)o, qwDTB);
            free(pbLowStub);
            VMProtectEnd();
            return qwDTB;
        }

        free(pbLowStub);
        VMProtectEnd();
        return 0;
    }
    
    // 扫描内存查找DTB
    QWORD ScanForDTB(DWORD dwMemoryModel = 0) {
        VMProtectBeginMutation("DTBDetector::ScanForDTB");
        BYTE pbPage[0x1000];
        QWORD qwDTB = 0;
        
        printf("[DTB] Starting DTB scan...\n");
        
        // 首先尝试X64 Low Stub（最快）
        if (dwMemoryModel == 0 || dwMemoryModel == 1) { // Auto or X64
            qwDTB = DetectLowStub();
            if (qwDTB) {
                // 验证DTB
                if (m_pDriver->ReadPhysicalMemory(qwDTB, 0x1000, pbPage)) {
                    if (ValidateDTB_X64(qwDTB, pbPage)) {
                        printf("[DTB] Validated X64 DTB: 0x%llx\n", qwDTB);
                        VMProtectEnd();
                        return qwDTB;
                    }
                }
                qwDTB = 0;
            }
        }
        
        // 扫描低16MB内存
        printf("[DTB] Scanning low 16MB for DTB...\n");
        for (QWORD pa = 0; pa < 0x1000000; pa += 0x1000) {
            // 读取页面
            if (!m_pDriver->ReadPhysicalMemory(pa, 0x1000, pbPage)) {
                continue;
            }
            
            // 尝试X64 DTB验证
            if (dwMemoryModel == 0 || dwMemoryModel == 1) {
                if (ValidateDTB_X64(pa, pbPage)) {
                    printf("[DTB] Found X64 DTB at: 0x%llx\n", pa);
                    VMProtectEnd();
                    return pa;
                }
            }
            
            // 尝试X86 PAE DTB验证
            if (dwMemoryModel == 0 || dwMemoryModel == 2) {
                if (ValidateDTB_X86PAE(pa, pbPage)) {
                    printf("[DTB] Found X86 PAE DTB at: 0x%llx\n", pa);
                    VMProtectEnd();
                    return pa;
                }
            }
            
            // 尝试X86 DTB验证
            if (dwMemoryModel == 0 || dwMemoryModel == 3) {
                if (ValidateDTB_X86(pa, pbPage)) {
                    printf("[DTB] Found X86 DTB at: 0x%llx\n", pa);
                    VMProtectEnd();
                    return pa;
                }
            }
        }
        
        printf("[DTB] DTB scan completed, no valid DTB found\n");
        VMProtectEnd();
        return 0;
    }
    
    // Get system DTB (with cache)
    static QWORD GetSystemDTB(DriverInterface* pDriver, BOOL bForceRescan = FALSE) {
        VMProtectBegin("DTBDetector::GetSystemDTB");
        static QWORD s_qwCachedDTB = 0;
        
        if (!bForceRescan && s_qwCachedDTB) {
            printf("[DTB] Returning cached DTB: 0x%llx\n", s_qwCachedDTB);
            VMProtectEnd();
            return s_qwCachedDTB;
        }
        
        // First try to get DTB from driver interface
        QWORD qwDriverDTB = pDriver->GetDTB();
        
        // Check if the driver returned a valid DTB
        // The driver will return the actual DTB via __readcr3() if successful
        // or 0x1AA000 as a fallback if the IOCTL fails
        if (qwDriverDTB != 0 && qwDriverDTB != 0x1AA000) {
            printf("[DTB] Successfully retrieved DTB from driver: 0x%llx\n", qwDriverDTB);
            s_qwCachedDTB = qwDriverDTB;
            VMProtectEnd();
            return s_qwCachedDTB;
        }
        
        // If we got the fallback value, try scanning
        if (qwDriverDTB == 0x1AA000) {
            printf("[DTB] Driver returned fallback DTB (0x%llx), attempting physical scan...\n", qwDriverDTB);
        } else {
            printf("[DTB] Driver DTB not available (returned 0x%llx), attempting physical scan...\n", qwDriverDTB);
        }
        
        DTBDetector detector(pDriver);
        s_qwCachedDTB = detector.ScanForDTB();
        
        // If scan also failed, use the driver's fallback value if available
        if (s_qwCachedDTB == 0 && qwDriverDTB == 0x1AA000) {
            printf("[DTB] Physical scan failed, using driver's fallback DTB: 0x%llx\n", qwDriverDTB);
            s_qwCachedDTB = qwDriverDTB;
        }
        
        VMProtectEnd();
        return s_qwCachedDTB;
    }
};

// 导出函数：获取系统DTB
extern "C" QWORD DTBDetector_GetSystemDTB(DriverInterface* pDriver, BOOL bForceRescan) {
    return DTBDetector::GetSystemDTB(pDriver, bForceRescan);
}

// 导出函数：获取内存模型
extern "C" VMM_MEMORYMODEL_TP DTBDetector_GetMemoryModel(void) {
    // 检测系统架构
#ifdef _WIN64
    return VMM_MEMORYMODEL_X64;  // 64位系统
#else
    // 32位系统，需要检测是否PAE
    BOOL fPAE = FALSE;
    typedef BOOL (WINAPI *LPFN_ISWOW64PROCESS2)(HANDLE, PUSHORT, PUSHORT);
    LPFN_ISWOW64PROCESS2 fnIsWow64Process2 = (LPFN_ISWOW64PROCESS2)GetProcAddress(GetModuleHandleA("kernel32"), "IsWow64Process2");
    if(fnIsWow64Process2) {
        USHORT pProcessMachine, pNativeMachine;
        fnIsWow64Process2(GetCurrentProcess(), &pProcessMachine, &pNativeMachine);
        if(pNativeMachine == 0x8664) {  // IMAGE_FILE_MACHINE_AMD64
            return VMM_MEMORYMODEL_X64;
        }
    }
    // 默认假设X86 PAE（现代32位Windows通常启用PAE）
    return VMM_MEMORYMODEL_X86PAE;
#endif
}
