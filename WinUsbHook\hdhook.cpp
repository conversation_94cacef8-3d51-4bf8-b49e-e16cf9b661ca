#include "hdhook.h"
#include "console_ui.h"
#include "sw_mem.h"

// NT_SUCCESS 宏定义
#ifndef NT_SUCCESS
#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)
#endif

// Page Guard Hook 静态成员初始化
std::vector<PageGuardHookInfo> HardBreakHook::pageGuardHooks;
PVOID HardBreakHook::pageGuardExceptionHandle = nullptr;
bool HardBreakHook::pageGuardInitialized = false;

// INT3 Hook 静态成员初始化
std::vector<PageGuardHookInfo> HardBreakHook::int3Hooks;
PVOID HardBreakHook::int3ExceptionHandle = nullptr;
bool HardBreakHook::int3Initialized = false;

// 判断线程是否被忽略
BOOL HardBreakHook::isHookThread(DWORD threadId)
{
	for (DWORD i = 0; i < 0x20; ++i) {
		if (removeThread[i] == threadId) {
			return  FALSE;   // 线程已被忽略
		}
	}
	return TRUE;  // 线程未被忽略
}

// 设置异常处理函数
void  HardBreakHook::setExceptionHandlingFunction(PVECTORED_EXCEPTION_HANDLER func)
{
	//exceptionHandle = AddVectoredContinueHandler(1,func);
	exceptionHandle = AddVectoredExceptionHandler(1, func);
}

// 安装 Hook
void  HardBreakHook::hook()
{
	// 创建系统线程快照
	HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPTHREAD, 0);
	if (hSnapshot != INVALID_HANDLE_VALUE) {
		THREADENTRY32 thread_entry32;
		thread_entry32.dwSize = sizeof(thread_entry32);
		HANDLE hThread = NULL;

		// 枚举所有线程
		if (Thread32First(hSnapshot, &thread_entry32)) {
			do {
				// 判断线程所属进程是否为当前进程
				if (thread_entry32.th32OwnerProcessID == GetCurrentProcessId()) {
					// 判断是否为需要 Hook 的线程
					if (isHookThread(thread_entry32.th32ThreadID)) { // 根据需要决定是否 Hook 每个线程
						// 打开线程句柄
						hThread = OpenThread(THREAD_ALL_ACCESS, FALSE, thread_entry32.th32ThreadID);
						if (hThread) {
							// 挂起线程
							SuspendThread(hThread);

							// 开始 Hook
							CONTEXT context;
							context.ContextFlags = CONTEXT_ALL;
							GetThreadContext(hThread, &context);

							// 设置硬件断点寄存器，为每个已存在的线程设置硬件断点
							context.Dr0 = dr0;
							context.Dr1 = dr1;
							context.Dr2 = dr2;
							context.Dr3 = dr3;
							context.Dr7 = dr7;

							// 设置线程上下文
							SetThreadContext(hThread, &context);

							// 恢复线程
							ResumeThread(hThread);

							// 关闭句柄
							CloseHandle(hThread);
						}
					}
				}
			} while (Thread32Next(hSnapshot, &thread_entry32));
		}

		// 关闭快照
		CloseHandle(hSnapshot);
	}
}

// 卸载 Hook
void  HardBreakHook::unHook()
{
	// 清除硬件断点寄存器
	dr0 = 0;
	dr1 = 0;
	dr2 = 0;
	dr3 = 0;
	dr7 = 0;

	// 将 Hook 的设置应用到目标线程
	hook();

	RemoveVectoredContinueHandler(exceptionHandle);
}

// Page Guard Hook 异常处理函数
LONG WINAPI HardBreakHook::PageGuardExceptionHandler(PEXCEPTION_POINTERS pExceptionInfo)
{
	if (pExceptionInfo->ExceptionRecord->ExceptionCode != STATUS_GUARD_PAGE_VIOLATION) {
		return EXCEPTION_CONTINUE_SEARCH;
	}
	
	PVOID faultAddress = (PVOID)pExceptionInfo->ExceptionRecord->ExceptionInformation[1];
	#ifdef DEBUGLOG
	LOG_INFO("[VEH] Page guard violation at: %p", faultAddress);
	#endif
	
	// 查找对应的 Hook 信息
	PageGuardHookInfo* hookInfo = FindPageGuardHook(faultAddress);
	if (!hookInfo) {
		LOG_WARNING("[VEH] No hook found for this address");
		return EXCEPTION_CONTINUE_SEARCH;
	}
	
	LOG_SUCCESS("[VEH] Found hook for address: %p, redirecting to: %p", hookInfo->targetAddress, hookInfo->hookFunction);
	
	// 临时移除页面保护以避免循环
	DWORD oldProtect;
	Sw3NtProtectVirtualMemory(GetCurrentProcess(), &hookInfo->targetAddress, &hookInfo->pageSize, PAGE_EXECUTE_READWRITE, &oldProtect);
	
	// 修改指令指针，跳转到 Hook 函数
	#ifdef _WIN64
		pExceptionInfo->ContextRecord->Rip = (DWORD64)hookInfo->hookFunction;
	#else
		pExceptionInfo->ContextRecord->Eip = (DWORD)hookInfo->hookFunction;
	#endif
	
	// 设置单步标志，下一条指令执行后恢复页面保护
	pExceptionInfo->ContextRecord->EFlags |= 0x100; // TF (Trap Flag)
	
	return EXCEPTION_CONTINUE_EXECUTION;
}

// 初始化 Page Guard Hook 系统
bool HardBreakHook::InitPageGuardHook()
{
	if (pageGuardInitialized) {
		return true;
	}
	
	pageGuardExceptionHandle = AddVectoredExceptionHandler(1, PageGuardExceptionHandler);
	if (!pageGuardExceptionHandle) {
		LOG_ERROR("[VEH] Failed to add vectored exception handler");
		return false;
	}
	
	pageGuardInitialized = true;
	LOG_SUCCESS("[VEH] Page Guard Hook system initialized");
	return true;
}

// 安装 Page Guard Hook
bool HardBreakHook::InstallPageGuardHook(PVOID targetAddress, PVOID hookFunction, PVOID* originalFunction)
{
	if (!InitPageGuardHook()) {
		return false;
	}
	
	#ifdef DEBUGLOG
	LOG_INFO("[VEH] Installing Page Guard Hook: target=%p, hook=%p", targetAddress, hookFunction);
	#endif
	
	// 检查是否已存在 Hook
	if (FindPageGuardHook(targetAddress)) {
		LOG_WARNING("[VEH] Hook already exists for this address");
		return false;
	}
	
	MEMORY_BASIC_INFORMATION mbi;
	if (!VirtualQuery(targetAddress, &mbi, sizeof(mbi))) {
		LOG_ERROR("[VEH] VirtualQuery failed");
		return false;
	}
	
	PageGuardHookInfo hookInfo = {};
	hookInfo.targetAddress = targetAddress;
	hookInfo.hookFunction = hookFunction;
	hookInfo.originalFunction = originalFunction;
	hookInfo.pageSize = mbi.RegionSize;
	hookInfo.isActive = false;
	
	// 备份原始字节
	memcpy(hookInfo.originalBytes, targetAddress, sizeof(hookInfo.originalBytes));
	
	// 记录原始函数指针
	if (originalFunction) {
		*originalFunction = targetAddress;
	}
	
	// 修改页面保护为 PAGE_GUARD
	PVOID baseAddr = mbi.BaseAddress;
	SIZE_T regionSize = mbi.RegionSize;
	NTSTATUS status = Sw3NtProtectVirtualMemory(GetCurrentProcess(), &baseAddr, &regionSize, mbi.Protect | PAGE_GUARD, &hookInfo.originalProtect);
	if (!NT_SUCCESS(status)) {
		LOG_ERROR("[VEH] Sw3NtProtectVirtualMemory failed, status: 0x%X", status);
		return false;
	}
	
	hookInfo.isActive = true;
	pageGuardHooks.push_back(hookInfo);
	
	LOG_SUCCESS("[VEH] Page Guard Hook installed successfully at %p", targetAddress);
	return true;
}

// 移除 Page Guard Hook
bool HardBreakHook::RemovePageGuardHook(PVOID targetAddress)
{
	for (auto it = pageGuardHooks.begin(); it != pageGuardHooks.end(); ++it) {
		if (it->targetAddress == targetAddress) {
			// 恢复原始字节
			DWORD oldProtect;
			PVOID addr = it->targetAddress;
			SIZE_T size = it->pageSize;
			Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr, &size, it->originalProtect, &oldProtect);
			
			pageGuardHooks.erase(it);
			LOG_SUCCESS("[VEH] Page Guard Hook removed from %p", targetAddress);
			return true;
		}
	}
	
	LOG_WARNING("[VEH] Hook not found for removal");
	return false;
}

// 清理所有 Page Guard Hooks
void HardBreakHook::CleanupPageGuardHooks()
{
	for (auto& hook : pageGuardHooks) {
		if (hook.isActive) {
			DWORD oldProtect;
			PVOID addr = hook.targetAddress;
			SIZE_T size = hook.pageSize;
			Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr, &size, hook.originalProtect, &oldProtect);
		}
	}
	
	pageGuardHooks.clear();
	
	if (pageGuardExceptionHandle) {
		RemoveVectoredExceptionHandler(pageGuardExceptionHandle);
		pageGuardExceptionHandle = nullptr;
	}
	
	pageGuardInitialized = false;
	LOG_SUCCESS("[VEH] Page Guard Hook system cleaned up");
}

// 查找 Page Guard Hook 信息
PageGuardHookInfo* HardBreakHook::FindPageGuardHook(PVOID address)
{
	for (auto& hook : pageGuardHooks) {
		// 判断地址是否在 Hook 的页保护范围内
		ULONG_PTR hookStart = (ULONG_PTR)hook.targetAddress;
		ULONG_PTR hookEnd = hookStart + hook.pageSize;
		ULONG_PTR checkAddr = (ULONG_PTR)address;
		
		if (checkAddr >= hookStart && checkAddr < hookEnd) {
			return &hook;
		}
	}
	
	return nullptr;
}

// INT3 Hook 异常处理函数
LONG WINAPI HardBreakHook::Int3ExceptionHandler(PEXCEPTION_POINTERS pExceptionInfo)
{
	if (pExceptionInfo->ExceptionRecord->ExceptionCode != EXCEPTION_BREAKPOINT) {
		return EXCEPTION_CONTINUE_SEARCH;
	}
	
	PVOID faultAddress = (PVOID)pExceptionInfo->ExceptionRecord->ExceptionAddress;
	#ifdef DEBUGLOG
	LOG_INFO("[INT3] Breakpoint exception at: %p", faultAddress);
	#endif
	
	// 查找对应的 Hook 信息
	PageGuardHookInfo* hookInfo = FindInt3Hook(faultAddress);
	if (!hookInfo) {
		LOG_WARNING("[INT3] No hook found for this address");
		return EXCEPTION_CONTINUE_SEARCH;
	}
	
	LOG_SUCCESS("[INT3] Found hook for address: %p, redirecting to: %p", hookInfo->targetAddress, hookInfo->hookFunction);
	
	// 恢复原始字节
	DWORD oldProtect;
	PVOID addr = hookInfo->targetAddress;
	SIZE_T size = 1;
	NTSTATUS status = Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr, &size, PAGE_EXECUTE_READWRITE, &oldProtect);
	if (!NT_SUCCESS(status)) {
		LOG_ERROR("[INT3] Sw3NtProtectVirtualMemory failed: 0x%X", status);
		return EXCEPTION_CONTINUE_SEARCH;
	}
	
	// 恢复内存保护
	*(PBYTE)hookInfo->targetAddress = hookInfo->originalBytes[0];
	
	// 恢复内存保护
	addr = hookInfo->targetAddress;
	size = 1;
	Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr, &size, oldProtect, &oldProtect);
	
	// 修改指令指针，跳转到 Hook 函数
	#ifdef _WIN64
		pExceptionInfo->ContextRecord->Rip = (DWORD64)hookInfo->hookFunction;
	#else
		pExceptionInfo->ContextRecord->Eip = (DWORD)hookInfo->hookFunction;
	#endif
	
	return EXCEPTION_CONTINUE_EXECUTION;
}

// 初始化 INT3 Hook 系统
bool HardBreakHook::InitInt3Hook()
{
	if (int3Initialized) {
		return true;
	}
	
	int3ExceptionHandle = AddVectoredExceptionHandler(1, Int3ExceptionHandler);
	if (!int3ExceptionHandle) {
		LOG_ERROR("[INT3] Failed to add vectored exception handler");
		return false;
	}
	
	int3Initialized = true;
	LOG_SUCCESS("[INT3] INT3 Hook system initialized");
	return true;
}

// 安装 INT3 Hook
bool HardBreakHook::InstallInt3Hook(PVOID targetAddress, PVOID hookFunction, PVOID* originalFunction)
{
	if (!InitInt3Hook()) {
		return false;
	}
	
	#ifdef DEBUGLOG
	LOG_INFO("[INT3] Installing INT3 Hook: target=%p, hook=%p", targetAddress, hookFunction);
	#endif
	
	// 检查是否已存在 Hook
	if (FindInt3Hook(targetAddress)) {
		LOG_WARNING("[INT3] Hook already exists for this address");
		return false;
	}
	
	PageGuardHookInfo hookInfo = {};
	hookInfo.targetAddress = targetAddress;
	hookInfo.hookFunction = hookFunction;
	hookInfo.originalFunction = originalFunction;
	hookInfo.pageSize = 1; // INT3 只需要 1 字节
	hookInfo.isActive = false;
	
	// 备份原始字节
	hookInfo.originalBytes[0] = *(PBYTE)targetAddress;
	
	// 记录原始函数指针
	if (originalFunction) {
		*originalFunction = targetAddress;
	}
	
	// 修改内存保护
	DWORD oldProtect;
	PVOID addr = targetAddress;
	SIZE_T size = 1;
	NTSTATUS status = Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr, &size, PAGE_EXECUTE_READWRITE, &oldProtect);
	if (!NT_SUCCESS(status)) {
		LOG_ERROR("[INT3] Sw3NtProtectVirtualMemory failed, status: 0x%X", status);
		return false;
	}
	
	hookInfo.originalProtect = oldProtect;
	
	// 写入 INT3 断点
	*(PBYTE)targetAddress = 0xCC;
	
	// 恢复内存保护
	addr = targetAddress;
	size = 1;
	Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr, &size, oldProtect, &oldProtect);
	
	hookInfo.isActive = true;
	int3Hooks.push_back(hookInfo);
	
	LOG_SUCCESS("[INT3] INT3 Hook installed successfully at %p", targetAddress);
	return true;
}

// 移除 INT3 Hook
bool HardBreakHook::RemoveInt3Hook(PVOID targetAddress)
{
	for (auto it = int3Hooks.begin(); it != int3Hooks.end(); ++it) {
		if (it->targetAddress == targetAddress) {
			// 恢复原始字节
			DWORD oldProtect;
			PVOID addr = it->targetAddress;
			SIZE_T size = 1;
			Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr, &size, PAGE_EXECUTE_READWRITE, &oldProtect);
			*(PBYTE)it->targetAddress = it->originalBytes[0];
			addr = it->targetAddress;
			size = 1;
			Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr, &size, it->originalProtect, &oldProtect);
			
			int3Hooks.erase(it);
			LOG_SUCCESS("[INT3] INT3 Hook removed from %p", targetAddress);
			return true;
		}
	}
	
	LOG_WARNING("[INT3] Hook not found for removal");
	return false;
}

// 清理所有 INT3 Hooks
void HardBreakHook::CleanupInt3Hooks()
{
	for (auto& hook : int3Hooks) {
		if (hook.isActive) {
			DWORD oldProtect;
			PVOID addr = hook.targetAddress;
			SIZE_T size = 1;
			Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr, &size, PAGE_EXECUTE_READWRITE, &oldProtect);
			*(PBYTE)hook.targetAddress = hook.originalBytes[0];
			addr = hook.targetAddress;
			size = 1;
			Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr, &size, hook.originalProtect, &oldProtect);
		}
	}
	
	int3Hooks.clear();
	
	if (int3ExceptionHandle) {
		RemoveVectoredExceptionHandler(int3ExceptionHandle);
		int3ExceptionHandle = nullptr;
	}
	
	int3Initialized = false;
	LOG_SUCCESS("[INT3] INT3 Hook system cleaned up");
}

// 查找 INT3 Hook 信息
PageGuardHookInfo* HardBreakHook::FindInt3Hook(PVOID address)
{
	for (auto& hook : int3Hooks) {
		if (hook.targetAddress == address) {
			return &hook;
		}
	}
	
	return nullptr;
}