#ifndef __DTB_DETECTOR_H__
#define __DTB_DETECTOR_H__

#include <Windows.h>

// Forward declaration to avoid including C++ header in C translation units
#include "Common.h"
struct DriverInterface;

#ifdef __cplusplus
extern "C" {
#endif

// LC_OPT_MEMORYINFO_* option definitions (from leechcore.h)
#ifndef LC_OPT_MEMORYINFO_OS_DTB
#define LC_OPT_MEMORYINFO_OS_DTB                    0x0200000700000000  // R
#define LC_OPT_MEMORYINFO_ARCH                      0x0200000100000000  // R
#define LC_OPT_MEMORYINFO_OS_KERNELBASE             0x0200000400000000  // R
#define LC_OPT_MEMORYINFO_OS_KERNELHINT             0x0200000500000000  // R
#define LC_OPT_MEMORYINFO_OS_VERSIONMAJOR           0x0200000800000000  // R
#define LC_OPT_MEMORYINFO_OS_VERSIONMINOR           0x0200000900000000  // R
#define LC_OPT_MEMORYINFO_OS_VERSIONBUILD           0x0200000A00000000  // R
#endif

// Memory model types
typedef enum tdVMM_MEMORYMODEL_TP {
    VMM_MEMORYMODEL_NA          = 0,
    VMM_MEMORYMODEL_X64         = 1,
    VMM_MEMORYMODEL_X86PAE      = 2,
    VMM_MEMORYMODEL_X86         = 3,
    VMM_MEMORYMODEL_ARM64       = 4,
} VMM_MEMORYMODEL_TP;

/*
* Get the system DTB (Directory Table Base).
* This function will scan physical memory to find the system DTB.
* The result is cached for subsequent calls unless bForceRescan is TRUE.
* -- pDriver: pointer to the driver interface
* -- bForceRescan: if TRUE, force a new scan even if DTB is cached
* -- return: DTB address, or 0 if not found
*/
QWORD DTBDetector_GetSystemDTB(struct DriverInterface* pDriver, BOOL bForceRescan);

/*
* Get the system memory model (architecture).
* -- return: memory model type
*/
VMM_MEMORYMODEL_TP DTBDetector_GetMemoryModel(void);

#ifdef __cplusplus
}
#endif

#endif /* __DTB_DETECTOR_H__ */
