// DTBCleanup.h : DTB cleanup and override functionality header
// This file declares functions for DTB value cleaning and command-line override support

#pragma once

#include <Windows.h>

// Forward declarations
struct DriverInterface;

#ifdef __cplusplus
extern "C" {
#endif

//-----------------------------------------------------------------------------
// DTB CLEANUP FUNCTIONS
//-----------------------------------------------------------------------------

// Clean DTB value by removing KVA shadow bit (bit 63) and PCID bits (bits 11-0)
// This is the full cleanup that removes all non-address bits
QWORD DTBCleanup_CleanDTB(QWORD qwRawDTB);

// Minimal cleanup: only clear PCID bits (preserve KVA shadow)
// Use this if the full cleanup causes issues
QWORD DTBCleanup_CleanDTBMinimal(QWORD qwRawDTB);

//-----------------------------------------------------------------------------
// DTB OVERRIDE FUNCTIONS
//-----------------------------------------------------------------------------

// Parse command line arguments for --dtb parameter
// Usage: CustomLeechServer.exe --dtb 0x1AA000
// Returns TRUE if --dtb was found and parsed successfully
BOOL DTBCleanup_ParseCommandLine(int argc, char* argv[]);

// Get the DTB override value if set via command line
// Returns 0 if no override is set
QWORD DTBCleanup_GetOverrideDTB();

// Check if a DTB override is currently active
BOOL DTBCleanup_HasOverride();

//-----------------------------------------------------------------------------
// DTB COMPARISON AND LOGGING
//-----------------------------------------------------------------------------

// Log DTB values for comparison between driver and MemProcFS
// This helps diagnose DTB mismatch issues
VOID DTBCleanup_LogDTBComparison(QWORD qwDriverDTB, QWORD qwMemProcFSDTB);

// Get the final DTB value to use (with override and cleanup applied)
// This is the main function to call when you need a DTB value
QWORD DTBCleanup_GetFinalDTB(struct DriverInterface* pDriver);

//-----------------------------------------------------------------------------
// DIAGNOSTIC FUNCTIONS
//-----------------------------------------------------------------------------

// Print detailed DTB diagnostic information
// Shows raw values, cleaned values, binary representation, etc.
VOID DTBCleanup_PrintDiagnostics();

// Run DTB cleanup tests with various test values
// Useful for verifying the cleanup logic works correctly
VOID DTBCleanup_RunTests();

#ifdef __cplusplus
}
#endif
