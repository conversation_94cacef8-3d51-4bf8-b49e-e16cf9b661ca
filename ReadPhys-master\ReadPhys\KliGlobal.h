#pragma once
#include <ntifs.h>
#include <ntstrsafe.h>
#include "kli.hpp"

// ȫ��KLI����ָ�붨�� - ͳһ��������KLI���溯��
// entry.cppʹ�õĺ���
KLI_CACHED_DEF(KeGetCurrentIrql);
KLI_CACHED_DEF(DbgPrintEx);
KLI_CACHED_DEF(ExAllocatePool2);
KLI_CACHED_DEF(ExAllocatePoolWithTag);
KLI_CACHED_DEF(ExFreePool);
KLI_CACHED_DEF(ExFreePoolWithTag);
KLI_CACHED_DEF(IoCreateDevice);
KLI_CACHED_DEF(IoCreateSymbolicLink);
KLI_CACHED_DEF(IoDeleteDevice);
KLI_CACHED_DEF(IoDeleteSymbolicLink);
KLI_CACHED_DEF(IofCompleteRequest);
KLI_CACHED_DEF(RtlInitUnicodeString);
// KLI_CACHED_DEF(RtlStringCchPrintfW); // �Ƴ� - �˺�����ntoskrnl��δ����
KLI_CACHED_DEF(KeQueryTimeIncrement);
KLI_CACHED_DEF(KeStackAttachProcess);
KLI_CACHED_DEF(KeUnstackDetachProcess);
KLI_CACHED_DEF(PsInitialSystemProcess);
KLI_CACHED_DEF(PsLookupProcessByProcessId);
KLI_CACHED_DEF(ObfDereferenceObject);
KLI_CACHED_DEF(PsGetProcessId);
// KLI_CACHED_DEF(PsGetProcessInheritedFromUniqueProcessId); // 此函数在某些Windows版本中不可用
// KLI_CACHED_DEF(PsGetProcessImageFileName); // 此函数需要特殊处理
// KLI_CACHED_DEF(PsGetProcessSessionId); // 此函数在某些Windows版本中不可用
KLI_CACHED_DEF(MmCopyMemory);

KLI_CACHED_DEF(_vsnwprintf);  // ����_vsnwprintf

// Anti4heatExpertʹ�õĺ���
KLI_CACHED_DEF(MmGetPhysicalMemoryRanges);
KLI_CACHED_DEF(MmGetPhysicalAddress);
KLI_CACHED_DEF(MmGetVirtualForPhysical);
KLI_CACHED_DEF(MmAllocateMappingAddress);
KLI_CACHED_DEF(MmFreeMappingAddress);

// ����ʵ�ֵ�RtlStringCchPrintfW����
NTSTATUS MyRtlStringCchPrintfW(PWSTR pszDest, size_t cchDest, PCWSTR pszFormat, ...);

// ͳһ��KLI��ʼ����������
void InitializeAllKliCache();

#ifdef __cplusplus
extern "C" {
#endif

    /* MyWcslen
     *   �ں˰�ȫ�� wcslen
     *   psz : �� NUL ��β�Ŀ��ַ���
     *   �����ַ����������� NUL��
     */
    __forceinline SIZE_T MyWcslen(_In_z_ PCWSTR psz)
    {
        PCWSTR p = psz;

        /* ��ָ��ֱ������ʱ���ܼ�⣬�����Ϊ return 0 */
        NT_ASSERT(psz != NULL);

        /* �򵥸�Ч�� 16-bit �ַ������ȼ��� */
        while (*p != L'\0')
            ++p;

        return (SIZE_T)(p - psz);
    }

    /* MyRtlStringCbLengthW ���ȼ����װ
     *  ���ֻ��Ҫ�ֽڳ��ȣ�����ʹ�������
     *  psz : �� NUL ��β�Ŀ��ַ���
     *  pcb : ����ֽ�����������ֹ L'\0'��
     */
#define MyStringCbLength(psz, pcb)                                \
    do {                                                          \
        *(pcb) = (MyWcslen(psz) + 1) * sizeof(WCHAR);             \
    } while (0,0)

#ifdef __cplusplus
}
#endif
