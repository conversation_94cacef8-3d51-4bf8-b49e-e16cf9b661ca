# ReadPhys 驱动修复说明

## 已解决的问题

### 1. 移除 __chkstk 导入函数

**问题**: 编译后的驱动仍然导入 `__chkstk` 函数

**解决方案**:
- 增加栈保护大小到 `/Gs65536`
- 添加 `/NODEFAULTLIB:libcmt.lib /NODEFAULTLIB:libvcruntime.lib` 避免链接CRT
- 添加 `/FORCE:UNRESOLVED` 允许未解析的符号
- 在代码中提供自定义的空 `__chkstk` 实现

**修改文件**:
- `ReadPhys.vcxproj`: 更新编译和链接选项
- `entry.cpp`: 添加自定义 `__chkstk` 函数

### 2. 移除异常目录

**问题**: 编译后的驱动仍然包含异常目录

**解决方案**:
- 添加 `/EHs-c-` 完全禁用C++异常处理
- 添加 `/DISCARD:.pdata` 丢弃异常处理数据段
- 设置 `ExceptionHandling=false`
- 添加 `extern "C" int _fltused = 0;` 禁用浮点支持

**修改文件**:
- `ReadPhys.vcxproj`: 更新编译选项
- `entry.cpp`: 添加禁用符号

### 3. 改进设备名称生成方法

**问题**: 随机GUID导致R3程序无法预知设备名称

**解决方案**:
- 移除随机数生成器
- 实现基于固定种子的哈希算法
- R3程序可以使用相同算法计算设备名称
- 保持GUID格式的外观，但内容可预测

**新增文件**:
- `DeviceNameGenerator.h`: R3程序使用的设备名称生成算法
- `R3TestClient/main.cpp`: R3测试客户端示例
- `R3TestClient/build.bat`: 编译脚本

**修改文件**:
- `entry.cpp`: 替换随机数生成为可预测算法

## 技术细节

### 设备名称算法

驱动和R3程序都使用相同的算法：

```cpp
// 哈希函数
ULONG SimpleHash(const char* str, ULONG seed)
{
    ULONG hash = seed;
    while (*str) {
        hash = hash * 33 + (ULONG)(*str);
        str++;
    }
    return hash;
}

// 生成GUID组件
void GeneratePredictableGUID(ULONG* part1, ULONG* part2, ULONG* part3, ULONG* part4)
{
    const char* seed1 = "ReadPhysDevice2024";
    const char* seed2 = "DMAController";
    const char* seed3 = "PhysicalMemory";
    const char* seed4 = "KernelDriver";
    
    *part1 = SimpleHash(seed1, 0x12345678);
    *part2 = SimpleHash(seed2, 0x87654321);
    *part3 = SimpleHash(seed3, 0xABCDEF00);
    *part4 = SimpleHash(seed4, 0x00FEDCBA);
}
```

### R3程序使用方法

```cpp
#include "DeviceNameGenerator.h"

// 获取设备名称
std::wstring deviceName = DeviceNameGenerator::GenerateDeviceName();

// 打开设备
HANDLE hDevice = CreateFileW(deviceName.c_str(), ...);
```

## 编译说明

1. **驱动编译**: 使用现有的项目文件，所有修改已集成
2. **R3客户端编译**: 进入 `R3TestClient` 目录，运行 `build.bat`

## 验证方法

1. 编译并加载驱动
2. 编译并运行R3测试客户端
3. 客户端应该能够：
   - 计算出正确的设备名称
   - 成功打开设备
   - 通过设备识别测试
   - 执行基本的IOCTL操作

## 预期结果

- ✅ 驱动无任何导入函数
- ✅ 驱动无异常目录和重定位目录  
- ✅ R3程序可以可靠地找到并访问设备
- ✅ 字符串仍然被混淆（通过OLLVM）
- ✅ 保持驱动的隐蔽性和功能性
