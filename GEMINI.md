# 项目概述：MemProcFS 生态系统

该目录包含 MemProcFS 生态系统的源代码和相关项目，这是一套功能强大的物理内存分析工具。核心项目包括：

1.  **MemProcFS：** 主要应用程序，将物理内存呈现为虚拟文件系统，从而可以使用标准文件系统工具和自定义 API 轻松访问内存内容和工件。它专为内存转储分析和实时内存采集（包括通过 PCILeech FPGA 等硬件进行读写模式）而设计。
2.  **LeechCore：** MemProcFS（和 PCILeech）用于物理内存采集的底层库。它提供了用于访问各种内存源的 API，包括文件转储、实时系统驱动程序（WinPMEM、DumpIt）、虚拟机自省（VMware、QEMU）和 PCILeech FPGA 等硬件设备。它还通过 LeechAgent 支持远程内存访问。
3.  **ReadPhys：** 一个演示如何通过手动映射页表条目 (PTE) 来读取物理内存的项目，而不使用标准的读/写 API。
4.  **CustomLeechServer：** LeechCore 或相关远程访问的自定义服务器实现。
5.  **God_Driver：** 一个驱动程序项目，可能与内存访问或系统交互有关。
6.  **WinUsbHook：** 一个用于与 USB 设备交互的实用程序，可能与 FPGA 硬件有关。

## 关键技术

*   **C/C++：** MemProcFS、LeechCore 和相关驱动程序/实用程序的主要语言。
*   **Python、C#、Java、Go、Rust：** 为这些语言提供了 API，以便将 MemProcFS/LeechCore 功能集成到其他项目中。
*   **Dokany/macFUSE/FUSE：** MemProcFS 用于在 Windows、macOS 和 Linux 上挂载虚拟文件系统的文件系统库。
*   **硬件 (FPGA)：** PCILeech FPGA 硬件是高速、硬件加速的直接内存访问 (DMA) 攻击和实时内存采集的关键组件。

## 构建和运行

该目录中的项目使用 Visual Studio 和 CMake 构建。

*   **Visual Studio：** `CustomLeechServer.sln` 和 `ReadPhys.sln` 文件可用于构建 `CustomLeechServer`、`WinUsbHook` 和 `ReadPhys` 项目。
*   **CMake：** `libleechgrpc-master` 和 `MemProcFS` 项目使用 CMake 进行构建。有关详细信息，请参阅相应目录中的 `CMakeLists.txt` 文件。

## 关键组件

*   **ReadPhys：** 该驱动程序提供了一种读取物理内存的方法。它经过修改，移除了对 C 运行时库的依赖，并使用可预测的设备名称生成算法。
*   **God_Driver：** 这是一个用于物理内存访问的内核驱动程序。
*   **WinUsbHook：** 该项目似乎与挂钩 WinUSB 函数有关，可能用于与自定义硬件交互。`winusb.def` 文件为空，表明它可能是动态链接到 `winusb.dll`。
*   **CustomLeechServer：** 这是一个使用 LeechCore 库提供对物理内存的远程访问的服务器。它通过 RPC 与客户端通信。

## DTB采集

`DTB_SOLUTION.md` 文件解释了如何获取目录表基址 (DTB)。VMM (MemProcFS) 可以通过三种方式获取 DTB：

1.  来自用户提供的参数。
2.  来自 LeechCore 设备。
3.  通过自动扫描内存查找 DTB。

该文档建议让 VMM 自动检测 DTB，这需要物理内存读取功能正常工作。

## 开发约定

*   **开源，封闭贡献：** 核心项目（MemProcFS、LeechCore、PCILeech）是开源的，但不接受对核心代码库的直接贡献。鼓励通过插件架构进行扩展和开发新功能。
*   **插件友好：** 特别强调允许用户通过用 C/C++、Rust 或 Python 编写的插件来扩展功能。

## 链接

*   **MemProcFS GitHub：** https://github.com/ufrisk/MemProcFS
*   **LeechCore GitHub：** https://github.com/ufrisk/LeechCore
*   **PCILeech GitHub：** https://github.com/ufrisk/pcileech
*   **PCILeech/MemProcFS Discord：** https://discord.gg/pcileech