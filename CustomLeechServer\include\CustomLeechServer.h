#pragma once

#include "Common.h"
#include "DriverInterface.h"
#include "SessionManager.h"
#include "leechrpc.h"

#include "VMP.h"

// Note: Using MS-RPC only, no gRPC dependencies needed

// CustomLeechServer class - main MS-RPC server implementation
class CustomLeechServer {
private:
    BOOL fRpcServerStarted;
    DriverInterface* pDriverInterface;
    SessionManager* pSessionManager;
    CUSTOM_SERVER_CONFIG config;
    BOOL fInitialized;
    BOOL fRunning;
    HANDLE hServerThread;
    DWORD dwServerThreadId;

    // Private methods
    BOOL InitializeRpcServer();
    BOOL CheckPortAvailability(DWORD dwPort);
    VOID CleanupRpcServer();
    static DWORD WINAPI RpcServerThread(LPVOID lpParam);

public:
    // Constructor/Destructor
    CustomLeechServer();
    ~CustomLeechServer();

    // Main interface
    BOOL Initialize(LPCSTR szListenAddress, DWORD dwPort, BO<PERSON> fInsecure, LPCSTR szDriverName);
    BOOL Start();
    VOID Stop();
    VOID Shutdown();

    // RPC command handler (will be implemented by IDL-generated code)
    // The actual RPC functions are implemented in leechrpcserver.c

    // Configuration methods removed - using fixed configuration

    // Accessors
    DriverInterface* GetDriverInterface() { return pDriverInterface; }
    SessionManager* GetSessionManager() { return pSessionManager; }
    
    // Singleton access
    static CustomLeechServer* GetInstance();
};

// Global C function to access driver interface from C code
extern "C" DriverInterface* GetGlobalDriverInterface();
