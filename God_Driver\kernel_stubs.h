// Minimal stubs for linter only. Real build must use WDK and ntddk.h
#pragma once
#include <stdint.h>
#include <stddef.h>

typedef unsigned char  UCHAR;
typedef unsigned char* PUCHAR;
typedef unsigned short USHORT;
typedef unsigned long  ULONG;
typedef unsigned long long ULONGLONG;
typedef long NTSTATUS;
typedef uint64_t SIZE_T;
typedef struct _PHYSICAL_ADDRESS { long long QuadPart; } PHYSICAL_ADDRESS;
typedef void* PVOID;
typedef unsigned long long ULONG_PTR;
typedef long long LONGLONG;
typedef unsigned long DWORD;
typedef void* HANDLE;
typedef HANDLE* PHANDLE;
typedef unsigned long ULONG32;
typedef unsigned long long QWORD64;

#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)
#define STATUS_SUCCESS 0
#define STATUS_INVALID_PARAMETER ((NTSTATUS)0xC000000DL)
#define STATUS_BUFFER_TOO_SMALL  ((NTSTATUS)0xC0000023L)
#define STATUS_INSUFFICIENT_RESOURCES ((NTSTATUS)0xC000009AL)

typedef struct _DEVICE_OBJECT DEVICE_OBJECT, *PDEVICE_OBJECT;
typedef struct _UNICODE_STRING { USHORT Length; USHORT MaximumLength; wchar_t* Buffer; } UNICODE_STRING, *PUNICODE_STRING;
typedef struct _IO_STATUS_BLOCK { NTSTATUS Status; unsigned long Information; } IO_STATUS_BLOCK, *PIO_STATUS_BLOCK;
typedef union _IRP_ASSOCIATED_UNION { void* SystemBuffer; } IRP_ASSOCIATED_UNION;
typedef struct _IRP { IO_STATUS_BLOCK IoStatus; IRP_ASSOCIATED_UNION AssociatedIrp; } IRP, *PIRP;
typedef struct _DRIVER_OBJECT { void* MajorFunction[0x1b]; void* DriverUnload; } DRIVER_OBJECT, *PDRIVER_OBJECT;
typedef struct _IO_STACK_LOCATION {
  union { struct { unsigned long IoControlCode; unsigned long InputBufferLength; unsigned long OutputBufferLength; } DeviceIoControl; } Parameters;
} IO_STACK_LOCATION, *PIO_STACK_LOCATION;

#define IO_NO_INCREMENT 0
#define FILE_DEVICE_UNKNOWN 0x00000022

#define _Dispatch_type_(x)

typedef NTSTATUS (*PDRIVER_DISPATCH)(PDEVICE_OBJECT, PIRP);
typedef void (*PDRIVER_UNLOAD_FN)(PDRIVER_OBJECT);
typedef NTSTATUS (*PDRIVER_INITIALIZE_FN)(PDRIVER_OBJECT, PUNICODE_STRING);

// Keep names for annotation only
typedef NTSTATUS DRIVER_INITIALIZE;
typedef void     DRIVER_UNLOAD_TYPE;
typedef NTSTATUS DRIVER_DISPATCH_TYPE;

static __inline void RtlInitUnicodeString(PUNICODE_STRING u, const wchar_t* s){ if(!u) return; u->Buffer=(wchar_t*)s; if(s){ size_t n=0; while(s[n]) n++; u->Length=(USHORT)(n*2); u->MaximumLength=(USHORT)((n+1)*2);} else {u->Length=u->MaximumLength=0;} }

// Decls only (no impls for lint)
NTSTATUS IoCreateDevice(PDRIVER_OBJECT, ULONG, PUNICODE_STRING, ULONG, ULONG, int, PDEVICE_OBJECT*);
NTSTATUS IoCreateSymbolicLink(PUNICODE_STRING, PUNICODE_STRING);
void IoDeleteSymbolicLink(PUNICODE_STRING);
void IoDeleteDevice(PDEVICE_OBJECT);
void IoCompleteRequest(PIRP, int);
PIO_STACK_LOCATION IoGetCurrentIrpStackLocation(PIRP);
void* MmMapIoSpace(PHYSICAL_ADDRESS, SIZE_T, int);
void MmUnmapIoSpace(void*, SIZE_T);
void RtlCopyMemory(void* D, const void* S, SIZE_T N);
void* __cdecl memset(void*, int, size_t);

#define UNREFERENCED_PARAMETER(P) (void)(P)
#define STATUS_INVALID_DEVICE_REQUEST ((NTSTATUS)0xC0000010L)
static __inline NTSTATUS GetExceptionCode(void){return (NTSTATUS)0xC0000005L;}

#define PAGE_SIZE 0x1000
#define MmCached 0
#define FALSE 0
#define TRUE 1

// Major codes
#define IRP_MJ_CREATE 0x00
#define IRP_MJ_CLOSE  0x02
#define IRP_MJ_DEVICE_CONTROL 0x0E

// SEH compatibility for lint only (no real exception handling)
#ifndef __try
#define __try if (1)
#endif
#ifndef __except
#define __except(x) else if (0)
#endif
#ifndef EXCEPTION_EXECUTE_HANDLER
#define EXCEPTION_EXECUTE_HANDLER 1
#endif


