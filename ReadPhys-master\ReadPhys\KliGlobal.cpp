#include "KliGlobal.h"
#include <ntifs.h>
#include <stdarg.h>

// ��֤����ָ���Ƿ���Ч
BOOLEAN IsValidKernelPointer(PVOID ptr)
{
    if (!ptr || ptr == (PVOID)-1 || (ULONG_PTR)ptr < 0xFFFF800000000000ULL)
        return FALSE;
    
    // �򵥵��ں˵�ַ��Χ���
    if ((ULONG_PTR)ptr >= 0xFFFF800000000000ULL && (ULONG_PTR)ptr < 0xFFFFFFFFF0000000ULL)
        return TRUE;
    
    return FALSE;
}

// ��ȫ��KLI�������ú�
#define KLI_SAFE_SET(name) do { \
    PVOID funcPtr = (PVOID)(kli::crypto::RC4Func(kli::find_kernel_export(KLI_HASH_STR(#name)))); \
    if (IsValidKernelPointer(funcPtr)) { \
        KLI##name = (decltype(&name))funcPtr; \
    } else { \
        KLI##name = nullptr; \
    } \
} while(0)

// ͳһ��KLI�����ʼ������
void InitializeAllKliCache()
{
    // ��ʼ��entry.cppʹ�õĺ���
    KLI_SAFE_SET(KeGetCurrentIrql);
    KLI_SAFE_SET(DbgPrintEx);
    KLI_SAFE_SET(ExAllocatePool2);
    KLI_SAFE_SET(ExAllocatePoolWithTag);
    KLI_SAFE_SET(ExFreePool);
    KLI_SAFE_SET(ExFreePoolWithTag);
    KLI_SAFE_SET(IoCreateDevice);
    KLI_SAFE_SET(IoCreateSymbolicLink);
    KLI_SAFE_SET(IoDeleteDevice);
    KLI_SAFE_SET(IoDeleteSymbolicLink);
    KLI_SAFE_SET(IofCompleteRequest);
    KLI_SAFE_SET(RtlInitUnicodeString);
    // KLI_SAFE_SET(RtlStringCchPrintfW); // �Ƴ� - �˺�����ntoskrnl��δ����
    KLI_SAFE_SET(KeQueryTimeIncrement);
    KLI_SAFE_SET(KeStackAttachProcess);
    KLI_SAFE_SET(KeUnstackDetachProcess);
    // ����������غ�����
    KLI_SAFE_SET(PsLookupProcessByProcessId);
    // PsInitialSystemProcess Ϊ���ݷ��ţ�ͨ������ͨ �� KLI ���������ﲻǿ�����á�
    KLI_SAFE_SET(ObfDereferenceObject);
    KLI_SAFE_SET(PsGetProcessId);
    // KLI_SAFE_SET(PsGetProcessInheritedFromUniqueProcessId); // 此函数在某些Windows版本中不可用
    // KLI_SAFE_SET(PsGetProcessImageFileName); // 此函数需要特殊处理
    // KLI_SAFE_SET(PsGetProcessSessionId); // 此函数在某些Windows版本中不可用
    KLI_SAFE_SET(MmCopyMemory);

    KLI_SAFE_SET(_vsnwprintf);  // ����_vsnwprintf��ʼ��

    // ��ʼ��Anti4heatExpertʹ�õĺ���
    KLI_SAFE_SET(MmGetPhysicalMemoryRanges);
    KLI_SAFE_SET(MmGetPhysicalAddress);
    KLI_SAFE_SET(MmGetVirtualForPhysical);
    KLI_SAFE_SET(MmAllocateMappingAddress);
    KLI_SAFE_SET(MmFreeMappingAddress);
}

// ����ʵ�ֵ�RtlStringCchPrintfW����
// �������ṩ���ں�ʵ���߼�
NTSTATUS MyRtlStringCchPrintfW(PWSTR pszDest, size_t cchDest, PCWSTR pszFormat, ...)
{
    va_list Args;
    NTSTATUS status;
    int result;
    size_t maxLength;

    va_start(Args, pszFormat);

    // ��黺������С�Ƿ���Ч
    if (cchDest - 1 > 0x7FFFFFFE) {
        status = STATUS_INVALID_PARAMETER; // -1073741811 = 0xC000000D
        if (cchDest && pszDest) {
            *pszDest = L'\0';
        }
        goto cleanup;
    }

    // ��������Ч��
    if (!pszDest || !pszFormat || cchDest == 0) {
        status = STATUS_INVALID_PARAMETER;
        goto cleanup;
    }

    maxLength = cchDest - 1;
    status = STATUS_SUCCESS;

    // ʹ��KLI�����_vsnwprintf����
    if (KLI_vsnwprintf) {
        result = KLI_CACHED_CALL(_vsnwprintf, pszDest, maxLength, pszFormat, Args);
    } else {
        // ���KLI���������ã����ش���
        status = STATUS_NOT_SUPPORTED;
        goto cleanup;
    }

    // �����
    if (result < 0 || (size_t)result > maxLength) {
        status = STATUS_BUFFER_OVERFLOW; // 0x80000005
        // ȷ���ַ�����null��β
        pszDest[maxLength] = L'\0';
    } else if ((size_t)result == maxLength) {
        // �������պ�������ȷ��null��ֹ
        pszDest[maxLength] = L'\0';
    }
    // ���result < maxLength��_vsnwprintf�Ѿ�������null��ֹ��

cleanup:
    va_end(Args);
    return status;
}
