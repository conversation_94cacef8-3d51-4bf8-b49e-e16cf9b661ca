# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 构建和开发命令

### Visual Studio 构建
主要项目使用 Visual Studio 2022 构建系统：

- **构建所有项目**: 打开 `CustomLeechServer\CustomLeechServer.sln`，选择 Release|x64 配置进行构建
- **单独构建 LeechCore**: 进入 `LeechCore\` 目录，打开 `LeechCore.sln`
- **单独构建 MemProcFS**: 进入 `MemProcFS\` 目录，打开 `MemProcFS.sln`  
- **ReadPhys 驱动构建**: 进入 `ReadPhys-master\` 目录，打开 `ReadPhys.sln`

### Linux 构建
对于支持 Linux 的组件，使用 Makefile：

- **构建 LeechCore**: `cd LeechCore/leechcore && make`
- **构建 MemProcFS**: `cd MemProcFS/vmm && make`
- **构建 LeechAgent**: `cd LeechCore/leechagent_linux && make`

## 项目架构概览

这是一个多层次的内存分析和DMA工具集合，专门设计用于将物理 DMA 硬件连接转换为网络 RPC 连接。

### 核心组件

**ReadPhys** - 自定义物理内存读取驱动 (最底层)
- 内核级直接物理内存访问，绕过传统内存 API
- 手动 PTE 映射，不依赖 MmCopyMemory
- 支持散读（scatter read）提高性能
- 提供 DTB 获取、内核基址探测等功能
- 反检测机制，规避安全软件
- 使用 KLI 框架包装所有内核 API 调用

**CustomLeechServer** - 基于 MS-RPC 的 LeechCore 服务端
- 将本地 ReadPhys 驱动访问转换为网络 RPC 服务
- 会话管理和客户端认证
- 基于 Windows RPC 的高性能网络传输
- DTB (Directory Table Base) 检测和处理
- 支持握手协议自动发现（端口 28474）
- 与 LeechCore 协议完全兼容

**WinUsbHook** - USB 设备钩子系统 (应用层注入)
- 拦截目标软件的 VMMDLL_Initialize 调用
- 将本地 FPGA 设备参数重定向为网络 RPC 连接
- 多种 Hook 技术 (硬件断点、Page Guard、INT3、内联 detour)
- MinHook 库集成，异常处理机制
- 软件检测和反检测功能
- 支持参数重写和缓存机制

**LeechCore** - 物理内存获取库 (兼容层)
- 提供统一的内存获取 API (C/C++, Python, C#)
- 支持多种硬件设备和网络连接
- 为上层软件提供标准接口

**MemProcFS** - 内存进程文件系统 (分析层)
- 将物理内存虚拟化为文件系统视图
- 支持实时内存分析和内存转储文件分析
- 丰富的取证分析模块和恶意软件检测
- 多语言 API 绑定

### 扩展组件

**软件兼容性**
- **FAST 软件**: 通过 WinUsbHook 支持
- **FOAH 软件**: 专门优化的 Hook 策略
- **其他 DMA 软件**: 通用兼容层

## 工作流程和依赖关系

### 标准工作流程
```
目标软件 (FAST/FOAH等)
    ↓ (VMMDLL_Initialize Hook)
WinUsbHook (DLL 注入)
    ↓ (参数重写: fpga:// → rpc://)
LeechCore RPC 客户端
    ↓ (网络 RPC 调用)
CustomLeechServer (RPC 服务端)
    ↓ (驱动 IOCTL 调用)
ReadPhys 驱动
    ↓ (物理内存访问)
系统物理内存
```

### 关键交互节点

1. **Hook 层**: WinUsbHook 拦截并重写内存访问参数
2. **网络层**: CustomLeechServer 提供 RPC 服务
3. **驱动层**: ReadPhys 执行实际的物理内存操作
4. **应用层**: MemProcFS 提供高级分析功能

## 开发注意事项

### 安全要求
- 该项目涉及底层内存访问和系统安全功能
- 仅支持防御性安全研究和合法的内存取证分析
- 禁止用于恶意目的或未经授权的内存访问

### ReadPhys 驱动开发规范
- **必须使用 KLI 框架**: 所有内核 API 调用必须通过 `KLI_CACHED_CALL` 包装
- **内存分配**: 使用 `KLI_CACHED_CALL(ExAllocatePoolWithTag, ...)`
- **字符串操作**: 避免明文常量，使用混淆和动态生成
- **IOCTL 处理**: 严格验证输入参数，防止缓冲区溢出
- **异常处理**: 使用 `__try/__except` 保护关键操作

### CustomLeechServer 开发规范  
- **R3/R0 交互**: 必须先在驱动中实现功能，再在用户态封装
- **设备连接**: 使用动态生成的设备名称连接驱动
- **错误处理**: 详细的错误码和调试输出
- **RPC 协议**: 严格遵循 LeechCore RPC 规范

### WinUsbHook 开发规范
- **Hook 策略**: 根据目标软件选择最适合的 Hook 方法
- **参数重写**: 保持原始调用语义，仅修改连接参数
- **缓存机制**: 避免重复初始化，提高性能
- **错误处理**: 优雅处理 Hook 失败情况

### 构建要求
- **Windows**: Visual Studio 2022, Windows SDK
- **驱动开发**: WDK (Windows Driver Kit)，需要测试签名或禁用强制签名
- **权限要求**: 驱动需要管理员权限加载
- **网络配置**: 防火墙需要允许端口 28473 (RPC) 和 28474 (握手)

## 关键文件和目录

### ReadPhys 驱动
- `ReadPhys-master/ReadPhys/entry.cpp` - 驱动主入口和 IOCTL 处理
- `ReadPhys-master/ReadPhys/KliGlobal.h` - KLI 函数指针定义
- `ReadPhys-master/ReadPhys/Anti4heatExpert.h` - 物理内存访问核心
- `ReadPhys-master/ReadPhys/pagereader.c` - 分页内存读取实现

### CustomLeechServer
- `CustomLeechServer/src/leechrpcserver.c` - RPC 服务器实现
- `CustomLeechServer/src/npp_plugin.cpp` - 服务器启动入口
- `CustomLeechServer/include/liangzi.hpp` - 授权验证

### WinUsbHook
- `WinUsbHook/winusb_hook_vmm.cpp` - 主要 Hook 实现
- `WinUsbHook/software_detector.hpp` - 软件检测逻辑
- `WinUsbHook/hdhook.cpp` - 硬件断点 Hook 实现

### 核心库
- `LeechCore/leechcore/leechcore.h` - LeechCore API 定义
- `MemProcFS/includes/vmmdll.h` - MemProcFS API 定义
- `LeechCore/leechrpc.idl` - RPC 接口定义

## API 和示例

### ReadPhys 驱动 IOCTL 接口
```cpp
#define IOCTL_DMA_READ_MEMORY   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_READ_SCATTER  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_GET_DTB       CTL_CODE(FILE_DEVICE_UNKNOWN, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_GET_KERNELBASE CTL_CODE(FILE_DEVICE_UNKNOWN, 0x805, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_GET_PHYSMAX    CTL_CODE(FILE_DEVICE_UNKNOWN, 0x806, METHOD_BUFFERED, FILE_ANY_ACCESS)
```

### CustomLeechServer RPC 命令
- `LC_CMD_MEMMAP_GET/SET` - 内存映射管理
- `LC_CMD_STATISTICS_GET` - 性能统计
- `LEECHRPC_MSGTYPE_READSCATTER_REQ` - 散读请求
- `LEECHRPC_MSGTYPE_COMMAND_REQ` - 通用命令

### WinUsbHook 使用
1. 编译为 `winusb.dll`
2. 放置在目标软件目录
3. 启动 CustomLeechServer
4. 运行目标软件，自动完成 Hook 和重定向

## 故障排除

### 常见问题
1. **Hook 失败**: 检查软件检测逻辑，可能需要调整 Hook 策略
2. **RPC 连接失败**: 确认防火墙设置和网络连通性
3. **驱动加载失败**: 检查测试签名设置和管理员权限
4. **内存读取失败**: 验证 DTB 和物理地址有效性

### 调试技巧
- 使用 `DBG_PRINT` 宏在驱动中输出调试信息
- 在 CustomLeechServer 中启用详细日志
- 使用 Process Monitor 监控文件和注册表访问
- 使用 Wireshark 分析网络 RPC 流量

### 性能优化
- 使用散读减少 IOCTL 调用次数
- 启用缓存机制避免重复操作
- 调整 RPC 压缩设置平衡性能和网络使用

## 最新更新

- **WinUsbHook 缓存机制**: 防止重复初始化，提高响应速度
- **ReadPhys 散读优化**: 支持批量物理内存读取
- **CustomLeechServer DTB 支持**: 增强 Directory Table Base 处理
- **软件兼容性改进**: 针对 FAST/FOAH 软件的专门优化

## 重要提醒

1. **遵循 KLI 规范**: 驱动开发必须使用 KLI 框架
2. **R3/R0 分离**: 用户态代码不得直接访问内核功能
3. **错误处理**: 所有函数都应有完善的错误处理
4. **性能考虑**: 避免不必要的内存拷贝和 IOCTL 调用
5. **安全第一**: 所有输入都必须严格验证