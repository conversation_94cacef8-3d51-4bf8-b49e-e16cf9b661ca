#include "../include/DriverInterface.h"
#include "../include/VMP.h"
#include <iostream>
#include <string>
#include <intrin.h>
#include <winioctl.h>  // For CTL_CODE and related macros
#include <winternl.h>  // For NtQuerySystemInformation
#include <tlhelp32.h>  // For process enumeration

// 声明 NtQuerySystemInformation 函数
typedef NTSTATUS(WINAPI* PFN_NtQuerySystemInformation)(
    SYSTEM_INFORMATION_CLASS SystemInformationClass,
    PVOID SystemInformation,
    ULONG SystemInformationLength,
    PULONG ReturnLength);

// 常量定义
#ifndef STATUS_INFO_LENGTH_MISMATCH
#define STATUS_INFO_LENGTH_MISMATCH      ((NTSTATUS)0xC0000004L)
#endif

// 完整的 SYSTEM_PROCESS_INFORMATION 结构定义 (绕过Windows SDK的限制版本)
typedef struct _SYSTEM_PROCESS_INFORMATION_FULL {
    ULONG NextEntryOffset;
    ULONG NumberOfThreads;
    LARGE_INTEGER WorkingSetPrivateSize;
    ULONG HardFaultCount;
    ULONG NumberOfThreadsHighWatermark;
    ULONGLONG CycleTime;
    LARGE_INTEGER CreateTime;
    LARGE_INTEGER UserTime;
    LARGE_INTEGER KernelTime;
    UNICODE_STRING ImageName;
    LONG BasePriority;
    HANDLE UniqueProcessId;
    HANDLE InheritedFromUniqueProcessId;
    ULONG HandleCount;
    ULONG SessionId;
    ULONG_PTR UniqueProcessKey;
    SIZE_T PeakVirtualSize;
    SIZE_T VirtualSize;
    ULONG PageFaultCount;
    SIZE_T PeakWorkingSetSize;
    SIZE_T WorkingSetSize;
    SIZE_T QuotaPeakPagedPoolUsage;
    SIZE_T QuotaPagedPoolUsage;
    SIZE_T QuotaPeakNonPagedPoolUsage;
    SIZE_T QuotaNonPagedPoolUsage;
    SIZE_T PagefileUsage;
    SIZE_T PeakPagefileUsage;
    SIZE_T PrivatePageCount;
    LARGE_INTEGER ReadOperationCount;
    LARGE_INTEGER WriteOperationCount;
    LARGE_INTEGER OtherOperationCount;
    LARGE_INTEGER ReadTransferCount;
    LARGE_INTEGER WriteTransferCount;
    LARGE_INTEGER OtherTransferCount;
} SYSTEM_PROCESS_INFORMATION_FULL, *PSYSTEM_PROCESS_INFORMATION_FULL;

// Network DMA protocol definitions
#define DMA_MAGIC 0x444D4131  // "DMA1"
#define DMA_CMD_READ  0x01
#define DMA_CMD_WRITE 0x02

// Define CTL_CODE macros if not already defined (backup definitions)
#ifndef CTL_CODE
#define FILE_DEVICE_UNKNOWN     0x00000022
#define METHOD_BUFFERED         0
#define FILE_ANY_ACCESS         0
#define CTL_CODE(DeviceType, Function, Method, Access) ( \
    ((DeviceType) << 16) | ((Access) << 14) | ((Function) << 2) | (Method) \
)
#endif



#define IOCTL_DMA_READ_MEMORY   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_WRITE_MEMORY  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_IDENTIFY      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_GET_DTB       CTL_CODE(FILE_DEVICE_UNKNOWN, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)
// ����������ɢ�� IOCTL����Сʵ�֣�METHOD_BUFFERED��
#define IOCTL_DMA_READ_SCATTER  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS)
// ��������ȡ�ں˻�ַ����������
#define IOCTL_DMA_GET_KERNELBASE CTL_CODE(FILE_DEVICE_UNKNOWN, 0x805, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_GET_PHYSMAX    CTL_CODE(FILE_DEVICE_UNKNOWN, 0x806, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_ENUM_PROCESSES CTL_CODE(FILE_DEVICE_UNKNOWN, 0x807, METHOD_BUFFERED, FILE_ANY_ACCESS)

// 进程信息结构 (必须与驱动完全一致)
typedef struct _PROCESS_INFO {
    ULONG ProcessId;
    ULONG ParentProcessId;
    ULONG64 DirectoryTableBase;
    CHAR ImageFileName[16];
    ULONG SessionId;
    ULONG State;                    // 进程状态
    ULONG64 VirtualAddress;         // EPROCESS 虚拟地址
    UCHAR Reserved[8];
} PROCESS_INFO, *PPROCESS_INFO;

// 进程列表响应头 (必须与驱动完全一致)
typedef struct _PROCESS_LIST_RESPONSE {
    ULONG Version;                  // = 1
    ULONG ProcessCount;             // 进程数量
    ULONG TotalSize;                // 总大小
    ULONG Reserved;
    PROCESS_INFO Processes[1];      // 可变长进程数组
} PROCESS_LIST_RESPONSE, *PPROCESS_LIST_RESPONSE;

// Helper functions for device name generation
static ULONG SimpleHash(const char* str, ULONG seed)
{
    ULONG hash = seed;
    while (*str) {
        hash = hash * 33 + (ULONG)(*str);  // Match driver implementation with explicit cast
        str++;
    }
    return hash;
}

static ULONG GetCpuId()
{
    int cpuInfo[4];
    __cpuid(cpuInfo, 1);
    return (ULONG)cpuInfo[3]; // EDX register contains CPU features - must match driver implementation
}

static void GeneratePredictableGUID(ULONG* part1, ULONG* part2, ULONG* part3, ULONG* part4)
{
    // Get CPU ID as system unique identifier - must match driver implementation exactly
    ULONG cpuId = GetCpuId();
    
    // Generate unique seeds based on CPU ID - must match driver implementation exactly
    ULONG baseSeed1 = 0x12345678 ^ cpuId;
    ULONG baseSeed2 = 0x87654321 ^ (cpuId >> 8);
    ULONG baseSeed3 = 0xABCDEF00 ^ (cpuId >> 16);
    ULONG baseSeed4 = 0x00FEDCBA ^ (cpuId >> 24);

    // Use fixed strings mixed with CPU ID - must match driver implementation exactly
    const char* seed1 = "ReadPhysDevice2024";
    const char* seed2 = "DMAController";
    const char* seed3 = "PhysicalMemory";  // Fixed to match driver implementation
    const char* seed4 = "KernelDriver";

    *part1 = SimpleHash(seed1, baseSeed1);
    *part2 = SimpleHash(seed2, baseSeed2);
    *part3 = SimpleHash(seed3, baseSeed3);
    *part4 = SimpleHash(seed4, baseSeed4);
}

static std::wstring GenerateDeviceName()
{
    ULONG part1, part2, part3, part4;
    GeneratePredictableGUID(&part1, &part2, &part3, &part4);

    // Debug output to verify GUID generation matches driver
#if 0
    printf("[DEBUG] Generated GUID parts: %08lx-%04x-%04x-%04x-%08lx%04x\n",
        (unsigned long)part1,
        (USHORT)(part2 >> 16),
        (USHORT)(part2 & 0xFFFF),
        (USHORT)(part3 >> 16),
        (unsigned long)part4,
        (USHORT)(part3 & 0xFFFF));
#endif

    wchar_t deviceName[256];
    // Use the same GUID format as driver, but with user-mode device path prefix
    // Driver creates: \DosDevices\{GUID}, user-mode accesses via \\.\{GUID}
    swprintf_s(deviceName, 256,
        L"\\\\.\\{%08x-%04x-%04x-%04x-%08x%04x}",
        part1,
        (USHORT)(part2 >> 16),
        (USHORT)(part2 & 0xFFFF),
        (USHORT)(part3 >> 16),
        part4,
        (USHORT)(part3 & 0xFFFF));

    return std::wstring(deviceName);
}

//-----------------------------------------------------------------------------
// Constructor/Destructor
//-----------------------------------------------------------------------------

DriverInterface::DriverInterface()
    : hDriver(INVALID_HANDLE_VALUE)
    , fInitialized(FALSE)
{
    VMProtectBeginUltra("DriverInterface::Ctor");
    ZeroMemory(szDriverName, sizeof(szDriverName));
    VMProtectEnd();
}

DriverInterface::~DriverInterface()
{
    Close();
}

//-----------------------------------------------------------------------------
// Initialization
//-----------------------------------------------------------------------------

BOOL DriverInterface::Initialize(LPCSTR szDriverName)
{
    VMProtectBegin("DriverInterface::Initialize");
    if (fInitialized) {
        VMProtectEnd();
        return TRUE;
    }

    // Generate device name using CPU ID based method (must match driver implementation)
    ULONG cpuId = GetCpuId();
    DBG_PRINTF("[DEBUG] CPU ID: 0x%08lX\n", (unsigned long)cpuId);
    
    std::wstring deviceName = GenerateDeviceName();
    
    // Convert to char for storage
    if (szDriverName) {
        strcpy_s(this->szDriverName, MAX_PATH, szDriverName);
    } else {
        // Convert wide string to char for storage
        WideCharToMultiByte(CP_UTF8, 0, deviceName.c_str(), -1, 
                           this->szDriverName, MAX_PATH, NULL, NULL);
    }
    
    {
        char utf8[512];
        WideCharToMultiByte(CP_UTF8, 0, deviceName.c_str(), -1, utf8, sizeof(utf8), NULL, NULL);
        DBG_PRINTF("[DEBUG] Generated device name: %s\n", utf8);
    }

    // Open the kernel driver device using generated name
    hDriver = CreateFileW(
        deviceName.c_str(),
        GENERIC_READ | GENERIC_WRITE,
        FILE_SHARE_READ | FILE_SHARE_WRITE,
        NULL,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        NULL
    );

    // Debug output
    if (hDriver == INVALID_HANDLE_VALUE) {
        DWORD dwError = GetLastError();
        {
            char utf8[512];
            WideCharToMultiByte(CP_UTF8, 0, deviceName.c_str(), -1, utf8, sizeof(utf8), NULL, NULL);
            DBG_PRINTF("Failed to open device: %s, Error: %lu\n", utf8, (unsigned long)dwError);
        }
        LogError("CreateFileW", dwError);
        VMProtectEnd();
        return FALSE;
    } else {
        {
            char utf8[512];
            WideCharToMultiByte(CP_UTF8, 0, deviceName.c_str(), -1, utf8, sizeof(utf8), NULL, NULL);
            DBG_PRINTF("Successfully opened device: %s\n", utf8);
        }
        LogInfo("Driver interface initialized successfully");
    }

    fInitialized = TRUE;
    
    // Try to identify the driver
    IdentifyDriver();
    
    VMProtectEnd();
    return TRUE;
}

VOID DriverInterface::Close()
{
    VMProtectBegin("DriverInterface::Close");
    if (hDriver != INVALID_HANDLE_VALUE) {
        CloseHandle(hDriver);
        hDriver = INVALID_HANDLE_VALUE;
    }

    fInitialized = FALSE;
    LogInfo("Driver interface closed");
    VMProtectEnd();
}

//-----------------------------------------------------------------------------
// Memory Operations
//-----------------------------------------------------------------------------

BOOL DriverInterface::ReadPhysicalMemory(QWORD qwAddress, DWORD cbSize, PBYTE pbBuffer)
{
    VMProtectBeginVirtualization("DriverInterface::ReadPhys");
    if (!fInitialized || !pbBuffer || cbSize == 0) {
        LogError("ReadPhysicalMemory", ERROR_INVALID_PARAMETER);
        VMProtectEnd();
        return FALSE;
    }

    // Kernel driver commonly requires <= 0x1000 and page-aware reads.
    // Perform conservative page-split reads to avoid GEN_FAILURE from the driver.
    typedef struct _DMA_REQUEST_READ {
        DWORD Command;
        ULONGLONG Address;
        DWORD Length;
        DWORD Reserved;
    } DMA_REQUEST_READ;

    const DWORD PAGE_SIZE = 0x1000;
    QWORD currAddress = qwAddress;
    DWORD remaining = cbSize;
    PBYTE dest = pbBuffer;

    while (remaining > 0) {
        DWORD pageOffset = (DWORD)(currAddress & (PAGE_SIZE - 1));
        DWORD maxChunkThisPage = PAGE_SIZE - pageOffset;
        DWORD chunk = remaining < maxChunkThisPage ? remaining : maxChunkThisPage;

        // Enforce DWORD-aligned access and min length constraints expected by driver.
        QWORD alignedAddr = currAddress & ~0x3ULL;                    // 4-byte aligned start
        DWORD headOffset = (DWORD)(currAddress - alignedAddr);        // bytes to skip from aligned head
        DWORD alignedLen = headOffset + chunk;                        // total bytes to fetch from aligned head
        alignedLen = (alignedLen + 3) & ~3U;                          // round up to multiple of 4
        if (alignedLen < 8) { alignedLen = 8; }                       // many drivers require >= 8 bytes

        // Ensure we don't cross the page boundary from the aligned start.
        DWORD alignedPageOffset = (DWORD)(alignedAddr & (PAGE_SIZE - 1));
        DWORD maxFromAligned = PAGE_SIZE - alignedPageOffset;
        if (alignedLen > maxFromAligned) {
            alignedLen = maxFromAligned;                               // clamp within page
            // Also clamp the user-visible chunk accordingly
            if (alignedLen <= headOffset) {
                // Should not happen; fallback to minimal forward progress
                alignedLen = (headOffset + 8 + 3) & ~3U;
                if (alignedLen > maxFromAligned) { alignedLen = maxFromAligned; }
            }
            DWORD usable = alignedLen - headOffset;
            if (usable < chunk) { chunk = usable; }
        }

        BOOL useTemp = (headOffset != 0) || (alignedLen != chunk);
        BYTE tempBuf[PAGE_SIZE];
        PBYTE outBuf = useTemp ? tempBuf : dest;

        DMA_REQUEST_READ req = {};
        req.Command = DMA_CMD_READ;
        req.Address = (ULONGLONG)alignedAddr;
        req.Length = alignedLen;
        req.Reserved = 0;

        DWORD cbBytesReturned = 0;
        BOOL fResult = FALSE;
        DWORD usableFromHead = 0; // declare before potential early use

        // Try progressive smaller lengths on failure to avoid GEN_FAILURE from driver
        DWORD tryLen = alignedLen;
        for (;;) {
            fResult = DeviceIoControl(
                hDriver,
                IOCTL_DMA_READ_MEMORY,
                &req,
                (DWORD)sizeof(req),
                outBuf,
                tryLen,
                &cbBytesReturned,
                NULL
            );

            if (fResult && cbBytesReturned > 0) {
                break;
            }

            if (tryLen <= 8) {
                // minimum granularity reached - treat as unmapped: fill zeros and continue
                DWORD dwError = GetLastError();
                printf("[READ_FAIL] addr=0x%llX alignedAddr=0x%llX headOff=%u alignedLen=%u tryLen=%u pageOff=%u err=0x%08X cbRet=%u -> ZERO-FILL\n",
                    (unsigned long long)currAddress,
                    (unsigned long long)alignedAddr,
                    (unsigned)headOffset,
                    (unsigned)alignedLen,
                    (unsigned)tryLen,
                    (unsigned)alignedPageOffset,
                    (unsigned)dwError,
                    (unsigned)cbBytesReturned);
                // zero-fill the requested chunk to emulate sparse/holes; advance and continue
                memset(dest, 0, chunk);
                usableFromHead = chunk;
                cbBytesReturned = 0;
                fResult = TRUE; // synthesize success for this chunk
                break;
            }

            // Reduce tryLen: keep 4-byte alignment, bound within page
            tryLen >>= 1;
            tryLen = (tryLen + 3) & ~3U;
            if (tryLen > maxFromAligned) {
                tryLen = maxFromAligned;
            }
            req.Length = tryLen;
        }

        // Determine how much of the requested 'chunk' we actually obtained
        if (cbBytesReturned > headOffset) {
            DWORD available = cbBytesReturned - headOffset;
            usableFromHead = (available < chunk) ? available : chunk;
        }

        if (useTemp) {
            if (usableFromHead) {
                memcpy(dest, tempBuf + headOffset, usableFromHead);
            }
        } else {
            // outBuf == dest case: data already placed at dest offset 0
            // If there's a head offset in this branch, useTemp would be true.
            // So nothing to do here.
        }

        if (usableFromHead == 0) {
            // should not happen since we zero-filled above, but ensure progress
            usableFromHead = chunk;
        }

        currAddress += usableFromHead;
        dest += usableFromHead;
        remaining -= usableFromHead;
    }

    VMProtectEnd();
    return TRUE;
}

BOOL DriverInterface::WritePhysicalMemory(QWORD qwAddress, DWORD cbSize, PBYTE pbBuffer)
{
    VMProtectBeginVirtualization("DriverInterface::WritePhys");
    if (!fInitialized || !pbBuffer || cbSize == 0) {
        LogError("WritePhysicalMemory", ERROR_INVALID_PARAMETER);
        VMProtectEnd();
        return FALSE;
    }

    // Allocate request buffer
    DWORD cbRequestSize = sizeof(PHYSICAL_MEMORY_REQUEST) + cbSize - 1;
    PPHYSICAL_MEMORY_REQUEST pRequest = (PPHYSICAL_MEMORY_REQUEST)malloc(cbRequestSize);
    if (!pRequest) {
        LogError("WritePhysicalMemory", ERROR_NOT_ENOUGH_MEMORY);
        return FALSE;
    }

    // Setup request
    pRequest->PhysicalAddress = qwAddress;
    pRequest->Size = cbSize;
    memcpy(pRequest->Data, pbBuffer, cbSize);

    DWORD cbBytesReturned = 0;
    BOOL fResult = DeviceIoControl(
        hDriver,
        IOCTL_DMA_WRITE_MEMORY,
        pRequest,
        cbRequestSize,
        NULL,
        0,
        &cbBytesReturned,
        NULL
    );

    if (!fResult) {
        DWORD dwError = GetLastError();
        LogError("DeviceIoControl (WRITE)", dwError);
    }

    free(pRequest);
    VMProtectEnd();
    return fResult;
}

// Optional kernel scatter read layout (METHOD_BUFFERED):
// Input:  [DWORD Version][DWORD Count][Count * { ULONGLONG Address; DWORD Length; DWORD _pad }]
// Output: [concatenated data for each entry, in-order]
// If not supported by the driver, IOCTL will fail and we fallback to per-entry reads.
BOOL DriverInterface::ReadScatter(DWORD cMEMs, PPMEM_SCATTER ppMEMs)
{
    VMProtectBegin("DriverInterface::ReadScatter");
    if (!fInitialized || !ppMEMs || cMEMs == 0) {
        LogError("ReadScatter", ERROR_INVALID_PARAMETER);
        VMProtectEnd();
        return FALSE;
    }

    // Validate inputs and clear f flags
    for (DWORD i = 0; i < cMEMs; i++) {
        PMEM_SCATTER p = ppMEMs[i];
        if (!p || !p->pb) { VMProtectEnd(); return FALSE; }
        p->f = FALSE;
        // Basic constraints (LeechCore/winpmem style): <= 0x1000, not cross page, dword aligned length
        if (p->cb > 0x1000) { VMProtectEnd(); return FALSE; }
        if (((p->qwA & 0xFFFULL) + p->cb) > 0x1000) { VMProtectEnd(); return FALSE; }
        if ((p->cb & 3U) != 0) { VMProtectEnd(); return FALSE; }
    }

    // Probe: attempt kernel scatter IOCTL in batches. Fallback if unsupported.
    const DWORD kMaxItemsPerBatch = 0x2000;           // 4096 entries (~16MB if each 4KB)
    const DWORD kMaxTotalBytesPerBatch = 32 * 1024 * 1024; // 16MB cap

    BOOL overallOK = TRUE;
    DWORD idx = 0;
    while (idx < cMEMs) {
        // Build one batch within limits
        DWORD count = 0;
        DWORD cbSum = 0;
        DWORD start = idx;
        while ((start + count) < cMEMs && count < kMaxItemsPerBatch) {
            PMEM_SCATTER p = ppMEMs[start + count];
            if (cbSum + p->cb > kMaxTotalBytesPerBatch) break;
            cbSum += p->cb;
            count++;
        }
        if (count == 0) { // ensure progress
            PMEM_SCATTER p = ppMEMs[idx];
            count = 1; cbSum = p->cb;
        }

        // Allocate input and output buffers
        struct SCAT_ITEM { ULONGLONG Address; DWORD Length; DWORD Pad; };
        struct SCAT_HDR { DWORD Version; DWORD Count; };
        const DWORD ver = 0x53434154; // 'SCAT'
        SIZE_T inSize = sizeof(SCAT_HDR) + count * sizeof(SCAT_ITEM);
        BYTE* inBuf = (BYTE*)LocalAlloc(LMEM_ZEROINIT, inSize);
        BYTE* outBuf = (BYTE*)LocalAlloc(LMEM_ZEROINIT, cbSum);
        if (!inBuf || !outBuf) {
            LocalFree(inBuf); LocalFree(outBuf); overallOK = FALSE; break;
        }
        // Fill header and items
        ((SCAT_HDR*)inBuf)->Version = ver;
        ((SCAT_HDR*)inBuf)->Count = count;
        SCAT_ITEM* it = (SCAT_ITEM*)(inBuf + sizeof(SCAT_HDR));
        for (DWORD i = 0; i < count; i++) {
            PMEM_SCATTER p = ppMEMs[start + i];
            it[i].Address = (ULONGLONG)p->qwA;
            it[i].Length = p->cb;
            it[i].Pad = 0;
        }

        DWORD cbReturned = 0;
        BOOL ok = DeviceIoControl(
            hDriver,
            IOCTL_DMA_READ_SCATTER,
            inBuf,
            (DWORD)inSize,
            outBuf,
            cbSum,
            &cbReturned,
            NULL
        );

        if (ok && cbReturned == cbSum) {
            // Distribute data back to MEM buffers
            DWORD o = 0;
            for (DWORD i = 0; i < count; i++) {
                PMEM_SCATTER p = ppMEMs[start + i];
                memcpy(p->pb, outBuf + o, p->cb);
                p->f = TRUE;
                o += p->cb;
            }
        } else {
            // Fallback to per-segment user-mode reads for this batch
            overallOK = FALSE; // mark partial fallback
            for (DWORD i = 0; i < count; i++) {
                PMEM_SCATTER p = ppMEMs[start + i];
                BOOL fs = ReadPhysicalMemory(p->qwA, p->cb, p->pb);
                p->f = fs ? TRUE : FALSE;
            }
        }
        LocalFree(inBuf); LocalFree(outBuf);
        idx = start + count;
    }

    // overallOK indicates whether all batches used kernel scatter; success criteria should be per-MEM f flags
    BOOL anyFail = FALSE;
    for (DWORD i = 0; i < cMEMs; i++) {
        if (!ppMEMs[i]->f) { anyFail = TRUE; break; }
    }
    VMProtectEnd();
    return !anyFail;
}

BOOL DriverInterface::WriteScatter(DWORD cMEMs, PPMEM_SCATTER ppMEMs)
{
    VMProtectBegin("DriverInterface::WriteScatter");
    if (!fInitialized || !ppMEMs || cMEMs == 0) {
        LogError("WriteScatter", ERROR_INVALID_PARAMETER);
        VMProtectEnd();
        return FALSE;
    }

    BOOL fOverallSuccess = TRUE;
    
    // Process each scatter request individually
    for (DWORD i = 0; i < cMEMs; i++) {
        PMEM_SCATTER pMEM = ppMEMs[i];
        if (!pMEM || !pMEM->pb) {
            continue;
        }

        // Attempt to write memory
        BOOL fSuccess = WritePhysicalMemory(
            pMEM->qwA,
            pMEM->cb,
            pMEM->pb
        );

        // Update scatter status
        if (fSuccess) {
            pMEM->f = TRUE;  // Mark as successful
        } else {
            pMEM->f = FALSE; // Mark as failed
            fOverallSuccess = FALSE;
        }
    }

    VMProtectEnd();
    return fOverallSuccess;
}

//-----------------------------------------------------------------------------
// Driver Identification
//-----------------------------------------------------------------------------

BOOL DriverInterface::IdentifyDriver()
{
    VMProtectBegin("DriverInterface::Identify");
    if (!fInitialized) {
        LogError("IdentifyDriver", ERROR_INVALID_HANDLE);
        VMProtectEnd();
        return FALSE;
    }

    DWORD cbBytesReturned = 0;
    CHAR szIdentifyBuffer[256] = {0};
    
    BOOL fResult = DeviceIoControl(
        hDriver,
        IOCTL_DMA_IDENTIFY,
        NULL,
        0,
        szIdentifyBuffer,
        sizeof(szIdentifyBuffer),
        &cbBytesReturned,
        NULL
    );

    if (fResult && cbBytesReturned > 0) {
        LogInfo("Driver identification successful");
       // std::cout << "[INFO] Driver Info: " << szIdentifyBuffer << std::endl;
        VMProtectEnd();
        return TRUE;
    } else {
        DWORD dwError = GetLastError();
        LogError("DeviceIoControl (IDENTIFY)", dwError);
        VMProtectEnd();
        return FALSE;
    }
}

//-----------------------------------------------------------------------------
// Private Utility Methods
//-----------------------------------------------------------------------------

VOID DriverInterface::LogError(LPCSTR szFunction, DWORD dwError)
{
    std::cout << "[ERROR] " << szFunction << " failed with error code: 0x" 
              << std::hex << dwError << std::dec << std::endl;
}

VOID DriverInterface::LogInfo(LPCSTR szMessage)
{
    DBG_PRINTF("[INFO] %s\n", szMessage);
}

//-----------------------------------------------------------------------------
// System Information Methods
//-----------------------------------------------------------------------------

QWORD DriverInterface::GetDTB()
{
    VMProtectBegin("DriverInterface::GetDTB");
    if (!fInitialized) {
        LogError("GetDTB", ERROR_INVALID_HANDLE);
        VMProtectEnd();
        return 0;
    }
    ULONGLONG dtb = 0;
    DWORD cbBytesReturned = 0;
    BOOL ok = DeviceIoControl(
        hDriver,
        IOCTL_DMA_GET_DTB,
        NULL,
        0,
        &dtb,
        (DWORD)sizeof(dtb),
        &cbBytesReturned,
        NULL
    );
    if (ok && cbBytesReturned == sizeof(dtb) && dtb) {
        VMProtectEnd();
        return (QWORD)dtb;
    }
    VMProtectEnd();
    return 0;
}

QWORD DriverInterface::GetKernelBase()
{
    VMProtectBegin("DriverInterface::GetKernelBase");
    if (!fInitialized) {
        LogError("GetKernelBase", ERROR_INVALID_HANDLE);
        VMProtectEnd();
        return 0;
    }
    ULONGLONG va = 0;
    DWORD cbBytesReturned = 0;
    BOOL ok = DeviceIoControl(
        hDriver,
        IOCTL_DMA_GET_KERNELBASE,
        NULL,
        0,
        &va,
        (DWORD)sizeof(va),
        &cbBytesReturned,
        NULL
    );
    if (ok && cbBytesReturned == sizeof(va) && va) {
        VMProtectEnd();
        return (QWORD)va;
    }
    VMProtectEnd();
    return 0;
}

QWORD DriverInterface::GetPhysicalMax()
{
    VMProtectBegin("DriverInterface::GetPhysicalMax");
    if (!fInitialized) {
        LogError("GetPhysicalMax", ERROR_INVALID_HANDLE);
        VMProtectEnd();
        return 0;
    }
    ULONGLONG paMax = 0;
    DWORD cbBytesReturned = 0;
    BOOL ok = DeviceIoControl(
        hDriver,
        IOCTL_DMA_GET_PHYSMAX,
        NULL,
        0,
        &paMax,
        (DWORD)sizeof(paMax),
        &cbBytesReturned,
        NULL
    );
    if (ok && cbBytesReturned == sizeof(paMax) && paMax) {
        VMProtectEnd();
        return (QWORD)paMax;
    }
    VMProtectEnd();
    return 0;
}

//-----------------------------------------------------------------------------
// Process enumeration - R3 Implementation using NtQuerySystemInformation
//-----------------------------------------------------------------------------

// 安全的字符串转换函数 (宽字符到多字节)
static void SafeWideCharToMultiByte(LPCWSTR src, LPSTR dest, DWORD destSize)
{
    if (!src || !dest || destSize == 0) {
        if (dest && destSize > 0) dest[0] = '\0';
        return;
    }
    
    int result = WideCharToMultiByte(CP_UTF8, 0, src, -1, dest, destSize, NULL, NULL);
    if (result == 0) {
        // 转换失败，使用安全的替代方案
        strncpy_s(dest, destSize, "<unknown>", _TRUNCATE);
    }
}

// 获取进程的DTB (Directory Table Base) - 简化实现
static ULONG64 GetProcessDTB(DWORD processId)
{
    // 对于R3实现，我们无法直接获取DTB
    // 返回0表示需要通过其他方式获取
    // MemProcFS会通过其他机制来确定DTB
    return 0;
}

BOOL DriverInterface::EnumerateProcesses(PVOID* ppBuffer, PDWORD pcbSize)
{
    VMProtectBegin("DriverInterface::EnumerateProcesses");
    
    if (!ppBuffer || !pcbSize) {
        LogError("EnumerateProcesses", ERROR_INVALID_PARAMETER);
        VMProtectEnd();
        return FALSE;
    }

    *ppBuffer = NULL;
    *pcbSize = 0;

    // 获取 ntdll.dll 中的 NtQuerySystemInformation 函数
    HMODULE hNtdll = GetModuleHandleW(L"ntdll.dll");
    if (!hNtdll) {
        LogError("GetModuleHandle(ntdll.dll)", GetLastError());
        VMProtectEnd();
        return FALSE;
    }

    PFN_NtQuerySystemInformation pfnNtQuerySystemInformation = 
        (PFN_NtQuerySystemInformation)GetProcAddress(hNtdll, "NtQuerySystemInformation");
    if (!pfnNtQuerySystemInformation) {
        LogError("GetProcAddress(NtQuerySystemInformation)", GetLastError());
        VMProtectEnd();
        return FALSE;
    }

    // 分配初始缓冲区
    ULONG bufferSize = 0x10000;  // 64KB 初始大小
    PVOID systemBuffer = NULL;
    NTSTATUS status;
    
    // 循环直到获取足够大的缓冲区
    for (int attempts = 0; attempts < 3; attempts++) {
        systemBuffer = LocalAlloc(LMEM_ZEROINIT, bufferSize);
        if (!systemBuffer) {
            LogError("LocalAlloc", ERROR_NOT_ENOUGH_MEMORY);
            VMProtectEnd();
            return FALSE;
        }
        
        ULONG returnLength = 0;
        status = pfnNtQuerySystemInformation(
            SystemProcessInformation,
            systemBuffer,
            bufferSize,
            &returnLength
        );
        
        if (NT_SUCCESS(status)) {
            break;
        }
        
        LocalFree(systemBuffer);
        systemBuffer = NULL;
        
        if (status == STATUS_INFO_LENGTH_MISMATCH && returnLength > bufferSize) {
            bufferSize = returnLength + 0x1000;  // 额外留一些空间
            continue;
        } else {
            LogError("NtQuerySystemInformation", RtlNtStatusToDosError(status));
            VMProtectEnd();
            return FALSE;
        }
    }
    
    if (!systemBuffer || !NT_SUCCESS(status)) {
        LogError("NtQuerySystemInformation failed after retries", RtlNtStatusToDosError(status));
        VMProtectEnd();
        return FALSE;
    }

    // 第一遍扫描：计算进程数量
    DWORD processCount = 0;
    SYSTEM_PROCESS_INFORMATION_FULL* currentProcess = (SYSTEM_PROCESS_INFORMATION_FULL*)systemBuffer;
    
    while (true) {
        processCount++;
        
        if (currentProcess->NextEntryOffset == 0) {
            break;
        }
        
        currentProcess = (SYSTEM_PROCESS_INFORMATION_FULL*)(
            (PBYTE)currentProcess + currentProcess->NextEntryOffset
        );
    }

    // 分配输出缓冲区
    DWORD outputSize = sizeof(PROCESS_LIST_RESPONSE) + (processCount - 1) * sizeof(PROCESS_INFO);
    PPROCESS_LIST_RESPONSE outputBuffer = (PPROCESS_LIST_RESPONSE)LocalAlloc(LMEM_ZEROINIT, outputSize);
    if (!outputBuffer) {
        LogError("LocalAlloc(output)", ERROR_NOT_ENOUGH_MEMORY);
        LocalFree(systemBuffer);
        VMProtectEnd();
        return FALSE;
    }

    // 设置响应头
    outputBuffer->Version = 1;
    outputBuffer->ProcessCount = processCount;
    outputBuffer->TotalSize = outputSize;
    outputBuffer->Reserved = 0;

    // 第二遍扫描：填充进程信息
    currentProcess = (SYSTEM_PROCESS_INFORMATION_FULL*)systemBuffer;
    DWORD index = 0;
    
    while (true) {
        PPROCESS_INFO processInfo = &outputBuffer->Processes[index];
        
        // 填充基本信息
        processInfo->ProcessId = (ULONG)(ULONG_PTR)currentProcess->UniqueProcessId;
        processInfo->ParentProcessId = (ULONG)(ULONG_PTR)currentProcess->InheritedFromUniqueProcessId;
        processInfo->SessionId = currentProcess->SessionId;
        processInfo->State = 0; // 暂时设为0，表示运行状态
        processInfo->DirectoryTableBase = GetProcessDTB(processInfo->ProcessId);
        processInfo->VirtualAddress = 0; // R3无法获取EPROCESS地址
        
        // 处理进程名称 - 使用更安全的方式
        if (currentProcess->ImageName.Buffer && currentProcess->ImageName.Length > 0) {
            // 限制字符串长度避免缓冲区溢出
            USHORT nameLength = min(currentProcess->ImageName.Length / sizeof(WCHAR), 15);
            WCHAR tempName[16] = {0};
            
            // 安全复制字符串
            if (nameLength > 0) {
                memcpy(tempName, currentProcess->ImageName.Buffer, nameLength * sizeof(WCHAR));
                tempName[nameLength] = L'\0';
            }
            SafeWideCharToMultiByte(tempName, processInfo->ImageFileName, sizeof(processInfo->ImageFileName));
        } else {
            // 处理System Idle Process (PID 0) 或其他无名进程
            if (processInfo->ProcessId == 0) {
                strcpy_s(processInfo->ImageFileName, sizeof(processInfo->ImageFileName), "System Idle");
            } else if (processInfo->ProcessId == 4) {
                strcpy_s(processInfo->ImageFileName, sizeof(processInfo->ImageFileName), "System");
            } else {
                strcpy_s(processInfo->ImageFileName, sizeof(processInfo->ImageFileName), "<unknown>");
            }
        }
        
        // 清空保留字段
        ZeroMemory(processInfo->Reserved, sizeof(processInfo->Reserved));
        
        index++;
        
        if (currentProcess->NextEntryOffset == 0) {
            break;
        }
        
        currentProcess = (SYSTEM_PROCESS_INFORMATION_FULL*)(
            (PBYTE)currentProcess + currentProcess->NextEntryOffset
        );
    }

    // 清理系统缓冲区
    LocalFree(systemBuffer);

    // 设置输出参数
    *ppBuffer = outputBuffer;
    *pcbSize = outputSize;

    LogInfo("R3 Process enumeration completed successfully");
    {
        char logBuffer[256];
        sprintf_s(logBuffer, sizeof(logBuffer), 
                 "Enumerated %u processes using NtQuerySystemInformation, buffer size: %u bytes", 
                 processCount, outputSize);
        LogInfo(logBuffer);
    }

    VMProtectEnd();
    return TRUE;
}