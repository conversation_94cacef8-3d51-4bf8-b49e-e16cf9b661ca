---
alwaysApply: true
description: 大型 C/C++ 仓库的上下文与编辑策略（Cursor 专用）
---
# 上下文与编辑策略

- 精准定位
  - 先区分“定义”与“使用”，跨 `vmm`/`leechcore`/`CustomLeechServer` 层级再动手。
  - 跨模块更改优先新增最小接口适配层，避免大范围重构。

- 原子化修改
  - 小步快跑：按模块/文件分批；每步可验证、可回滚。
  - 保持头文件自洽与最小包含集；避免因包含链扩散导致的级联编译错误。

- 大文件
  - >500 行的文件分段处理；每段 50–100 行；修改后立即验证包含/宏影响面。
  - 尽量将改动限制在函数内部，不轻易扩散到公共头文件。

- Cursor 辅助
  - 使用 `@Recent Changes`、`@Definitions`、`@Lint Errors` 保持编辑连贯与可追踪。