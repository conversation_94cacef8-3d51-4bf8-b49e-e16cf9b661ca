#include "pe_utils.h"
#include "console_ui.h"
#include <winternl.h>
#include <sstream>

namespace PEUtils {

    // PEB遍历相关结构
    struct PEB_LDR_DATA {
        ULONG Length;
        BOOLEAN Initialized;
        PVOID SsHandle;
        LIST_ENTRY InLoadOrderModuleList;
        LIST_ENTRY InMemoryOrderModuleList;
        LIST_ENTRY InInitializationOrderModuleList;
    };

    struct LDR_DATA_TABLE_ENTRY {
        LIST_ENTRY InLoadOrderLinks;
        LIST_ENTRY InMemoryOrderLinks;
        LIST_ENTRY InInitializationOrderLinks;
        PVOID DllBase;
        PVOID EntryPoint;
        ULONG SizeOfImage;
        UNICODE_STRING FullDllName;
        UNICODE_STRING BaseDllName;
        ULONG Flags;
        SHORT LoadCount;
        SHORT TlsIndex;
        LIST_ENTRY HashLinks;
        PVOID SectionPointer;
        ULONG CheckSum;
        ULONG TimeDateStamp;
        PVOID LoadedImports;
        PVOID EntryPointActivationContext;
        PVOID PatchInformation;
    };

    struct PEB {
        BOOLEAN InheritedAddressSpace;
        BOOLEAN ReadImageFileExecOptions;
        BOOLEAN BeingDebugged;
        BOOLEAN SpareBool;
        HANDLE Mutant;
        PVOID ImageBaseAddress;
        PEB_LDR_DATA* Ldr;
        // ... 其他字段
    };

    HMODULE FindModuleByName(const std::string& moduleName) {
        PEB* peb = (PEB*)__readgsqword(0x60); // x64
        if (!peb || !peb->Ldr) return nullptr;

        LIST_ENTRY* head = &peb->Ldr->InLoadOrderModuleList;
        LIST_ENTRY* current = head->Flink;

        while (current != head) {
            LDR_DATA_TABLE_ENTRY* entry = CONTAINING_RECORD(current, LDR_DATA_TABLE_ENTRY, InLoadOrderLinks);
            
            if (entry->BaseDllName.Buffer) {
                // 转换为ANSI字符串
                int len = WideCharToMultiByte(CP_ACP, 0, entry->BaseDllName.Buffer, 
                                            entry->BaseDllName.Length / sizeof(WCHAR), 
                                            nullptr, 0, nullptr, nullptr);
                if (len > 0) {
                    std::vector<char> ansiName(len + 1);
                    WideCharToMultiByte(CP_ACP, 0, entry->BaseDllName.Buffer,
                                      entry->BaseDllName.Length / sizeof(WCHAR),
                                      ansiName.data(), len, nullptr, nullptr);
                    ansiName[len] = '\0';

                    if (_stricmp(ansiName.data(), moduleName.c_str()) == 0) {
                        return (HMODULE)entry->DllBase;
                    }
                }
            }
            
            current = current->Flink;
        }

        return nullptr;
    }

    HMODULE FindModuleByAddress(PVOID address) {
        PEB* peb = (PEB*)__readgsqword(0x60);
        if (!peb || !peb->Ldr) return nullptr;

        LIST_ENTRY* head = &peb->Ldr->InLoadOrderModuleList;
        LIST_ENTRY* current = head->Flink;

        while (current != head) {
            LDR_DATA_TABLE_ENTRY* entry = CONTAINING_RECORD(current, LDR_DATA_TABLE_ENTRY, InLoadOrderLinks);
            
            DWORD_PTR moduleBase = (DWORD_PTR)entry->DllBase;
            DWORD_PTR moduleEnd = moduleBase + entry->SizeOfImage;
            DWORD_PTR addr = (DWORD_PTR)address;

            if (addr >= moduleBase && addr < moduleEnd) {
                return (HMODULE)entry->DllBase;
            }
            
            current = current->Flink;
        }

        return nullptr;
    }

    std::vector<ModuleInfo> EnumerateLoadedModules() {
        std::vector<ModuleInfo> modules;
        
        PEB* peb = (PEB*)__readgsqword(0x60);
        if (!peb || !peb->Ldr) return modules;

        LIST_ENTRY* head = &peb->Ldr->InLoadOrderModuleList;
        LIST_ENTRY* current = head->Flink;

        while (current != head) {
            LDR_DATA_TABLE_ENTRY* entry = CONTAINING_RECORD(current, LDR_DATA_TABLE_ENTRY, InLoadOrderLinks);
            
            ModuleInfo info;
            info.baseAddress = (HMODULE)entry->DllBase;
            info.imageSize = entry->SizeOfImage;
            info.entryPoint = entry->EntryPoint;

            // 获取模块名
            if (entry->BaseDllName.Buffer) {
                int len = WideCharToMultiByte(CP_ACP, 0, entry->BaseDllName.Buffer, 
                                            entry->BaseDllName.Length / sizeof(WCHAR), 
                                            nullptr, 0, nullptr, nullptr);
                if (len > 0) {
                    std::vector<char> ansiName(len + 1);
                    WideCharToMultiByte(CP_ACP, 0, entry->BaseDllName.Buffer,
                                      entry->BaseDllName.Length / sizeof(WCHAR),
                                      ansiName.data(), len, nullptr, nullptr);
                    ansiName[len] = '\0';
                    info.name = ansiName.data();
                }
            }

            // 获取完整路径
            if (entry->FullDllName.Buffer) {
                int len = WideCharToMultiByte(CP_ACP, 0, entry->FullDllName.Buffer, 
                                            entry->FullDllName.Length / sizeof(WCHAR), 
                                            nullptr, 0, nullptr, nullptr);
                if (len > 0) {
                    std::vector<char> ansiPath(len + 1);
                    WideCharToMultiByte(CP_ACP, 0, entry->FullDllName.Buffer,
                                      entry->FullDllName.Length / sizeof(WCHAR),
                                      ansiPath.data(), len, nullptr, nullptr);
                    ansiPath[len] = '\0';
                    info.fullPath = ansiPath.data();
                }
            }

            modules.push_back(info);
            current = current->Flink;
        }

        return modules;
    }

    ModuleInfo GetModuleInfo(HMODULE module) {
        ModuleInfo info;
        if (!module) return info;

        info.baseAddress = module;
        
        PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(module);
        if (ntHeaders) {
            info.imageSize = ntHeaders->OptionalHeader.SizeOfImage;
            info.entryPoint = (BYTE*)module + ntHeaders->OptionalHeader.AddressOfEntryPoint;
        }

        info.name = GetModuleName(module);
        info.fullPath = GetModulePath(module);

        return info;
    }

    bool IsValidPE(HMODULE module) {
        if (!module) return false;

        __try {
            PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)module;
            if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) return false;

            PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)((BYTE*)module + dosHeader->e_lfanew);
            return (ntHeaders->Signature == IMAGE_NT_SIGNATURE);
        }
        __except (EXCEPTION_EXECUTE_HANDLER) {
            return false;
        }
    }

    PIMAGE_DOS_HEADER GetDosHeader(HMODULE module) {
        if (!IsValidPE(module)) return nullptr;
        return (PIMAGE_DOS_HEADER)module;
    }

    PIMAGE_NT_HEADERS GetNtHeaders(HMODULE module) {
        PIMAGE_DOS_HEADER dosHeader = GetDosHeader(module);
        if (!dosHeader) return nullptr;
        
        return (PIMAGE_NT_HEADERS)((BYTE*)module + dosHeader->e_lfanew);
    }

    PIMAGE_SECTION_HEADER GetSectionHeaders(HMODULE module) {
        PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(module);
        if (!ntHeaders) return nullptr;
        
        return IMAGE_FIRST_SECTION(ntHeaders);
    }

    std::vector<ExportInfo> ParseExportTable(HMODULE module) {
        std::vector<ExportInfo> exports;
        
        PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(module);
        if (!ntHeaders) return exports;

        DWORD exportRva = ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
        if (!exportRva) return exports;

        PIMAGE_EXPORT_DIRECTORY exportDir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)module + exportRva);
        
        DWORD* nameRvas = (DWORD*)((BYTE*)module + exportDir->AddressOfNames);
        DWORD* functionRvas = (DWORD*)((BYTE*)module + exportDir->AddressOfFunctions);
        WORD* ordinals = (WORD*)((BYTE*)module + exportDir->AddressOfNameOrdinals);

        // 导出的函数
        for (DWORD i = 0; i < exportDir->NumberOfFunctions; ++i) {
            if (!functionRvas[i]) continue;

            ExportInfo info;
            info.ordinal = (WORD)(exportDir->Base + i);
            info.address = (BYTE*)module + functionRvas[i];

            // 检查是否为转发器
            DWORD exportDirStart = exportRva;
            DWORD exportDirEnd = exportRva + ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].Size;
            DWORD funcRva = functionRvas[i];
            
            if (funcRva >= exportDirStart && funcRva < exportDirEnd) {
                info.isForwarder = true;
                info.forwarderName = (char*)((BYTE*)module + funcRva);
            }

            // 查找对应的名称
            for (DWORD j = 0; j < exportDir->NumberOfNames; ++j) {
                if (ordinals[j] == i) {
                    info.name = (char*)((BYTE*)module + nameRvas[j]);
                    break;
                }
            }

            exports.push_back(info);
        }

        return exports;
    }

    PVOID GetExportAddress(HMODULE module, const std::string& functionName) {
        PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(module);
        if (!ntHeaders) return nullptr;

        DWORD exportRva = ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
        if (!exportRva) return nullptr;

        PIMAGE_EXPORT_DIRECTORY exportDir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)module + exportRva);
        
        DWORD* nameRvas = (DWORD*)((BYTE*)module + exportDir->AddressOfNames);
        DWORD* functionRvas = (DWORD*)((BYTE*)module + exportDir->AddressOfFunctions);
        WORD* ordinals = (WORD*)((BYTE*)module + exportDir->AddressOfNameOrdinals);

        for (DWORD i = 0; i < exportDir->NumberOfNames; ++i) {
            const char* name = (char*)((BYTE*)module + nameRvas[i]);
            if (strcmp(name, functionName.c_str()) == 0) {
                WORD ordinal = ordinals[i];
                if (ordinal < exportDir->NumberOfFunctions && functionRvas[ordinal]) {
                    return (BYTE*)module + functionRvas[ordinal];
                }
            }
        }

        return nullptr;
    }

    PVOID GetExportAddressByOrdinal(HMODULE module, WORD ordinal) {
        PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(module);
        if (!ntHeaders) return nullptr;

        DWORD exportRva = ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
        if (!exportRva) return nullptr;

        PIMAGE_EXPORT_DIRECTORY exportDir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)module + exportRva);
        DWORD* functionRvas = (DWORD*)((BYTE*)module + exportDir->AddressOfFunctions);

        if (ordinal >= exportDir->Base && ordinal < exportDir->Base + exportDir->NumberOfFunctions) {
            DWORD index = ordinal - exportDir->Base;
            if (functionRvas[index]) {
                return (BYTE*)module + functionRvas[index];
            }
        }

        return nullptr;
    }

    std::string ResolveForwarder(const std::string& forwarder) {
        size_t dotPos = forwarder.find('.');
        if (dotPos == std::string::npos) return "";

        std::string dllName = forwarder.substr(0, dotPos) + ".dll";
        std::string funcName = forwarder.substr(dotPos + 1);

        HMODULE forwardModule = LoadLibraryA(dllName.c_str());
        if (!forwardModule) return "";

        PVOID addr = GetProcAddress(forwardModule, funcName.c_str());
        if (addr) {
            std::stringstream ss;
            ss << "0x" << std::hex << (DWORD_PTR)addr;
            return ss.str();
        }

        return "";
    }

    std::vector<SectionInfo> GetSections(HMODULE module) {
        std::vector<SectionInfo> sections;
        
        PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(module);
        if (!ntHeaders) return sections;

        PIMAGE_SECTION_HEADER sectionHeader = IMAGE_FIRST_SECTION(ntHeaders);
        
        for (WORD i = 0; i < ntHeaders->FileHeader.NumberOfSections; ++i) {
            SectionInfo info;
            
            // 节名称（最多8字符）
            char sectionName[9] = {0};
            memcpy(sectionName, sectionHeader[i].Name, 8);
            info.name = sectionName;
            
            info.virtualAddress = (BYTE*)module + sectionHeader[i].VirtualAddress;
            info.virtualSize = sectionHeader[i].Misc.VirtualSize;
            info.rawSize = sectionHeader[i].SizeOfRawData;
            info.characteristics = sectionHeader[i].Characteristics;
            
            // 权限分析
            info.isExecutable = (info.characteristics & IMAGE_SCN_MEM_EXECUTE) != 0;
            info.isWritable = (info.characteristics & IMAGE_SCN_MEM_WRITE) != 0;
            info.isReadable = (info.characteristics & IMAGE_SCN_MEM_READ) != 0;
            
            sections.push_back(info);
        }

        return sections;
    }

    SectionInfo* FindSection(HMODULE module, const std::string& sectionName) {
        static std::vector<SectionInfo> sections; // 静态存储以保持引用有效
        sections = GetSections(module);
        
        for (auto& section : sections) {
            if (section.name == sectionName) {
                return &section;
            }
        }
        
        return nullptr;
    }

    PVOID GetSectionAddress(HMODULE module, const std::string& sectionName) {
        SectionInfo* section = FindSection(module, sectionName);
        return section ? section->virtualAddress : nullptr;
    }

    SIZE_T GetSectionSize(HMODULE module, const std::string& sectionName) {
        SectionInfo* section = FindSection(module, sectionName);
        return section ? section->virtualSize : 0;
    }

    DWORD RvaToFileOffset(HMODULE module, DWORD rva) {
        PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(module);
        if (!ntHeaders) return 0;

        PIMAGE_SECTION_HEADER sectionHeader = IMAGE_FIRST_SECTION(ntHeaders);
        
        for (WORD i = 0; i < ntHeaders->FileHeader.NumberOfSections; ++i) {
            DWORD sectionStart = sectionHeader[i].VirtualAddress;
            DWORD sectionEnd = sectionStart + sectionHeader[i].Misc.VirtualSize;
            
            if (rva >= sectionStart && rva < sectionEnd) {
                return rva - sectionStart + sectionHeader[i].PointerToRawData;
            }
        }

        return 0;
    }

    DWORD FileOffsetToRva(HMODULE module, DWORD offset) {
        PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(module);
        if (!ntHeaders) return 0;

        PIMAGE_SECTION_HEADER sectionHeader = IMAGE_FIRST_SECTION(ntHeaders);
        
        for (WORD i = 0; i < ntHeaders->FileHeader.NumberOfSections; ++i) {
            DWORD rawStart = sectionHeader[i].PointerToRawData;
            DWORD rawEnd = rawStart + sectionHeader[i].SizeOfRawData;
            
            if (offset >= rawStart && offset < rawEnd) {
                return offset - rawStart + sectionHeader[i].VirtualAddress;
            }
        }

        return 0;
    }

    PVOID RvaToVa(HMODULE module, DWORD rva) {
        return (BYTE*)module + rva;
    }

    PVOID GetEntryPoint(HMODULE module) {
        PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(module);
        if (!ntHeaders) return nullptr;
        
        return (BYTE*)module + ntHeaders->OptionalHeader.AddressOfEntryPoint;
    }

    PVOID GetOriginalEntryPoint(HMODULE module) {
        // 与GetEntryPoint相同，除非有特殊处理
        return GetEntryPoint(module);
    }

    std::string GetModulePath(HMODULE module) {
        char path[MAX_PATH] = {0};
        if (GetModuleFileNameA(module, path, MAX_PATH)) {
            return std::string(path);
        }
        return "";
    }

    std::string GetModuleName(HMODULE module) {
        std::string fullPath = GetModulePath(module);
        if (fullPath.empty()) return "";
        
        size_t lastSlash = fullPath.find_last_of("\\/");
        if (lastSlash != std::string::npos) {
            return fullPath.substr(lastSlash + 1);
        }
        
        return fullPath;
    }

    DWORD GetModuleSize(HMODULE module) {
        PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(module);
        if (!ntHeaders) return 0;
        
        return ntHeaders->OptionalHeader.SizeOfImage;
    }

    DWORD GetImageChecksum(HMODULE module) {
        PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(module);
        if (!ntHeaders) return 0;
        
        return ntHeaders->OptionalHeader.CheckSum;
    }

    DWORD CalculateChecksum(HMODULE module) {
        // 简化的校验和计算，实际应该使用MapFileAndCheckSum等API
        DWORD size = GetModuleSize(module);
        if (!size) return 0;
        
        DWORD checksum = 0;
        WORD* data = (WORD*)module;
        
        for (DWORD i = 0; i < size / 2; ++i) {
            checksum += data[i];
            checksum = (checksum >> 16) + (checksum & 0xFFFF);
        }
        
        checksum += size;
        return checksum;
    }

    // 基于PEB的GetProcAddress实现，避免调用系统API
    FARPROC GetProcAddressPEB(HMODULE hModule, const char* lpProcName) {
        if (!hModule || !lpProcName) return nullptr;

        // 直接使用原始PE解析，避免在__try中创建C++对象
        PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(hModule);
        if (!ntHeaders) return nullptr;

        DWORD exportRva = ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
        if (!exportRva) return nullptr;

        PIMAGE_EXPORT_DIRECTORY exportDir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)hModule + exportRva);
        
        DWORD* nameRvas = (DWORD*)((BYTE*)hModule + exportDir->AddressOfNames);
        DWORD* functionRvas = (DWORD*)((BYTE*)hModule + exportDir->AddressOfFunctions);
        WORD* ordinals = (WORD*)((BYTE*)hModule + exportDir->AddressOfNameOrdinals);

        for (DWORD i = 0; i < exportDir->NumberOfNames; ++i) {
            const char* name = (char*)((BYTE*)hModule + nameRvas[i]);
            if (strcmp(name, lpProcName) == 0) {
                WORD ordinal = ordinals[i];
                if (ordinal < exportDir->NumberOfFunctions && functionRvas[ordinal]) {
                    return (FARPROC)((BYTE*)hModule + functionRvas[ordinal]);
                }
            }
        }

        return nullptr;
    }

    // 内部辅助函数：通过模块名查找模块（C字符串版本）
    HMODULE FindModuleByNameA(const char* moduleName) {
        if (!moduleName) return nullptr;
        
        PEB* peb = (PEB*)__readgsqword(0x60); // x64
        if (!peb || !peb->Ldr) return nullptr;

        LIST_ENTRY* head = &peb->Ldr->InLoadOrderModuleList;
        LIST_ENTRY* current = head->Flink;

        while (current != head) {
            LDR_DATA_TABLE_ENTRY* entry = CONTAINING_RECORD(current, LDR_DATA_TABLE_ENTRY, InLoadOrderLinks);
            
            if (entry->BaseDllName.Buffer) {
                // 转换为ANSI字符串
                int len = WideCharToMultiByte(CP_ACP, 0, entry->BaseDllName.Buffer, 
                                            entry->BaseDllName.Length / sizeof(WCHAR), 
                                            nullptr, 0, nullptr, nullptr);
                if (len > 0) {
                    char ansiName[MAX_PATH];
                    if (len < MAX_PATH) {
                        WideCharToMultiByte(CP_ACP, 0, entry->BaseDllName.Buffer,
                                          entry->BaseDllName.Length / sizeof(WCHAR),
                                          ansiName, len, nullptr, nullptr);
                        ansiName[len] = '\0';

                        if (_stricmp(ansiName, moduleName) == 0) {
                            return (HMODULE)entry->DllBase;
                        }
                    }
                }
            }
            
            current = current->Flink;
        }

        return nullptr;
    }

    // 通过模块名获取模块句柄并查找函数地址
    FARPROC GetProcAddressByModuleName(const char* moduleName, const char* functionName) {
        if (!moduleName || !functionName) return nullptr;

        // 使用PEB查找模块
        HMODULE hModule = FindModuleByNameA(moduleName);
        if (!hModule) return nullptr;

        return GetProcAddressPEB(hModule, functionName);
    }

    // 完全基于PEB的GetModuleHandle实现
    HMODULE GetModuleHandlePEB(const char* lpModuleName) {
        if (!lpModuleName) return nullptr;
        return FindModuleByNameA(lpModuleName);
    }

    // 安全的字符串比较（避免导入API）
    bool SafeStringCompareA(const char* str1, const char* str2) {
        if (!str1 || !str2) return false;
        
        while (*str1 && *str2) {
            if (*str1 != *str2) return false;
            str1++;
            str2++;
        }
        return (*str1 == *str2);
    }

    // 不区分大小写的字符串比较
    bool SafeStringCompareIA(const char* str1, const char* str2) {
        if (!str1 || !str2) return false;
        
        while (*str1 && *str2) {
            char c1 = *str1;
            char c2 = *str2;
            
            // 转为小写
            if (c1 >= 'A' && c1 <= 'Z') c1 += 32;
            if (c2 >= 'A' && c2 <= 'Z') c2 += 32;
            
            if (c1 != c2) return false;
            str1++;
            str2++;
        }
        return (*str1 == *str2);
    }

    // 获取系统目录（手动实现，避免调用GetSystemDirectory）
    std::string GetSystemDirectoryPath() {
        // 通过PEB获取系统路径
        PEB* peb = (PEB*)__readgsqword(0x60);
        if (!peb) return "C:\\Windows\\System32\\";

        // 简化实现：返回标准系统目录
        return "C:\\Windows\\System32\\";
    }

    // 手动加载DLL（基于PEB，不使用LoadLibrary）
    HMODULE LoadLibraryPEB(const char* dllName) {
        if (!dllName) return nullptr;

        // 首先检查是否已经加载
        HMODULE existingModule = FindModuleByNameA(dllName);
        if (existingModule) return existingModule;

        // 如果没有加载，尝试从系统目录加载
        // 注意：这是一个简化实现，实际的DLL加载需要更复杂的逻辑
        // 在这里我们返回nullptr，让调用者知道模块未加载
        return nullptr;
    }

    // 高级函数：通过多种方式查找函数地址
    FARPROC FindFunctionAddress(const char* moduleName, const char* functionName) {
        if (!moduleName || !functionName) return nullptr;

        // 方法1：直接通过PEB查找已加载的模块
        HMODULE hModule = FindModuleByNameA(moduleName);
        if (hModule) {
            FARPROC addr = GetProcAddressPEB(hModule, functionName);
            if (addr) return addr;
        }

        // 方法2：尝试添加.dll扩展名
        char dllName[MAX_PATH];
        strcpy_s(dllName, MAX_PATH, moduleName);
        
        // 检查是否已经有.dll扩展名
        size_t nameLen = strlen(moduleName);
        if (nameLen < 4 || _stricmp(moduleName + nameLen - 4, ".dll") != 0) {
            // 添加.dll扩展名
            strcat_s(dllName, MAX_PATH, ".dll");
            hModule = FindModuleByNameA(dllName);
            if (hModule) {
                FARPROC addr = GetProcAddressPEB(hModule, functionName);
                if (addr) return addr;
            }
        }

        return nullptr;
    }

    // 调试辅助函数：枚举模块的所有导出函数
    void EnumerateModuleExports(HMODULE hModule) {
        if (!hModule) return;

        std::vector<ExportInfo> exports = ParseExportTable(hModule);
        
        std::string moduleName = GetModuleName(hModule);
        #ifdef DEBUGLOG
        LOG_INFO("=== Module %s Exports (%zu functions) ===", 
                    moduleName.c_str(), exports.size());
        #endif

        for (const auto& exp : exports) {
            if (!exp.name.empty()) {
                #ifdef DEBUGLOG
                LOG_INFO("  %s @ 0x%p (ordinal %u)", 
                           exp.name.c_str(), exp.address, exp.ordinal);
                #endif
            } else {
                #ifdef DEBUGLOG
                LOG_INFO("  [Ordinal %u] @ 0x%p", exp.ordinal, exp.address);
                #endif
            }
        }
    }

} // namespace PEUtils