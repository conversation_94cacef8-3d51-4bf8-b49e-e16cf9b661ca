@echo off
echo ========================================
echo    Scatter Test Compilation Script
echo ========================================
echo.

REM 设置 Clang 路径
set CLANG_PATH=C:\llvm-msvc-build\bin\clang.exe

REM 检查 Clang 是否存在
if not exist "%CLANG_PATH%" (
    echo ERROR: Clang not found at %CLANG_PATH%
    echo Please check your Clang installation path
    pause
    exit /b 1
)

echo Found Clang at: %CLANG_PATH%
echo.

REM 设置编译参数
set CXX_STD=c++17
set OPTIMIZATION=-O2
set OUTPUT_NAME=test_scatter_perf.exe
set SOURCE_FILE=test_scatter_perf.cpp

echo Compiling %SOURCE_FILE%...
echo Target: %OUTPUT_NAME%
echo Standard: %CXX_STD%
echo Optimization: %OPTIMIZATION%
echo.

REM 执行编译
"%CLANG_PATH%" -std=%CXX_STD% %OPTIMIZATION% -fuse-ld=lld -o %OUTPUT_NAME% %SOURCE_FILE%

REM 检查编译结果
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo    Compilation SUCCESSFUL!
    echo ========================================
    echo.
    echo Output file: %OUTPUT_NAME%
    echo.
    echo You can now run: %OUTPUT_NAME%
    echo.
) else (
    echo.
    echo ========================================
    echo    Compilation FAILED!
    echo ========================================
    echo.
    echo Error code: %ERRORLEVEL%
    echo Please check the error messages above
    echo.
)

pause
