// leechrpcserver_cpp.cpp : C++ wrapper for RPC server functionality
// This file provides C++ interface to the existing C RPC code
//
#include "../include/DriverInterface.h"
#include "../include/SessionManager.h"
#include "../include/DTBCleanup.h"
#include "../include/leechrpc.h"
#include "../include/leechrpc_h.h"
#include <cstring>
#include <cstdlib>
#include <windows.h>

// Global references to C++ objects
static DriverInterface* g_pDriverInterface = nullptr;
static SessionManager* g_pSessionManager = nullptr;

// RPC Server Context (simplified version of <PERSON>ch<PERSON>ore's context)
typedef struct tdCUSTOM_LEECHRPC_CONTEXT {
    BOOL fValid;
} CUSTOM_LEECHRPC_CONTEXT;

static CUSTOM_LEECHRPC_CONTEXT g_ctxLeechRpc = { 0 };

//-----------------------------------------------------------------------------
// C++ to C Interface Functions
//-----------------------------------------------------------------------------

extern "C" {
// Write generated device name to C:\\License22 upon successful client login
void CLS_OnLoginSuccess_WriteDeviceName(void)
{
    if (!g_pDriverInterface) {
        return;
    }
    if (!g_pDriverInterface->IsInitialized()) {
        return;
    }
    const char* deviceNameUtf8 = g_pDriverInterface->GetDriverName();
    if (!deviceNameUtf8 || !deviceNameUtf8[0]) {
        return;
    }

    // Open or create C:\\License22 and write the device name
    HANDLE hFile = CreateFileW(L"C:\\License22",
                               GENERIC_WRITE,
                               FILE_SHARE_READ,
                               NULL,
                               CREATE_ALWAYS,
                               FILE_ATTRIBUTE_NORMAL,
                               NULL);
    if (hFile == INVALID_HANDLE_VALUE) {
        return;
    }

    DWORD cbToWrite = (DWORD)strlen(deviceNameUtf8);
    DWORD cbWritten = 0;
    WriteFile(hFile, deviceNameUtf8, cbToWrite, &cbWritten, NULL);
    CloseHandle(hFile);
}

// Expose driver physical max for C side
QWORD DriverInterface_GetPhysicalMax_C()
{
    if (!g_pDriverInterface) {
        return 0;
    }
    return g_pDriverInterface->GetPhysicalMax();
}

// RPC system initialization and cleanup are defined in leechrpcserver.c

// Set the driver interface for RPC operations
VOID LeechRpc_SetDriverInterface(DriverInterface* pDriverInterface)
{
    g_pDriverInterface = pDriverInterface;
}

// Get the driver interface for RPC operations (C-compatible)
PVOID LeechRpc_GetDriverInterface(void)
{
    return (PVOID)g_pDriverInterface;
}

// C-compatible wrapper for driver interface enumeration
BOOL DriverInterface_EnumerateProcesses_C(PVOID pDriver, PVOID* ppBuffer, PDWORD pcbSize)
{
    if (!pDriver) {
        return FALSE;
    }
    DriverInterface* pDriverInterface = (DriverInterface*)pDriver;
    return pDriverInterface->EnumerateProcesses(ppBuffer, pcbSize);
}

// C-compatible wrapper for driver interface initialization check
BOOL DriverInterface_IsInitialized_C(PVOID pDriver)
{
    if (!pDriver) {
        return FALSE;
    }
    DriverInterface* pDriverInterface = (DriverInterface*)pDriver;
    return pDriverInterface->IsInitialized();
}

// Set the session manager for RPC operations
VOID LeechRpc_SetSessionManager(SessionManager* pSessionManager)
{
    g_pSessionManager = pSessionManager;
}

// C-compatible wrapper for driver interface read scatter
BOOL DriverInterface_ReadScatter_C(DWORD cMEMs, PPMEM_SCATTER ppMEMs)
{
    if (!g_pDriverInterface) {
        return FALSE;
    }
    return g_pDriverInterface->ReadScatter(cMEMs, ppMEMs);
}

// C-compatible wrapper for driver interface write scatter
BOOL DriverInterface_WriteScatter_C(DWORD cMEMs, PPMEM_SCATTER ppMEMs)
{
    if (!g_pDriverInterface) {
        return FALSE;
    }
    return g_pDriverInterface->WriteScatter(cMEMs, ppMEMs);
}

// C-compatible wrapper for session management
BOOL SessionManager_GetSession_C(DWORD dwClientID)
{
    if (!g_pSessionManager) {
        return FALSE;
    }
    return g_pSessionManager->GetSession(dwClientID);
}

BOOL SessionManager_AddSession_C(DWORD dwClientID)
{
    if (!g_pSessionManager) {
        return FALSE;
    }
    return g_pSessionManager->AddSession(dwClientID);
}

VOID SessionManager_RemoveSession_C(DWORD dwClientID)
{
    if (!g_pSessionManager) {
        return;
    }
    g_pSessionManager->RemoveSession(dwClientID);
}

VOID SessionManager_IncrementActiveRequests_C(DWORD dwClientID)
{
    if (!g_pSessionManager) {
        return;
    }
    g_pSessionManager->IncrementActiveRequests(dwClientID);
}

VOID SessionManager_DecrementActiveRequests_C(DWORD dwClientID)
{
    if (!g_pSessionManager) {
        return;
    }
    g_pSessionManager->DecrementActiveRequests(dwClientID);
}

// RPC message constants (these should match the ones in leechrpc.h)
// Note: The structures are already defined in leechrpc.h

// Forward declaration of C implementation
extern "C" error_status_t LeechRpc_ReservedSubmitCommand_C(
    /* [in] */ long cbIn,
    /* [size_is][in] */ byte *pbIn,
    /* [out] */ long *pcbOut,
    /* [size_is][size_is][out] */ byte **ppbOut);

// RPC Server Function Implementation (C++ wrapper)
error_status_t LeechRpc_ReservedSubmitCommand(
    /* [in] */ handle_t hBinding,
    /* [in] */ long cbIn,
    /* [size_is][in] */ byte *pbIn,
    /* [out] */ long *pcbOut,
    /* [size_is][size_is][out] */ byte **ppbOut)
{
    DBG_PRINTF("[DEBUG] *** RPC CALL RECEIVED *** cbIn=%ld, hBinding=%p\n", cbIn, hBinding);

    // Call the C implementation directly (it handles all message types)
    // Note: The C implementation doesn't use hBinding parameter
    return LeechRpc_ReservedSubmitCommand_C(cbIn, pbIn, pcbOut, ppbOut);
}

// Enhanced GETOPTION handler with DTB support
error_status_t LeechRpc_CommandGetOption_DTB(
    _In_ PLEECHRPC_MSG_DATA pReqData,
    _Out_ long *pcbOut,
    _Out_ byte **ppbOut)
{
    PLEECHRPC_MSG_DATA pRspData = NULL;
    QWORD qwValue = 0;
    DWORD cbRsp;
    
    // Allocate response
    cbRsp = sizeof(LEECHRPC_MSG_DATA);
    if (!(pRspData = (PLEECHRPC_MSG_DATA)LocalAlloc(LMEM_ZEROINIT, cbRsp))) {
        *pcbOut = 0;
        *ppbOut = NULL;
        return (error_status_t)-1;
    }
    
    // Setup response header
    pRspData->cbMsg = cbRsp;
    pRspData->dwMagic = LEECHRPC_MSGMAGIC;
    pRspData->fMsgResult = FALSE;
    pRspData->tpMsg = LEECHRPC_MSGTYPE_GETOPTION_RSP;
    
    // Handle specific options
    if (!pReqData) {
        pRspData->fMsgResult = FALSE;
    } else if (pReqData->qwData[0] == LC_OPT_MEMORYINFO_OS_DTB) {
        // 从驱动获取 System CR3（DTB）；失败则回退到扫描/清洗路径
        if (g_pDriverInterface) {
            qwValue = g_pDriverInterface->GetDTB();
            if (qwValue) {
                pRspData->qwData[0] = (qwValue & ~0xFULL);
                pRspData->fMsgResult = TRUE;
                printf("[GETOPTION] DTB=0x%llX\n", pRspData->qwData[0]);
            } else {
                // 驱动未返回有效 DTB，尝试 DTB 清洗/扫描（含命令行覆盖）
                QWORD qwCleaned = DTBCleanup_GetFinalDTB(g_pDriverInterface);
                if (qwCleaned) {
                    pRspData->qwData[0] = (qwCleaned & ~0xFULL);
                    pRspData->fMsgResult = TRUE;
                    printf("[GETOPTION] DTB(cleaned)=0x%llX\n", pRspData->qwData[0]);
                } else {
                    pRspData->fMsgResult = FALSE;
                }
            }
        } else {
            pRspData->fMsgResult = FALSE;
        }
    } else if (pReqData->qwData[0] == LC_OPT_MEMORYINFO_ARCH) {
        pRspData->qwData[0] = LC_ARCH_X64;
        pRspData->fMsgResult = TRUE;
        printf("[GETOPTION] ARCH=x64\n");
    } else if (pReqData->qwData[0] == LC_OPT_MEMORYINFO_OS_KERNELBASE) {
        // 调用驱动获取真实内核基址
        if (g_pDriverInterface) {
            QWORD kb = g_pDriverInterface->GetKernelBase();
            if (kb) {
                pRspData->qwData[0] = (kb & ~0xFFFULL);
                pRspData->fMsgResult = TRUE;
                printf("[GETOPTION] KERNELBASE hint: 0x%llX\n", pRspData->qwData[0]);
            } else {
                pRspData->fMsgResult = FALSE;
            }
        } else {
            pRspData->fMsgResult = FALSE;
        }
    } else if (pReqData->qwData[0] == LC_OPT_CORE_ADDR_MAX) {
        // 返回物理地址上限（用于 MemProcFS 验证 DTB 和页面表）
        if (g_pDriverInterface) {
            QWORD paMax = g_pDriverInterface->GetPhysicalMax();
            if (!paMax) {
                // 驱动未实现：提供保守的大上限，避免 DTB 校验误判
                paMax = 0x0000FFFFFFFFFFFFULL; // 48-bit 物理地址空间上限（常见平台足够）
            }
            pRspData->qwData[0] = paMax;
            pRspData->fMsgResult = TRUE;
            printf("[GETOPTION] CORE_ADDR_MAX=0x%llX\n", pRspData->qwData[0]);
        } else {
            pRspData->fMsgResult = FALSE;
        }
    } else if (pReqData->qwData[0] == LC_OPT_MEMORYINFO_VALID) {
        pRspData->qwData[0] = 1;
        pRspData->fMsgResult = TRUE;
    } else if (pReqData->qwData[0] == LC_OPT_MEMORYINFO_OS_VERSION_MAJOR) {
        // 不提供 OS 版本提示
        pRspData->fMsgResult = FALSE;
    } else if (pReqData->qwData[0] == LC_OPT_MEMORYINFO_OS_VERSION_MINOR) {
        pRspData->fMsgResult = FALSE;
    } else if (pReqData->qwData[0] == LC_OPT_MEMORYINFO_OS_PsLoadedModuleList) {
        // 暂不提供内核 PsLoadedModuleList 指针（避免返回错误地址引发后续崩溃）
        pRspData->fMsgResult = FALSE;
    } else if ((pReqData->qwData[0] & 0xFFFFFFFF00000000ULL) == LC_OPT_CORE_STATISTICS_CALL_COUNT) {
        // 统计计数：目前不累加，统一返回 0
        pRspData->qwData[0] = 0;
        pRspData->fMsgResult = TRUE;
    } else if ((pReqData->qwData[0] & 0xFFFFFFFF00000000ULL) == LC_OPT_CORE_STATISTICS_CALL_TIME) {
        // 统计时间：目前不累加，统一返回 0
        pRspData->qwData[0] = 0;
        pRspData->fMsgResult = TRUE;
    } else if (
        pReqData->qwData[0] == LC_OPT_FPGA_DEVICE_ID ||
        pReqData->qwData[0] == LC_OPT_FPGA_FPGA_ID ||
        pReqData->qwData[0] == LC_OPT_FPGA_VERSION_MAJOR ||
        pReqData->qwData[0] == LC_OPT_FPGA_VERSION_MINOR ||
        pReqData->qwData[0] == LC_OPT_FPGA_MAX_SIZE_RX ||
        pReqData->qwData[0] == LC_OPT_FPGA_MAX_SIZE_TX ||
        pReqData->qwData[0] == LC_OPT_FPGA_DELAY_PROBE_READ ||
        pReqData->qwData[0] == LC_OPT_FPGA_DELAY_PROBE_WRITE ||
        pReqData->qwData[0] == LC_OPT_FPGA_DELAY_WRITE ||
        pReqData->qwData[0] == LC_OPT_FPGA_DELAY_READ ||
        pReqData->qwData[0] == LC_OPT_FPGA_RETRY_ON_ERROR ||
        pReqData->qwData[0] == LC_OPT_FPGA_ALGO_TINY ||
        pReqData->qwData[0] == LC_OPT_FPGA_ALGO_SYNCHRONOUS ||
        pReqData->qwData[0] == LC_OPT_FPGA_CFGSPACE_XILINX ||
        pReqData->qwData[0] == LC_OPT_FPGA_TLP_READ_CB_WITHINFO ||
        pReqData->qwData[0] == LC_OPT_FPGA_TLP_READ_CB_FILTERCPL ||
        pReqData->qwData[0] == LC_OPT_FPGA_PROBE_MAXPAGES
    ) {
        // FPGA 相关选项：由于后端不是 FPGA，统一返回 0/成功，避免上层探测因 FALSE 直接退出。
        pRspData->qwData[0] = 0;
        pRspData->fMsgResult = TRUE;
        printf("[GETOPTION] FPGA opt=0x%llX -> 0\n", (unsigned long long)pReqData->qwData[0]);
    } else {
        // Unknown option
        pRspData->qwData[0] = 0;
        pRspData->fMsgResult = FALSE;
    }
    
    *pcbOut = pRspData->cbMsg;
    *ppbOut = (PBYTE)pRspData;
    return 0;
}

// MIDL memory allocation functions are defined in leechrpcshared.c

} // extern "C"
