#pragma once

#include "Common.h"

#define MAX_CLIENTS 16
#define SESSION_TIMEOUT_MS (60 * 1000)

// SessionManager class - manages client sessions and connections
class SessionManager {
private:
    struct ClientSession {
        DWORD dwClientID;
        QWORD qwLastActivity;
        DWORD cActiveRequests;
        BOOL fActive;
    };

    ClientSession sessions[MAX_CLIENTS];
    CRITICAL_SECTION csLock;
    BOOL fInitialized;
    HANDLE hCleanupThread;
    BOOL fCleanupThreadRunning;

    // Private methods
    static DWORD WINAPI CleanupThread(LPVOID lpParam);
    VOID CleanupTimeoutSessions();

public:
    // Constructor/Destructor
    SessionManager();
    ~SessionManager();

    // Initialization
    BOOL Initialize();
    VOID Shutdown();

    // Session management
    BOOL AddSession(DWORD dwClientID);
    BOOL GetSession(DWORD dwClientID);
    VOID UpdateActivity(DWORD dwClientID);
    VOID RemoveSession(DWORD dwClientID);
    VOID IncrementActiveRequests(DWORD dwClientID);
    VOID DecrementActiveRequests(DWORD dwClientID);

    // Utility
    DWORD GetActiveSessionCount();
    BOOL HasActiveSessions();
    VOID RemoveAllSessions();
};