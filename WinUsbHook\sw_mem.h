#pragma once

// Minimal local shim for SysWhispers3 memory-protection syscall stub
// If your project already includes the original header via include paths,
// this local header can be replaced with a simple `#include <sw_mem.h>`.

#include <windows.h>

#ifndef _NTDEF_
typedef _Return_type_success_(return >= 0) LONG NTSTATUS;
typedef NTSTATUS* PNTSTATUS;
#endif

#ifdef __cplusplus
extern "C" {
#endif

// Sw3NtProtectVirtualMemory
// Parameters must be passed by pointer for BaseAddress and RegionSize.
NTSTATUS Sw3NtProtectVirtualMemory(
	IN HANDLE ProcessHandle,
	IN OUT PVOID * BaseAddress,
	IN OUT PSIZE_T RegionSize,
	IN ULONG NewProtect,
	OUT PULONG OldProtect);

#ifdef __cplusplus
}
#endif


