#pragma once

#include "Common.h"

// IOCTL command definitions (now defined in .cpp file)

// Physical memory request structure
typedef struct _PHYSICAL_MEMORY_REQUEST {
    QWORD PhysicalAddress;
    DWORD Size;
    BYTE Data[1];
} PHYSICAL_MEMORY_REQUEST, *PPHYSICAL_MEMORY_REQUEST;

// DriverInterface class - encapsulates communication with custom kernel driver
class DriverInterface {
private:
    HANDLE hDriver;
    CHAR szDriverName[MAX_PATH];
    BOOL fInitialized;

    // Private utility methods
    VOID LogError(LPCSTR szFunction, DWORD dwError);
    VOID LogInfo(LPCSTR szMessage);

public:
    // Constructor/Destructor
    DriverInterface();
    ~DriverInterface();

    // Initialization
    BOOL Initialize(LPCSTR szDriverName);
    VOID Close();

    // Memory operations
    BOOL ReadPhysicalMemory(QWORD qwAddress, DWORD cbSize, PBY<PERSON> pbBuffer);
    <PERSON>O<PERSON> WritePhysicalMemory(QWORD qwAddress, <PERSON>WOR<PERSON> cbSize, <PERSON><PERSON>Y<PERSON> pbBuffer);
    BOOL ReadScatter(DWORD cMEMs, PPMEM_SCATTER ppMEMs);
    BOOL WriteScatter(DWORD cMEMs, PPMEM_SCATTER ppMEMs);

    // Driver identification
    BOOL IdentifyDriver();
    
    // System information
    QWORD GetDTB();
    QWORD GetKernelBase();
    QWORD GetPhysicalMax();
    
    // Process enumeration
    BOOL EnumerateProcesses(PVOID* ppBuffer, PDWORD pcbSize);

    // Status
    BOOL IsInitialized() const { return fInitialized; }
    LPCSTR GetDriverName() const { return szDriverName; }
};