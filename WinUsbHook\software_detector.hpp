#pragma once
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#include <windows.h>
#include <string>
#include "console_ui.h"


// Software types enumeration
enum class SoftwareType {
    UNKNOWN = 0,
    FAST = 1,
    EVI = 2,
    FOAH = 3
};

// Hook strategy enumeration
enum class HookStrategy {
    NONE = 0,
    LEECHCORE_EXPORT = 1,
    VMM_RVA = 2,
    VMM_SIGNATURE = 3
};

class SoftwareDetector {
private:
    static SoftwareType s_detectedType;
    static HookStrategy s_selectedStrategy;
    static std::string s_currentDirectory;

public:
    // Get current process directory
    static bool GetCurrentProcessDirectory(std::string& outDir) {
        char path[MAX_PATH] = {0};
        DWORD length = GetModuleFileNameA(NULL, path, MAX_PATH);
        if (length == 0 || length == MAX_PATH) {
#if DBG_LOG
            LOG_ERROR("[SoftDetector] GetModuleFileName failed");
#endif
            return false;
        }
        
        // Strip filename to get directory
        char* lastSlash = strrchr(path, '\\');
        if (lastSlash) {
            *lastSlash = '\0';
        }
        
        outDir = path;
#if DBG_LOG
#ifdef DEBUGLOG
        LOG_INFO("[SoftDetector] Current directory: %s", outDir.c_str());
#endif
#endif
        return true;
    }

    // File exists?
    static bool FileExists(const std::string& filePath) {
        DWORD attributes = GetFileAttributesA(filePath.c_str());
        return (attributes != INVALID_FILE_ATTRIBUTES && !(attributes & FILE_ATTRIBUTE_DIRECTORY));
    }

    // Directory exists?
    static bool DirectoryExists(const std::string& dirPath) {
        DWORD attributes = GetFileAttributesA(dirPath.c_str());
        return (attributes != INVALID_FILE_ATTRIBUTES && (attributes & FILE_ATTRIBUTE_DIRECTORY));
    }

    // Detect FAST by presence of cheatfast.exe in current directory
    static bool DetectFAST(const std::string& currentDir) {
        std::string cheatFastExe = currentDir + "\\cheatfast.exe";
        bool exists = FileExists(cheatFastExe);
#if DBG_LOG
#ifdef DEBUGLOG
        LOG_INFO("[SoftDetector] FAST: cheatfast.exe %s: %s", 
            exists ? "found" : "not found", cheatFastExe.c_str());
#endif
#endif
        return exists;
    }

    // Detect FOAH by presence of both config.FO and frpc.dll in current directory
    static bool DetectFOAH(const std::string& currentDir) {
        std::string cfgPath = currentDir + "\\config.FO";
        std::string frpcPath = currentDir + "\\frpc.dll";
        bool hasBoth = FileExists(cfgPath) && FileExists(frpcPath);
#if DBG_LOG
#ifdef DEBUGLOG
        LOG_INFO("[SoftDetector] FOAH: config.FO %s, frpc.dll %s",
            FileExists(cfgPath) ? "found" : "not found",
            FileExists(frpcPath) ? "found" : "not found");
#endif
#endif
        return hasBoth;
    }

    // Detect EVI by presence of Java_Radar directory
    static bool DetectEVI(const std::string& currentDir) {
        std::string javaRadarDir = currentDir + "\\Java_Radar";
        bool exists = DirectoryExists(javaRadarDir);
#if DBG_LOG
#ifdef DEBUGLOG
        LOG_INFO("[SoftDetector] EVI: Java_Radar directory %s: %s", 
            exists ? "found" : "not found", javaRadarDir.c_str());
#endif
#endif
        return exists;
    }

    // Run detection
    static SoftwareType DetectSoftware() {
        if (s_detectedType != SoftwareType::UNKNOWN) {
            return s_detectedType;
        }

        std::string currentDir;
        if (!GetCurrentProcessDirectory(currentDir)) {
#if DBG_LOG
            LOG_ERROR("[SoftDetector] Failed to get current directory");
#endif
            s_detectedType = SoftwareType::UNKNOWN;
            return s_detectedType;
        }

        s_currentDirectory = currentDir;

#if DBG_LOG
#ifdef DEBUGLOG
        LOG_INFO("[SoftDetector] Starting software detection...");
#endif
#endif

        // Detection priority: FOAH -> FAST -> EVI
        if (DetectFOAH(currentDir)) {
            s_detectedType = SoftwareType::FOAH;
#if DBG_LOG
#ifdef DEBUGLOG
            LOG_SUCCESS("[SoftDetector] Detected: FOAH");
#endif
#endif
        }
        else if (DetectFAST(currentDir)) {
            s_detectedType = SoftwareType::FAST;
#if DBG_LOG
#ifdef DEBUGLOG
            LOG_SUCCESS("[SoftDetector] Detected: FAST");
#endif
#endif
        }
        else if (DetectEVI(currentDir)) {
            s_detectedType = SoftwareType::EVI;
#if DBG_LOG
#ifdef DEBUGLOG
            LOG_SUCCESS("[SoftDetector] Detected: EVI");
#endif
#endif
        }
        else {
            s_detectedType = SoftwareType::UNKNOWN;
#if DBG_LOG
#ifdef DEBUGLOG
            LOG_WARNING("[SoftDetector] Unknown software detected");
#endif
#endif
        }

        return s_detectedType;
    }

    // Decide hook strategy by detected type
    static HookStrategy DetermineHookStrategy(SoftwareType softType = SoftwareType::UNKNOWN) {
        if (softType == SoftwareType::UNKNOWN) {
            softType = DetectSoftware();
        }

        switch (softType) {
            case SoftwareType::FAST:
            case SoftwareType::EVI:
                // Use LeechCore export for FAST/EVI
                s_selectedStrategy = HookStrategy::LEECHCORE_EXPORT;
#if DBG_LOG
#ifdef DEBUGLOG
                LOG_INFO("[SoftDetector] Selected strategy: LEECHCORE_EXPORT");
#endif
#endif
                break;
                
            case SoftwareType::FOAH:
                // Use VMM RVA strategy for FOAH
                s_selectedStrategy = HookStrategy::VMM_RVA;
#if DBG_LOG
#ifdef DEBUGLOG
                LOG_INFO("[SoftDetector] Selected strategy: VMM_RVA");
#endif
#endif
                break;
                
            case SoftwareType::UNKNOWN:
            default:
                // Fallback
                s_selectedStrategy = HookStrategy::LEECHCORE_EXPORT;
#if DBG_LOG
#ifdef DEBUGLOG
                LOG_INFO("[SoftDetector] Selected strategy: LEECHCORE_EXPORT (fallback for unknown)");
#endif
#endif
                break;
        }

        return s_selectedStrategy;
    }

    // Accessors
    static SoftwareType GetDetectedType() { return s_detectedType; }
    static HookStrategy GetSelectedStrategy() { return s_selectedStrategy; }
    static const std::string& GetCurrentDirectory() { return s_currentDirectory; }

    // Get software type name
    static const char* GetSoftwareTypeName(SoftwareType type) {
        switch (type) {
            case SoftwareType::FAST: return "FAST";
            case SoftwareType::EVI: return "EVI";
            case SoftwareType::FOAH: return "FOAH";
            case SoftwareType::UNKNOWN: 
            default: return "UNKNOWN";
        }
    }

    // Get hook strategy name
    static const char* GetHookStrategyName(HookStrategy strategy) {
        switch (strategy) {
            case HookStrategy::LEECHCORE_EXPORT: return "LEECHCORE_EXPORT";
            case HookStrategy::VMM_RVA: return "VMM_RVA";
            case HookStrategy::VMM_SIGNATURE: return "VMM_SIGNATURE";
            case HookStrategy::NONE:
            default: return "NONE";
        }
    }

    // Reset detector state
    static void Reset() {
        s_detectedType = SoftwareType::UNKNOWN;
        s_selectedStrategy = HookStrategy::NONE;
        s_currentDirectory.clear();
    }
};

// ��̬��Ա��������
SoftwareType SoftwareDetector::s_detectedType = SoftwareType::UNKNOWN;
HookStrategy SoftwareDetector::s_selectedStrategy = HookStrategy::NONE;
std::string SoftwareDetector::s_currentDirectory = "";
