; Minimal INF to install <PERSON>_Driver (sample). Adjust CatalogFile/Sign for production.

[Version]
Signature = "$Windows NT$"
Class = System
ClassGuid = {4d36e97d-e325-11ce-bfc1-08002be10318}
Provider = %ManufacturerName%
DriverVer = 08/09/2025,1.0.0.0

[DestinationDirs]
DefaultDestDir = 12

[Manufacturer]
%ManufacturerName% = Standard,NTamd64

[Standard.NTamd64]
%GodDriver.DeviceDesc% = GodDriver_Install, Root\God_Driver

[GodDriver_Install]
CopyFiles = @God_Driver.sys

[GodDriver_Install.Services]
AddService = God_Driver, 0x00000002, GodDriver_Service_Inst

[GodDriver_Service_Inst]
DisplayName    = %GodDriver.ServiceName%
ServiceType    = 1 ; SERVICE_KERNEL_DRIVER
StartType      = 3 ; SERVICE_DEMAND_START
ErrorControl   = 1 ; SERVICE_ERROR_NORMAL
ServiceBinary  = %12%\God_Driver.sys

[Strings]
ManufacturerName = "GodDriver"
GodDriver.DeviceDesc = "GodDriver Physical Memory Access Driver"
GodDriver.ServiceName = "God_Driver"


