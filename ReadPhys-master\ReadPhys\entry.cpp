﻿#include <ntifs.h>
#include "Anti4heatExpert.h"
#include "kli.hpp"
#include "KliGlobal.h"
#include <ntstrsafe.h>
#include <intrin.h>
#include "readphy.h"
#include "kxor.hpp"

// ----------------------------------------------------------------------------
// Optional debug logging: compile-time gated by DBGLOG.
// Define DBGLOG in project macros to enable kernel logging via KLI.
// ----------------------------------------------------------------------------
#ifdef DBGLOG
#  define DBG_PRINT(...) do { \
        if (KLIDbgPrintEx) { \
            KLI_CACHED_CALL(DbgPrintEx, DPFLTR_IHVDRIVER_ID, DPFLTR_ERROR_LEVEL, __VA_ARGS__); \
        } \
    } while (0)
#else
#  define DBG_PRINT(...) ((void)0)
#endif

// 鑷畾涔� __chkstk 瀹炵幇浠ラ伩鍏嶅鍏�
extern "C" void __chkstk(void)
{
	// 绌哄疄鐜� - 鎴戜滑宸茬粡閫氳繃 /Gs65536 璁剧疆浜嗚冻澶熷ぇ鐨勬爤淇濇姢
}

// 绂佺敤寮傚父澶勭悊鐩稿叧鐨勭鍙�
extern "C" int _fltused = 0;

// 鍐呮牳椹卞姩DMA鍗忚瀹氫箟 - 閬垮厤鍖呭惈鐢ㄦ埛妯″紡澶存枃浠�
#define DMA_MAGIC 0x444D4131  // "DMA1"
#define DMA_CMD_READ  0x01
#define DMA_CMD_WRITE 0x02

// 椹卞姩IOCTL瀹氫箟
#define IOCTL_DMA_READ_MEMORY   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_WRITE_MEMORY  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_IDENTIFY      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_GET_DTB       CTL_CODE(FILE_DEVICE_UNKNOWN, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)
// 新增：批量散读 IOCTL（最小实现，METHOD_BUFFERED）
#define IOCTL_DMA_READ_SCATTER  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS)
// 新增：获取内核基址与物理上限
#define IOCTL_DMA_GET_KERNELBASE CTL_CODE(FILE_DEVICE_UNKNOWN, 0x805, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_GET_PHYSMAX    CTL_CODE(FILE_DEVICE_UNKNOWN, 0x806, METHOD_BUFFERED, FILE_ANY_ACCESS)
// 新增：进程枚举
#define IOCTL_DMA_ENUM_PROCESSES CTL_CODE(FILE_DEVICE_UNKNOWN, 0x807, METHOD_BUFFERED, FILE_ANY_ACCESS)

// 璁惧璇嗗埆鏍囪瘑绗�
#define DMA_DEVICE_SIGNATURE    0x444D4144  // "DMAD"

// 椹卞姩璇锋眰缁撴瀯
typedef struct _DMA_REQUEST {
	DWORD Command;      // DMA_CMD_READ 鎴� DMA_CMD_WRITE
	ULONG64 Address;    // 鐗╃悊鍐呭瓨鍦板潃
	DWORD Length;       // 鏁版嵁闀垮害
	DWORD Reserved;     // 淇濈暀瀛楁
} DMA_REQUEST, * PDMA_REQUEST;

// 最小散读请求头/元素（METHOD_BUFFERED 输入；输出为各段数据顺序拼接）
typedef struct _DMA_SCATTER_ELEM {
    ULONGLONG Address;  // 物理地址
    ULONG     Length;   // 每段长度（建议≤0x1000）
    ULONG     Reserved;
} DMA_SCATTER_ELEM, *PDMA_SCATTER_ELEM;

typedef struct _DMA_SCATTER_HDR {
    ULONG Version;      // = 1
    ULONG Flags;        // 预留
    ULONG Count;        // 元素个数
    ULONG Reserved;
    DMA_SCATTER_ELEM Elem[1]; // 可变长
} DMA_SCATTER_HDR, *PDMA_SCATTER_HDR;

// 进程信息结构
typedef struct _PROCESS_INFO {
    ULONG ProcessId;
    ULONG ParentProcessId;
    ULONG64 DirectoryTableBase;
    CHAR ImageFileName[16];
    ULONG SessionId;
    ULONG State;                    // 进程状态
    ULONG64 VirtualAddress;         // EPROCESS 虚拟地址
    UCHAR Reserved[8];
} PROCESS_INFO, *PPROCESS_INFO;

// 进程列表响应头
typedef struct _PROCESS_LIST_RESPONSE {
    ULONG Version;                  // = 1
    ULONG ProcessCount;             // 进程数量
    ULONG TotalSize;                // 总大小
    ULONG Reserved;
    PROCESS_INFO Processes[1];      // 可变长进程数组
} PROCESS_LIST_RESPONSE, *PPROCESS_LIST_RESPONSE;

// KLI函数指针已在KliGlobal.h中统一定义

// 鍏ㄥ眬鍙橀噺淇濆瓨璁惧鍚嶇О
UNICODE_STRING g_DeviceName;
UNICODE_STRING g_DosDeviceName;

// 简单字符串解密（旧：逐字节 -> 会产生宽字符奇偶位错，不用于格式串）
VOID DeobfuscateString(PWCHAR dest, const UCHAR* src, ULONG len, UCHAR key)
{
    for (ULONG i = 0; i < len; i++) {
        dest[i] = (WCHAR)(src[i] ^ key);
    }
    dest[len] = L'\0';
}

// 解密“宽格式串”：输入为按字节存储的 WCHAR，每个字符2字节均做异或
// src: 按字节存储的加密宽串（如: ch_low^key, ch_high^key, ch_low^key, ch_high^key, ...）
static VOID DecryptWideFromPairXor(_Out_writes_(destCch) PWCHAR dest, SIZE_T destCch,
                                   _In_reads_(cbSrc) const UCHAR* src, SIZE_T cbSrc, UCHAR key)
{
    if (!dest || !src || destCch == 0) { return; }
    SIZE_T cch = cbSrc / 2; // 每2字节组成1个WCHAR
    if (cch >= destCch) { cch = destCch - 1; }
    for (SIZE_T i = 0; i < cch; ++i) {
        UCHAR lo = src[2 * i] ^ key;
        UCHAR hi = src[2 * i + 1] ^ key;
        dest[i] = (WCHAR)(lo | ((USHORT)hi << 8));
    }
    dest[cch] = L'\0';
}

// 辅助：写入固定宽度的十六进制（大写，零填充）
static __forceinline VOID WriteHexN(_Out_writes_(width) PWCHAR dst, ULONG value, INT width)
{
    for (INT i = width - 1; i >= 0; --i) {
        UINT nibble = (value >> (i * 4)) & 0xF;
        dst[width - 1 - i] = (WCHAR)(nibble < 10 ? (L'0' + nibble) : (L'A' + (nibble - 10)));
    }
}

// 生成形如:{%08x-%04x-%04x-%04x-%08x%04x} 的 GUID 文本（与原格式一致）
static NTSTATUS BuildGuidString(_Out_writes_(cchOut) PWCHAR out, SIZE_T cchOut,
                                ULONG part1, ULONG part2, ULONG part3, ULONG part4)
{
    const SIZE_T needed = 39; // 不含终止符需要38，含NUL至少39
    if (!out || cchOut < needed) {
        if (out && cchOut) { out[0] = L'\0'; }
        return STATUS_BUFFER_OVERFLOW;
    }
    SIZE_T p = 0;
    out[p++] = L'{';
    WriteHexN(out + p, part1, 8); p += 8; out[p++] = L'-';
    WriteHexN(out + p, (ULONG)((part2 >> 16) & 0xFFFF), 4); p += 4; out[p++] = L'-';
    WriteHexN(out + p, (ULONG)(part2 & 0xFFFF), 4); p += 4; out[p++] = L'-';
    WriteHexN(out + p, (ULONG)((part3 >> 16) & 0xFFFF), 4); p += 4; out[p++] = L'-';
    WriteHexN(out + p, part4, 8); p += 8;
    WriteHexN(out + p, (ULONG)(part3 & 0xFFFF), 4); p += 4;
    out[p++] = L'}';
    out[p] = L'\0';
    return STATUS_SUCCESS;
}

// 娣锋穯鐨勫瓧绗︿覆鏁版嵁
// "\\Device\\{%08x-%04x-%04x-%04x-%08x%04x}" XOR 0xAA per byte (WCHAR little-endian)
static const UCHAR g_DeviceFormat[] = {
    0xF6,0xAA, 0xF6,0xAA, 0xEE,0xAA, 0xCF,0xAA, 0xDC,0xAA, 0xC3,0xAA, 0xC9,0xAA, 0xCF,0xAA,
    0xF6,0xAA, 0xF6,0xAA, 0xD1,0xAA, 0x8F,0xAA, 0x9A,0xAA, 0x92,0xAA, 0xD2,0xAA, 0x87,0xAA,
    0x8F,0xAA, 0x9A,0xAA, 0x9E,0xAA, 0xD2,0xAA, 0x87,0xAA, 0x8F,0xAA, 0x9A,0xAA, 0x9E,0xAA,
    0xD2,0xAA, 0x87,0xAA, 0x8F,0xAA, 0x9A,0xAA, 0x9E,0xAA, 0xD2,0xAA, 0x87,0xAA, 0x8F,0xAA,
    0x9A,0xAA, 0x92,0xAA, 0xD2,0xAA, 0x8F,0xAA, 0x9A,0xAA, 0x9E,0xAA, 0xD2,0xAA, 0xD7,0xAA
};

// "\\DosDevices\\{%08x-%04x-%04x-%04x-%08x%04x}" XOR 0xBB per byte (WCHAR little-endian)
static const UCHAR g_DosFormat[] = {
    0xE7,0xBB, 0x84,0xBB, 0x84,0xBB, 0xE7,0xBB, 0xC0,0xBB,
      0x9E,0xBB, 0x8B,0xBB, 0x83,0xBB, 0xC3,0xBB, 0x96,0xBB,
      0x9E,0xBB, 0x8B,0xBB, 0x8F,0xBB, 0xC3,0xBB, 0x96,0xBB,
      0x9E,0xBB, 0x8B,0xBB, 0x8F,0xBB, 0xC3,0xBB, 0x96,0xBB,
      0x9E,0xBB, 0x8B,0xBB, 0x83,0xBB, 0xC3,0xBB, 0x9E,0xBB,
      0x8B,0xBB, 0x8F,0xBB, 0xC3,0xBB, 0xC6,0xBB
};

ULONG SimpleHash(const char* str, ULONG seed)
{
	ULONG hash = seed;
	while (*str) {
		hash = hash * 33 + (ULONG)(*str);
		str++;
	}
	return hash;
}

// 获取CPU ID - 内核版本实现
ULONG GetCpuId()
{
	int cpuInfo[4];
	__cpuid(cpuInfo, 1);
	return (ULONG)cpuInfo[3]; // EDX寄存器包含CPU特征标志
}

VOID GeneratePredictableGUID(ULONG* part1, ULONG* part2, ULONG* part3, ULONG* part4)
{
	// 获取CPU ID作为系统唯一标识
	ULONG cpuId = GetCpuId();

	// 基于CPU ID生成唯一种子
	ULONG baseSeed1 = 0x12345678 ^ cpuId;
	ULONG baseSeed2 = 0x87654321 ^ (cpuId >> 8);
	ULONG baseSeed3 = 0xABCDEF00 ^ (cpuId >> 16);
	ULONG baseSeed4 = 0x00FEDCBA ^ (cpuId >> 24);

	// 使用固定字符串和CPU ID混合生成
	const char* seed1 = "ReadPhysDevice2024";
	const char* seed2 = "DMAController";
	const char* seed3 = "PhysicalMemory";
	const char* seed4 = "KernelDriver";

	*part1 = SimpleHash(seed1, baseSeed1);
	*part2 = SimpleHash(seed2, baseSeed2);
	*part3 = SimpleHash(seed3, baseSeed3);
	*part4 = SimpleHash(seed4, baseSeed4);
}
NTSTATUS GenerateRandomDeviceName(PUNICODE_STRING DeviceName, PUNICODE_STRING DosDeviceName)
{
	NTSTATUS status = STATUS_SUCCESS;
	PWCHAR deviceBuf = NULL;
	PWCHAR dosBuf = NULL;

	// 检查函数指针
	if (!KLIExAllocatePoolWithTag) {
		return STATUS_NOT_SUPPORTED;
	}

	deviceBuf = (PWCHAR)KLI_CACHED_CALL(ExAllocatePoolWithTag, NonPagedPool, 128 * sizeof(WCHAR), 'enoN');
	if (!deviceBuf) return STATUS_INSUFFICIENT_RESOURCES;

	dosBuf = (PWCHAR)KLI_CACHED_CALL(ExAllocatePoolWithTag, NonPagedPool, 128 * sizeof(WCHAR), 'enoN');
	if (!dosBuf) {
		if (KLIExFreePool) {
			KLI_CACHED_CALL(ExFreePool, deviceBuf);
		}
		return STATUS_INSUFFICIENT_RESOURCES;
	}

    // 生成"可预测但机器相关"的 4 段（你自己的函数需要保证 r3 可复现）
    ULONG part1, part2, part3, part4;
    GeneratePredictableGUID(&part1, &part2, &part3, &part4);

    // 前缀手工拼装，避免出现明文宽字符串常量
    wchar_t devPrefix[10] = { L'\\', L'D', L'e', L'v', L'i', L'c', L'e', L'\\', L'\0' };
    wchar_t dosPrefix[13] = { L'\\', L'D', L'o', L's', L'D', L'e', L'v', L'i', L'c', L'e', L's', L'\\', L'\0' };

    // 生成 GUID 文本（无明文格式串，手工写入各字段）
    wchar_t guidBuf[64] = {0};
    status = BuildGuidString(guidBuf, _countof(guidBuf), part1, part2, part3, part4);
    if (!NT_SUCCESS(status)) { goto Exit; }

    // 组合 devPrefix + guidBuf → deviceBuf
    {
        size_t cbPrefix = 0, cbGuid = 0; MyStringCbLength(devPrefix, &cbPrefix); MyStringCbLength(guidBuf, &cbGuid);
        size_t cchPrefix = cbPrefix / sizeof(WCHAR) - 1, cchGuid = cbGuid / sizeof(WCHAR) - 1;
        if (cchPrefix + cchGuid + 1 > 128) { status = STATUS_BUFFER_OVERFLOW; goto Exit; }
        RtlCopyMemory(deviceBuf, devPrefix, cchPrefix * sizeof(WCHAR));
        RtlCopyMemory(deviceBuf + cchPrefix, guidBuf, cbGuid);
    }

    // 组合 dosPrefix + guidBuf → dosBuf
    {
        size_t cbPrefix = 0, cbGuid = 0; MyStringCbLength(dosPrefix, &cbPrefix); MyStringCbLength(guidBuf, &cbGuid);
        size_t cchPrefix = cbPrefix / sizeof(WCHAR) - 1, cchGuid = cbGuid / sizeof(WCHAR) - 1;
        if (cchPrefix + cchGuid + 1 > 128) { status = STATUS_BUFFER_OVERFLOW; goto Exit; }
        RtlCopyMemory(dosBuf, dosPrefix, cchPrefix * sizeof(WCHAR));
        RtlCopyMemory(dosBuf + cchPrefix, guidBuf, cbGuid);
    }

	if (!NT_SUCCESS(status)) {
		goto Exit;
	}

	// 获取字符串长度
	size_t devLen = MyWcslen(deviceBuf);
	size_t dosLen = MyWcslen(dosBuf);

	// 设置 UNICODE_STRING
	DeviceName->Buffer = deviceBuf;
	DeviceName->Length = (USHORT)(devLen * sizeof(WCHAR));
	DeviceName->MaximumLength = (USHORT)(128 * sizeof(WCHAR));

	DosDeviceName->Buffer = dosBuf;
	DosDeviceName->Length = (USHORT)(dosLen * sizeof(WCHAR));
	DosDeviceName->MaximumLength = (USHORT)(128 * sizeof(WCHAR));

	return STATUS_SUCCESS;

Exit:
	if (deviceBuf && KLIExFreePool) KLI_CACHED_CALL(ExFreePool, deviceBuf);
	if (dosBuf && KLIExFreePool) KLI_CACHED_CALL(ExFreePool, dosBuf);
	return status;
}




ULONG ReadVirtualMemory(ULONG64 DirectoryTableBase, PVOID Address, PVOID Buffer, PULONG pSizeRead)
{
	BOOLEAN IsDoneRead;
	ULONG ErrorCode;
	ULONG SizeLeft;
	ULONG OffsetInPage;
	ULONG SizeRead;
	ULONG SizeLeftInPage;
	ULONG Size;
	ULONG64 PhysicalAddress;
	ULONG64 PageSize;
	ULONG64 PageAddress;
	Anti4heatExpert::PHYSICAL_PAGE_INFO TransferPageInfo;

	SizeRead = 0;
	memset(&TransferPageInfo, 0, sizeof(TransferPageInfo));
	if (!DirectoryTableBase)
	{
		DirectoryTableBase = __readcr3();
	}
	if (Address && Buffer && pSizeRead && *pSizeRead)
	{
		if (KLI_CACHED_CALL(KeGetCurrentIrql) <= 2u)
		{
			Anti4heatExpert::AllocatePhysicalPage(&TransferPageInfo, 0x1000);
			PageSize = 0;
			ErrorCode = Anti4heatExpert::GetPhysPageSize(&TransferPageInfo, (ULONG64)Address, &PageSize, DirectoryTableBase);
			if (!ErrorCode && PageSize > 0x1000)
			{
				ErrorCode = 1;
				goto _EXIT;
			}
			OffsetInPage = (ULONG64)Address & 0xFFF;
			PageAddress = (ULONG64)Address & 0xFFFFFFFFFFFFF000u;
			Size = *pSizeRead;
			SizeLeft = *pSizeRead;
			do
			{
				IsDoneRead = FALSE;
				if (SizeLeft >= PAGE_SIZE - OffsetInPage)
					SizeLeftInPage = PAGE_SIZE - OffsetInPage;
				else
					SizeLeftInPage = SizeLeft;

				PhysicalAddress = 0;
				ErrorCode = Anti4heatExpert::GetPhysPageAddress(&TransferPageInfo, DirectoryTableBase, (PVOID)PageAddress, &PhysicalAddress);

				if (!ErrorCode && PhysicalAddress
					&& !Anti4heatExpert::ReadPhysicalPage(
						&TransferPageInfo,
						PhysicalAddress + OffsetInPage,
						Buffer,
						SizeLeftInPage))
				{
					SizeRead += SizeLeftInPage;
					IsDoneRead = TRUE;
				}
				if (!IsDoneRead)
					memset(Buffer, 0, SizeLeftInPage);
				Buffer = (PUCHAR)Buffer + SizeLeftInPage;
				PageAddress += OffsetInPage + (ULONG64)SizeLeftInPage;
				OffsetInPage = 0;
				SizeLeft -= SizeLeftInPage;
			} while (SizeLeft && SizeLeft < Size);
			if (SizeRead)
			{
				*pSizeRead = SizeRead;
				ErrorCode = 0;
			}
			else
			{
				ErrorCode = 216;
			}
		}
		else
		{
			ErrorCode = 261;
		}
	}
	else
	{
		ErrorCode = 22;
	}
_EXIT:
	Anti4heatExpert::FreePhysicalPage(&TransferPageInfo);
	return ErrorCode;
}

// 直锟接讹拷取锟斤拷锟斤拷锟节达拷锟街凤拷锟斤拷锟�
ULONG ReadPhysicalMemory(ULONG64 PhysicalAddress, PVOID Buffer, ULONG Size, PULONG pSizeRead)
{
	ULONG ErrorCode;
	ULONG SizeRead = 0;
	ULONG SizeLeft = Size;
	ULONG SizeLeftInPage;
	ULONG64 PhysPageBase;
	ULONG OffsetInPage;
	Anti4heatExpert::PHYSICAL_PAGE_INFO TransferPageInfo;

	memset(&TransferPageInfo, 0, sizeof(TransferPageInfo));

	if (!Buffer || !pSizeRead || !Size)
	{
		return 22; // ERROR_INVALID_PARAMETER
	}

	*pSizeRead = 0;

	if (KLI_CACHED_CALL(KeGetCurrentIrql) > 2u)
	{
		return 261; // ERROR_INVALID_LEVEL
	}

	// 锟斤拷锟斤拷锟斤拷锟斤拷页锟斤拷锟节达拷锟斤拷
	ErrorCode = Anti4heatExpert::AllocatePhysicalPage(&TransferPageInfo, 0x1000);
	if (ErrorCode)
	{
		return ErrorCode;
	}

	while (SizeLeft > 0)
	{
		PhysPageBase = PhysicalAddress & 0xFFFFFFFFFFFFF000ULL; // 页锟斤拷锟斤拷
		OffsetInPage = (ULONG)(PhysicalAddress & 0xFFF);

		// 锟斤拷锟姐本锟轿讹拷取锟斤拷小
		if (SizeLeft >= PAGE_SIZE - OffsetInPage)
			SizeLeftInPage = PAGE_SIZE - OffsetInPage;
		else
			SizeLeftInPage = SizeLeft;

		// 锟斤拷取锟斤拷锟斤拷页
		ErrorCode = Anti4heatExpert::ReadPhysicalPage(
			&TransferPageInfo,
			PhysPageBase + OffsetInPage,
			(PUCHAR)Buffer + SizeRead,
			SizeLeftInPage);

		if (ErrorCode)
		{
			// 锟斤拷取失锟杰ｏ拷锟斤拷锟�0
			memset((PUCHAR)Buffer + SizeRead, 0, SizeLeftInPage);
		}
		else
		{
			SizeRead += SizeLeftInPage;
		}

		PhysicalAddress += SizeLeftInPage;
		SizeLeft -= SizeLeftInPage;
	}

	Anti4heatExpert::FreePhysicalPage(&TransferPageInfo);

	*pSizeRead = SizeRead;
	return SizeRead > 0 ? 0 : 216; // ERROR_ARITHMETIC_OVERFLOW if no data read
}

// 获取 System 进程（PID=4）DTB：附加到 System 进程上下文后读取 CR3
// 全局缓存的 CR3（PML4），DriverEntry 初始化时可直接赋值
static ULONG64 g_CachedCR3 = 0;

ULONG64 GetSystemDTB()
{
    // 1) 先用缓存（DriverEntry 已在 PASSIVE_LEVEL 获取过）
    if (g_CachedCR3) {
        return g_CachedCR3;
    }

    // 2) 兜底：通过 PID=4 查找 System EPROCESS 并附加读取 CR3
    if (!KLIPsLookupProcessByProcessId || !KLIKeStackAttachProcess || !KLIKeUnstackDetachProcess) {
        return 0;
    }
    if (KLI_CACHED_CALL(KeGetCurrentIrql) != PASSIVE_LEVEL) {
        return 0; // 仅在 PASSIVE_LEVEL 执行
    }

    ULONG64 cr3 = 0;
    PEPROCESS pSystem = NULL;
    if (!NT_SUCCESS(KLI_CACHED_CALL(PsLookupProcessByProcessId, (HANDLE)4, &pSystem)) || !pSystem) {
        return 0;
    }
    KAPC_STATE apc = { 0 };
    KLI_CACHED_CALL(KeStackAttachProcess, (PRKPROCESS)pSystem, &apc);
    cr3 = __readcr3();
    KLI_CACHED_CALL(KeUnstackDetachProcess, &apc);
    KLI_CACHED_CALL(ObfDereferenceObject, pSystem);

    // 缓存结果（清理低 12 位/掩掉高位由用户态清理）
    if (cr3) {
        g_CachedCR3 = cr3;
    }
    return cr3;
}

// 获取 ActiveProcessLinks 在 EPROCESS 中的偏移
// 这个偏移需要根据 Windows 版本调整
static ULONG GetActiveProcessLinksOffset()
{
    // Windows 10/11 x64 的常见偏移
    // 可以通过符号文件或动态检测获得更准确的偏移
    return 0x448; // 常见偏移，可能需要根据系统版本调整
}
#if 0
// 计算 scatter 输出总长度（并做简单上限约束）
static
BOOLEAN CalcScatterOutSize(_In_ PDMA_SCATTER_HDR hdr, _In_ ULONG inLen, _Out_ SIZE_T* pTotal)
{
	if (!hdr || inLen < sizeof(DMA_SCATTER_HDR)) return FALSE;
	ULONG count = hdr->Count;
	SIZE_T needIn = sizeof(DMA_SCATTER_HDR) + ((SIZE_T)count - 1) * sizeof(DMA_SCATTER_ELEM);
	if (inLen < needIn) return FALSE;

	SIZE_T total = 0;
	for (ULONG i = 0; i < count; ++i) {
		SIZE_T cb = hdr->Elem[i].Length;
		if (cb == 0 || cb > 0x100000) return FALSE; // 限制单段大小，防御
		total += cb;
		if (total > (SIZE_T)16 * 1024 * 1024) return FALSE; // 上限 16MB
	}
	*pTotal = total;
	return TRUE;
}
// 枚举系统进程
NTSTATUS EnumerateSystemProcesses(PVOID OutputBuffer, ULONG OutputLength, PULONG BytesReturned)
{
    if (!OutputBuffer || OutputLength < sizeof(PROCESS_LIST_RESPONSE)) {
        return STATUS_BUFFER_TOO_SMALL;
    }

    // 检查必要的 KLI 函数指针
    if (!KLIPsGetProcessId || !KLIKeStackAttachProcess || !KLIKeUnstackDetachProcess ||
        !KLIPsLookupProcessByProcessId || !KLIObfDereferenceObject) {
        DBG_PRINT("[-] Required KLI functions not available\n");
        return STATUS_NOT_SUPPORTED;
    }

    PPROCESS_LIST_RESPONSE response = (PPROCESS_LIST_RESPONSE)OutputBuffer;
    RtlZeroMemory(response, sizeof(PROCESS_LIST_RESPONSE));
    
    response->Version = 1;
    
    ULONG maxProcesses = (OutputLength - sizeof(PROCESS_LIST_RESPONSE) + sizeof(PROCESS_INFO)) / sizeof(PROCESS_INFO);
    ULONG processCount = 0;
    
    // 从 System 进程开始 (PID=4)
    PEPROCESS systemProcess = NULL;
    NTSTATUS status = KLI_CACHED_CALL(PsLookupProcessByProcessId, (HANDLE)4, &systemProcess);
    if (!NT_SUCCESS(status) || !systemProcess) {
        DBG_PRINT("[-] Failed to lookup System process: 0x%08X\n", status);
        return STATUS_UNSUCCESSFUL;
    }

    PEPROCESS currentProcess = systemProcess;
    PEPROCESS firstProcess = systemProcess;
    ULONG activeLinksOffset = GetActiveProcessLinksOffset();
    
    __try {
        do {
            if (processCount >= maxProcesses) {
                break;
            }
            
            PROCESS_INFO* procInfo = &response->Processes[processCount];
            
            // 获取基本信息
            procInfo->ProcessId = (ULONG)HandleToUlong(KLI_CACHED_CALL(PsGetProcessId, currentProcess));
            procInfo->ParentProcessId = 0; // 暂时设为0，避免使用不稳定的API
            procInfo->VirtualAddress = (ULONG64)currentProcess;
            
            // 获取 DTB (CR3)
            if (procInfo->ProcessId != 0) {
                KAPC_STATE apc = {0};
                __try {
                    KLI_CACHED_CALL(KeStackAttachProcess, (PKPROCESS)currentProcess, &apc);
                    procInfo->DirectoryTableBase = __readcr3() & ~0xFFFULL; // 清除低12位
                    KLI_CACHED_CALL(KeUnstackDetachProcess, &apc);
                } __except(EXCEPTION_EXECUTE_HANDLER) {
                    procInfo->DirectoryTableBase = 0;
                }
            } else {
                procInfo->DirectoryTableBase = 0;
            }
            
            // 获取进程名 - 使用直接偏移方法 (更安全)
            __try {
                // EPROCESS.ImageFileName 通常在固定偏移处 (0x5a8 for Win10/11 x64)
                PUCHAR imageName = (PUCHAR)((PUCHAR)currentProcess + 0x5a8);
                if (imageName && imageName[0] != 0) {
                    // 手动计算字符串长度，避免使用 strlen
                    SIZE_T nameLen = 0;
                    while (nameLen < 15 && imageName[nameLen] != 0) {
                        nameLen++;
                    }
                    if (nameLen > 0) {
                        RtlCopyMemory(procInfo->ImageFileName, imageName, nameLen);
                        procInfo->ImageFileName[nameLen] = '\0';
                    } else {
                        RtlCopyMemory(procInfo->ImageFileName, "Unknown", 8);
                    }
                } else {
                    RtlCopyMemory(procInfo->ImageFileName, "Unknown", 8);
                }
            } __except(EXCEPTION_EXECUTE_HANDLER) {
                RtlCopyMemory(procInfo->ImageFileName, "Error", 6);
            }
            
            // 获取会话ID - 暂时设为0
            procInfo->SessionId = 0;
            
            // 进程状态（简化版本）
            procInfo->State = 0; // 假设为活动状态
            
            processCount++;
            
            // 获取下一个进程 (通过 ActiveProcessLinks)
            PLIST_ENTRY processLinks = (PLIST_ENTRY)((PUCHAR)currentProcess + activeLinksOffset);
            if (!processLinks || !processLinks->Flink) {
                DBG_PRINT("[+] End of process list reached\n");
                break;
            }
            
            PLIST_ENTRY nextEntry = processLinks->Flink;
            if (nextEntry == processLinks) {
                DBG_PRINT("[+] Circular link detected, stopping\n");
                break;
            }
            
            // 计算下一个 EPROCESS 地址
            currentProcess = CONTAINING_RECORD(nextEntry, EPROCESS, ActiveProcessLinks);
            
            // 防止无限循环
            if (currentProcess == firstProcess) {
                DBG_PRINT("[+] Completed full process list traversal\n");
                break;
            }
            
        } while (currentProcess != NULL && processCount < maxProcesses);
        
    } __except(EXCEPTION_EXECUTE_HANDLER) {
        DBG_PRINT("[-] Exception during process enumeration: 0x%08X\n", GetExceptionCode());
        if (processCount == 0) {
            KLI_CACHED_CALL(ObfDereferenceObject, systemProcess);
            return STATUS_UNSUCCESSFUL;
        }
    }
    
    KLI_CACHED_CALL(ObfDereferenceObject, systemProcess);
    
    response->ProcessCount = processCount;
    response->TotalSize = sizeof(PROCESS_LIST_RESPONSE) + (processCount > 0 ? (processCount - 1) * sizeof(PROCESS_INFO) : 0);
    
    *BytesReturned = response->TotalSize;
    
    DBG_PRINT("[+] Enumerated %u processes\n", processCount);
    return STATUS_SUCCESS;
}

#endif
// IOCTL锟街凤拷锟斤拷锟斤拷
NTSTATUS DmaDeviceControl(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	UNREFERENCED_PARAMETER(DeviceObject);

	PIO_STACK_LOCATION irpStack = IoGetCurrentIrpStackLocation(Irp);
	NTSTATUS status = STATUS_INVALID_DEVICE_REQUEST;
	ULONG bytesReturned = 0;

	switch (irpStack->Parameters.DeviceIoControl.IoControlCode)
	{
    case IOCTL_DMA_READ_MEMORY:
    {
        // METHOD_BUFFERED: 输入与输出共用同一 SystemBuffer，先安全拷贝输入头
        ULONG inLen  = irpStack->Parameters.DeviceIoControl.InputBufferLength;
        ULONG outLen = irpStack->Parameters.DeviceIoControl.OutputBufferLength;
        DMA_REQUEST inReq = { 0 };

        if (inLen < sizeof(DMA_REQUEST)) {
            status = STATUS_INVALID_PARAMETER;
            break;
        }

        RtlCopyMemory(&inReq, Irp->AssociatedIrp.SystemBuffer, sizeof(DMA_REQUEST));

        if (inReq.Command != DMA_CMD_READ || inReq.Length == 0 || outLen < inReq.Length) {
            status = STATUS_INVALID_PARAMETER;
            break;
        }

        PUCHAR outBuf = (PUCHAR)Irp->AssociatedIrp.SystemBuffer;
        SIZE_T want = inReq.Length;
        // 防御性上限，避免一次请求极大触发风险
        if (want > (SIZE_T)(16 * 1024 * 1024)) {
            want = 16 * 1024 * 1024;
        }

        SIZE_T got = 0;
        NTSTATUS st = ReadPhysicalPaged_NoMmCopy(inReq.Address, outBuf, want, &got);
        if (!NT_SUCCESS(st)) {
            status = st;
            bytesReturned = 0;
            break;
        }
        if (got < want) {
            RtlZeroMemory(outBuf + got, want - got);
        }
        bytesReturned = (ULONG)got;
        status = (bytesReturned > 0) ? STATUS_SUCCESS : STATUS_UNSUCCESSFUL;
        break;
    }

    case IOCTL_DMA_READ_SCATTER:
    {
        // METHOD_BUFFERED 散读：兼容两种头格式
        // A) 旧版: DMA_SCATTER_HDR{Version=1,Flags,Count,Reserved} + Elem[]
        // B) 兼容: ALT_HDR{Magic='SCAT',Count} + Elem[]
        ULONG inLen  = irpStack->Parameters.DeviceIoControl.InputBufferLength;
        ULONG outLen = irpStack->Parameters.DeviceIoControl.OutputBufferLength;
        if (inLen < sizeof(ULONG) * 2) { status = STATUS_INVALID_PARAMETER; break; }

        PUCHAR sysBuf = (PUCHAR)Irp->AssociatedIrp.SystemBuffer;
        ULONG versionOrMagic = *(ULONG*)sysBuf;
        BOOLEAN useAlt = FALSE;
        ULONG count = 0;
        SIZE_T needIn = 0;
        SIZE_T headerSize = 0;

        if (versionOrMagic == 0x53434154) { // 'SCAT'
            // ALT: [Magic][Count] then items
            if (inLen < sizeof(ULONG) * 2) { status = STATUS_INVALID_PARAMETER; break; }
            count = *(ULONG*)(sysBuf + sizeof(ULONG));
            headerSize = sizeof(ULONG) * 2;
            useAlt = TRUE;
        } else {
            if (inLen < sizeof(DMA_SCATTER_HDR)) { status = STATUS_INVALID_PARAMETER; break; }
            PDMA_SCATTER_HDR inHdr = (PDMA_SCATTER_HDR)sysBuf;
            if (inHdr->Version != 1 || inHdr->Count == 0) { status = STATUS_INVALID_PARAMETER; break; }
            count = inHdr->Count;
            headerSize = sizeof(DMA_SCATTER_HDR);
        }

        if (count == 0) { status = STATUS_INVALID_PARAMETER; break; }
        // 修复：对于标准格式，DMA_SCATTER_HDR已包含一个Elem[1]
        if (useAlt) {
            // ALT格式：header只有8字节(Magic+Count)，所以需要完整计算
            needIn = headerSize + ((SIZE_T)count) * sizeof(DMA_SCATTER_ELEM);
        } else {
            // 标准格式：DMA_SCATTER_HDR已包含Elem[1]，所以要减1
            needIn = sizeof(DMA_SCATTER_HDR) + ((SIZE_T)(count - 1)) * sizeof(DMA_SCATTER_ELEM);
        }
        if (inLen < needIn) { status = STATUS_INVALID_PARAMETER; break; }

        // 复制输入头到非分页缓冲，避免输出覆盖输入头导致后续 Elem 被破坏
        // 为了安全，分配稍大的缓冲区以容纳所有元素
        SIZE_T allocSize = sizeof(DMA_SCATTER_HDR) + ((SIZE_T)count) * sizeof(DMA_SCATTER_ELEM);
        PDMA_SCATTER_HDR hdrCopy = (PDMA_SCATTER_HDR)KLI_CACHED_CALL(ExAllocatePoolWithTag,NonPagedPoolNx, allocSize, 'tacS');
        if (!hdrCopy) { status = STATUS_INSUFFICIENT_RESOURCES; break; }
        RtlZeroMemory(hdrCopy, allocSize);
        if (useAlt) {
            // 填充到 DMA_SCATTER_HDR 兼容结构
            hdrCopy->Version = 1; hdrCopy->Flags = 0; hdrCopy->Count = count; hdrCopy->Reserved = 0;
            PDMA_SCATTER_ELEM src = (PDMA_SCATTER_ELEM)(sysBuf + headerSize);
            for (ULONG i = 0; i < count; ++i) { hdrCopy->Elem[i] = src[i]; }
        } else {
            RtlCopyMemory(hdrCopy, sysBuf, needIn);
        }

        // 计算总输出长度并校验单段大小
        SIZE_T total = 0;
        BOOLEAN validationFailed = FALSE;
        for (ULONG i = 0; i < hdrCopy->Count; ++i) {
            SIZE_T cb = hdrCopy->Elem[i].Length;
            if (cb == 0 || cb > 0x1000) { 
                status = STATUS_INVALID_PARAMETER; 
                validationFailed = TRUE;
                break; 
            }
            total += cb;
            if (total > (SIZE_T)16 * 1024 * 1024) { 
                status = STATUS_INVALID_PARAMETER; 
                validationFailed = TRUE;
                break; 
            }
        }
        if (validationFailed) { 
            KLI_CACHED_CALL(ExFreePoolWithTag, hdrCopy, 'tacS'); 
            break; 
        }
        if (outLen < total) { KLI_CACHED_CALL(ExFreePoolWithTag, hdrCopy, 'tacS'); status = STATUS_BUFFER_TOO_SMALL; break; }

        // 执行读取：对每段失败零填充，但不中断整个请求
        PUCHAR outBuf = (PUCHAR)Irp->AssociatedIrp.SystemBuffer; // 输出从偏移0开始
        SIZE_T o = 0;
        for (ULONG i = 0; i < hdrCopy->Count; ++i) {
            SIZE_T want = (SIZE_T)hdrCopy->Elem[i].Length;
            SIZE_T got = 0;
            NTSTATUS st = ReadPhysicalPaged_NoMmCopy(hdrCopy->Elem[i].Address, outBuf + o, want, &got);
            if (!NT_SUCCESS(st)) { got = 0; }
            if (got < want) { RtlZeroMemory(outBuf + o + got, want - got); }
            o += want;
        }
        KLI_CACHED_CALL(ExFreePoolWithTag,hdrCopy, 'tacS');
        bytesReturned = (ULONG)total;
        status = STATUS_SUCCESS;
        break;
    }

    case IOCTL_DMA_GET_KERNELBASE:
    {
        ULONG outLen = irpStack->Parameters.DeviceIoControl.OutputBufferLength;
        if (outLen < sizeof(ULONG64)) { status = STATUS_BUFFER_TOO_SMALL; break; }
        ULONG64 vaKernel = 0;
        // 使用 KLI 内核基址探测器（不创建导入表）
        vaKernel = kli::detail::get_kernel_base();
        *(ULONG64*)Irp->AssociatedIrp.SystemBuffer = vaKernel;
        bytesReturned = sizeof(ULONG64);
        status = STATUS_SUCCESS;
        break;
    }

    case IOCTL_DMA_GET_PHYSMAX:
    {
        ULONG outLen = irpStack->Parameters.DeviceIoControl.OutputBufferLength;
        if (outLen < sizeof(ULONG64)) { status = STATUS_BUFFER_TOO_SMALL; break; }
        ULONG64 paMax = 0;
        if (KLIMmGetPhysicalMemoryRanges) {
            PPHYSICAL_MEMORY_RANGE ranges = KLI_CACHED_CALL(MmGetPhysicalMemoryRanges);
            if (ranges) {
                for (ULONG i = 0; ; ++i) {
                    if (ranges[i].BaseAddress.QuadPart == 0 && ranges[i].NumberOfBytes.QuadPart == 0) break;
                    ULONG64 end = ranges[i].BaseAddress.QuadPart + ranges[i].NumberOfBytes.QuadPart - 1;
                    if (end > paMax) paMax = end;
                }
            }
        }
        *(ULONG64*)Irp->AssociatedIrp.SystemBuffer = paMax & ~0xFFFULL;
        bytesReturned = sizeof(ULONG64);
        status = STATUS_SUCCESS;
        break;
    }

	case IOCTL_DMA_WRITE_MEMORY:
	{
		// 鏆傛椂涓嶅疄鐜板啓鍏ュ姛鑳�
		status = STATUS_NOT_IMPLEMENTED;
		break;
	}

	case IOCTL_DMA_IDENTIFY:
	{
		// 璁惧璇嗗埆璇锋眰
		ULONG outputLength = irpStack->Parameters.DeviceIoControl.OutputBufferLength;
		if (outputLength >= sizeof(DWORD)) {
			*(DWORD*)Irp->AssociatedIrp.SystemBuffer = DMA_DEVICE_SIGNATURE;
			bytesReturned = sizeof(DWORD);
			status = STATUS_SUCCESS;
		}
		else {
			status = STATUS_INVALID_PARAMETER;
		}
		break;
	}

	case IOCTL_DMA_GET_DTB:
	{
		// 获取系统DTB请求
		ULONG outputLength = irpStack->Parameters.DeviceIoControl.OutputBufferLength;
		if (outputLength >= sizeof(ULONG64)) {
			ULONG64 dtb = GetSystemDTB();
			if (dtb != 0) {
				*(ULONG64*)Irp->AssociatedIrp.SystemBuffer = dtb;
				bytesReturned = sizeof(ULONG64);
				status = STATUS_SUCCESS;
			}
			else {
				status = STATUS_UNSUCCESSFUL;
			}
		}
		else {
			status = STATUS_INVALID_PARAMETER;
		}
		break;
	}

#if 0
    case IOCTL_DMA_ENUM_PROCESSES:
    {
        // 进程枚举请求
        ULONG outputLength = irpStack->Parameters.DeviceIoControl.OutputBufferLength;
        if (outputLength < sizeof(PROCESS_LIST_RESPONSE)) {
            status = STATUS_BUFFER_TOO_SMALL;
            break;
        }
        
        ULONG actualBytes = 0;
        status = EnumerateSystemProcesses(
            Irp->AssociatedIrp.SystemBuffer,
            outputLength,
            &actualBytes
        );
        
        if (NT_SUCCESS(status)) {
            bytesReturned = actualBytes;
            DBG_PRINT("[+] Process enumeration successful, returned %u bytes\n", actualBytes);
        } else {
            DBG_PRINT("[-] Process enumeration failed: 0x%08X\n", status);
            bytesReturned = 0;
        }
        break;
    }
#endif

	default:
		status = STATUS_INVALID_DEVICE_REQUEST;
		break;
	}

	Irp->IoStatus.Status = status;
	Irp->IoStatus.Information = bytesReturned;
	KLI_CACHED_CALL(IofCompleteRequest, Irp, IO_NO_INCREMENT);

	return status;
}

// 通锟矫分凤拷锟斤拷锟斤拷
NTSTATUS DmaDispatchCreateClose(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
	UNREFERENCED_PARAMETER(DeviceObject);

	Irp->IoStatus.Status = STATUS_SUCCESS;
	Irp->IoStatus.Information = 0;
	KLI_CACHED_CALL(IofCompleteRequest, Irp, IO_NO_INCREMENT);

	return STATUS_SUCCESS;
}


EXTERN_C NTSTATUS DriverEntry(PDRIVER_OBJECT DriverObject, PUNICODE_STRING RegistryPath)
{
	//UNREFERENCED_PARAMETER(RegistryPath);

	NTSTATUS status;
	PDEVICE_OBJECT deviceObject = NULL;


	InitializeAllKliCache();


	//DbgPrintEx(DPFLTR_IHVDRIVER_ID, DPFLTR_ERROR_LEVEL, "[+] Driver loading - checking system resources\n");
	// 检查系统资源状态
    DBG_PRINT("[+] Driver loading - checking system resources\n");

    status = GenerateRandomDeviceName(&g_DeviceName, &g_DosDeviceName);
	if (!NT_SUCCESS(status)) {
        DBG_PRINT("[!] Failed to generate random device name: 0x%08X\n", status);
        if (status == STATUS_INSUFFICIENT_RESOURCES) {
            DBG_PRINT("[!] System resources insufficient - check available memory\n");
        }
		return status;
	}

    DBG_PRINT("[+] Generated device name: %wZ\n", &g_DeviceName);

	// 鍒涘缓璁惧瀵硅薄
	status = KLI_CACHED_CALL(IoCreateDevice,
		DriverObject,
		0,
		&g_DeviceName,
		FILE_DEVICE_UNKNOWN,
		FILE_DEVICE_SECURE_OPEN,
		FALSE,
		&deviceObject);

	if (!NT_SUCCESS(status))
	{
        DBG_PRINT("[!] Failed to create device object: 0x%08X\n", status);

		if (g_DeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DeviceName.Buffer);
		if (g_DosDeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DosDeviceName.Buffer);
		return status;
	}

	status = KLI_CACHED_CALL(IoCreateSymbolicLink, &g_DosDeviceName, &g_DeviceName);
    if (!NT_SUCCESS(status))
    {
        DBG_PRINT("[!] Failed to create symbolic link: 0x%08X\n", status);
		KLI_CACHED_CALL(IoDeleteDevice, deviceObject);

		if (g_DeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DeviceName.Buffer);
		if (g_DosDeviceName.Buffer) KLI_CACHED_CALL(ExFreePool, g_DosDeviceName.Buffer);
		return status;
	}

	DriverObject->MajorFunction[IRP_MJ_CREATE] = DmaDispatchCreateClose;
	DriverObject->MajorFunction[IRP_MJ_CLOSE] = DmaDispatchCreateClose;
	DriverObject->MajorFunction[IRP_MJ_DEVICE_CONTROL] = DmaDeviceControl;

	DriverObject->DriverUnload = [](PDRIVER_OBJECT DriverObject)->VOID {

		if (g_DosDeviceName.Buffer) {
			KLI_CACHED_CALL(IoDeleteSymbolicLink, &g_DosDeviceName);
			KLI_CACHED_CALL(ExFreePool, g_DosDeviceName.Buffer);
		}

		if (DriverObject->DeviceObject)
		{
			KLI_CACHED_CALL(IoDeleteDevice, DriverObject->DeviceObject);
		}

		// 娓呯悊璁惧鍚嶇О缂撳啿鍖�
		if (g_DeviceName.Buffer) {
			KLI_CACHED_CALL(ExFreePool, g_DeviceName.Buffer);
		}

        DBG_PRINT("[+] DMA Driver unloaded\n");
		};

	// 锟斤拷锟斤拷锟借备锟斤拷志
	deviceObject->Flags |= DO_BUFFERED_IO;
	deviceObject->Flags &= ~DO_DEVICE_INITIALIZING;

    // 在初始化阶段缓存一次 CR3（PASSIVE_LEVEL，通常处于 System 进程上下文）
    {
        ULONG64 cr3 = __readcr3();
        if (cr3) { g_CachedCR3 = cr3; }
    }

    DBG_PRINT("[+] DMA Driver loaded successfully\n");

	return STATUS_SUCCESS;
}

