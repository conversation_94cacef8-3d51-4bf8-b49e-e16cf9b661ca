// leechrpcserver_dtb.c : Implementation of DTB-enhanced GETOPTION handler
// This file contains the enhanced GETOPTION handler that supports DTB detection

#include "../include/leechrpc.h"
#include "../include/leechrpc_h.h"
#include "../include/leechrpc.h" // for LC_OPT_MEMORYINFO_* macros (LC_OPT_* are in leechcore.h via Common.h)
#include "../include/DTBDetector.h"
#include "../include/DTBCleanup.h"
// Note: avoid including C++ class header in C translation unit
#include <stdio.h>
#include "../include/VMP.h"

// External function to get driver interface from CustomLeechServer (C-compatible)
struct DriverInterface;
extern struct DriverInterface* GetGlobalDriverInterface();

// Enhanced GETOPTION handler with DTB support
error_status_t LeechRpc_CommandGetOption_DTB(
    _In_ PLEECHRPC_MSG_DATA pReqData,
    _Out_ long *pcbOut,
    _Out_ byte **ppbOut)
{
    VMProtectBegin("RPC::GetOption_DTB");
    PLEECHRPC_MSG_DATA pRspData = NULL;
    
    // Allocate response
    if(!(pRspData = LocalAlloc(0, sizeof(LEECHRPC_MSG_DATA)))) {
        *pcbOut = 0;
        *ppbOut = NULL;
        return (error_status_t)-1;
    }
    
    // Initialize response
    pRspData->cbMsg = sizeof(LEECHRPC_MSG_DATA);
    pRspData->dwMagic = LEECHRPC_MSGMAGIC;
    pRspData->fMsgResult = FALSE;
    pRspData->tpMsg = LEECHRPC_MSGTYPE_GETOPTION_RSP;
    pRspData->qwData[0] = 0;
    
    // Handle different options
    if(pReqData) {
        QWORD qwOption = pReqData->qwData[0];
        struct DriverInterface* pDriver = GetGlobalDriverInterface();
        
        if(pDriver) {
            switch(qwOption) {
                case LC_OPT_MEMORYINFO_OS_DTB:
                    {
                        // Detect and return system DTB with cleanup and override support
                        printf("[RPC] Client requesting DTB...\n");
                        
                        // Get final DTB with cleanup and override applied
                        QWORD qwDTB = DTBCleanup_GetFinalDTB(pDriver);
                        
                        if(qwDTB) {
                            // Successfully got DTB - return cleaned value
                            pRspData->qwData[0] = qwDTB;
                            pRspData->fMsgResult = TRUE;
                            printf("[RPC] Returning cleaned DTB: 0x%llx\n", qwDTB);
                            
                            // Print diagnostics if verbose mode
                            DTBCleanup_PrintDiagnostics();
                        } else {
                            printf("[RPC] DTB detection failed - will scan on first request\n");
                            // Force rescan if initial detection failed
                            QWORD qwRawDTB = DTBDetector_GetSystemDTB(pDriver, TRUE);
                            if(qwRawDTB) {
                                // Apply cleanup to the scanned DTB
                                qwDTB = DTBCleanup_CleanDTB(qwRawDTB);
                                pRspData->qwData[0] = qwDTB;
                                pRspData->fMsgResult = TRUE;
                                printf("[RPC] DTB scan complete, returning cleaned: 0x%llx (raw: 0x%llx)\n", qwDTB, qwRawDTB);
                            } else {
                                // DTB scan failed, return 0 to indicate failure
                                printf("[RPC] DTB scan failed - client will need to scan\n");
                                pRspData->qwData[0] = 0;
                                pRspData->fMsgResult = FALSE;
                            }
                        }
                    }
                    break;
                    
                case LC_OPT_MEMORYINFO_ARCH:
                    {
                        // Return memory architecture
                        VMM_MEMORYMODEL_TP tpModel = DTBDetector_GetMemoryModel();
                        pRspData->qwData[0] = (QWORD)tpModel;
                        pRspData->fMsgResult = TRUE;
                        printf("[RPC] Returning memory model: %d\n", tpModel);
                    }
                    break;
                    
                case LC_OPT_MEMORYINFO_OS_KERNELBASE:
                    // Kernel base address (optional, not implemented yet)
                    printf("[RPC] Kernel base request - not implemented\n");
                    break;
                    
                case LC_OPT_MEMORYINFO_OS_KERNELHINT:
                    // Kernel hint (optional, not implemented yet)
                    printf("[RPC] Kernel hint request - not implemented\n");
                    break;
                    
                case 0x0200000800000000ULL: /* LC_OPT_MEMORYINFO_OS_VERSIONMAJOR */
                        // Windows major version
                        {
                            OSVERSIONINFOW osvi = { sizeof(OSVERSIONINFOW) };
                            if(GetVersionExW(&osvi)) {
                                pRspData->qwData[0] = (QWORD)osvi.dwMajorVersion;
                                pRspData->fMsgResult = TRUE;
                                printf("[RPC] Returning OS major version: %lu\n", (unsigned long)osvi.dwMajorVersion);
                            }
                        }
                    break;
                    
                case 0x0200000900000000ULL: /* LC_OPT_MEMORYINFO_OS_VERSIONMINOR */
                        // Windows minor version
                        {
                            OSVERSIONINFOW osvi = { sizeof(OSVERSIONINFOW) };
                            if(GetVersionExW(&osvi)) {
                                pRspData->qwData[0] = (QWORD)osvi.dwMinorVersion;
                                pRspData->fMsgResult = TRUE;
                                printf("[RPC] Returning OS minor version: %lu\n", (unsigned long)osvi.dwMinorVersion);
                            }
                        }
                    break;
                    
                case 0x0200000A00000000ULL: /* LC_OPT_MEMORYINFO_OS_VERSIONBUILD */
                    // Windows build number
                    {
                        OSVERSIONINFOW osvi = { sizeof(OSVERSIONINFOW) };
                        if(GetVersionExW(&osvi)) {
                            pRspData->qwData[0] = (QWORD)osvi.dwBuildNumber;
                            pRspData->fMsgResult = TRUE;
                            printf("[RPC] Returning OS build number: %lu\n", (unsigned long)osvi.dwBuildNumber);
                        }
                    }
                    break;
                    
                default:
                    printf("[RPC] Unknown option requested: 0x%llx\n", qwOption);
                    break;
            }
        } else {
            printf("[RPC] Driver interface not available\n");
        }
    }
    
    *pcbOut = pRspData->cbMsg;
    *ppbOut = (PBYTE)pRspData;
    VMProtectEnd();
    return 0;
}
