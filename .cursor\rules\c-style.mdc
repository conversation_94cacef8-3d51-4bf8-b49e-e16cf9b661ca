---
globs: *.c,*.h,*.cpp,*.hpp
description: C/C++ 编码约定（最简、轻量、性能优先；适配 MSRPC + vmm 路径）
---
# C/C++ 编码约定

- 简洁与性能（热路径优先）
  - 尽量减少抽象层；热路径避免动态分配；优先栈内存与局部对象。
  - 小型工具函数使用 `static inline`；避免在关键路径中引入虚调用/异常开销。
  - 头文件轻量化，减少级联包含；能前置声明则不包含。

- 错误处理与资源释放
  - 优先卫语句；需要集中清理时可使用 `goto cleanup` 统一释放。
  - 返回明确错误码；避免在 C ABI 边界抛出异常；C++ 内部异常需就地捕获并转为错误码。

- Windows/MSVC/clang-cl 可移植性
  - 必要时使用 SAL 注解；避免仅 GCC/Clang 可用的内建特性。
  - 跨模块 C 接口统一使用 `extern "C"`；跨 RPC 的共享结构体需显式对齐/定长。

- 安全要点
  - 所有 RPC 反序列化字段做长度与版本校验；拒绝超大/碎片化输入。
  - 所有缓冲区访问做边界检查；释放前对敏感数据清零。

- 代码可读性
  - 单一职责函数；控制嵌套深度；命名表达意图（避免缩写）。