// leechrpc_cpp_wrappers.cpp : C++ wrapper functions for C interface
//
// This file provides C++ implementations that are callable from C code
// through extern "C" linkage.
//

#include "../include/Common.h"
#include "../include/CustomLeechServer.h"
#include "../include/DriverInterface.h"
#include "../include/SessionManager.h"
#include "../include/leechrpc.h"
#include "../include/leechrpc_h.h"  // For RPC interface definitions
#include <rpc.h>  // For handle_t type

// Global functions to access C++ singletons
extern "C" {

// DriverInterface wrapper functions
BOOL DriverInterface_ReadScatter_Impl(DWORD cMEMs, PPMEM_SCATTER ppMEMs)
{
    CustomLeechServer* pServer = CustomLeechServer::GetInstance();
    if (pServer) {
        DriverInterface* pDriverInterface = pServer->GetDriverInterface();
        if (pDriverInterface) {
            return pDriverInterface->ReadScatter(cMEMs, ppMEMs);
        }
    }
    
    // No driver interface available, mark all reads as failed
    for (DWORD i = 0; i < cMEMs; i++) {
        if (ppMEMs[i]) {
            ppMEMs[i]->f = FALSE;
        }
    }
    return FALSE;
}

BOOL DriverInterface_WriteScatter_Impl(DWORD cMEMs, PPMEM_SCATTER ppMEMs)
{
    CustomLeechServer* pServer = CustomLeechServer::GetInstance();
    if (pServer) {
        DriverInterface* pDriverInterface = pServer->GetDriverInterface();
        if (pDriverInterface) {
            return pDriverInterface->WriteScatter(cMEMs, ppMEMs);
        }
    }
    
    // No driver interface available, mark all writes as failed
    for (DWORD i = 0; i < cMEMs; i++) {
        if (ppMEMs[i]) {
            ppMEMs[i]->f = FALSE;
        }
    }
    return FALSE;
}

QWORD DriverInterface_GetDTB_Impl()
{
    CustomLeechServer* pServer = CustomLeechServer::GetInstance();
    if (pServer) {
        DriverInterface* pDriverInterface = pServer->GetDriverInterface();
        if (pDriverInterface) {
            return pDriverInterface->GetDTB();
        }
    }
    return 0;
}

QWORD DriverInterface_GetKernelBase_Impl()
{
    CustomLeechServer* pServer = CustomLeechServer::GetInstance();
    if (pServer) {
        DriverInterface* pDriverInterface = pServer->GetDriverInterface();
        if (pDriverInterface) {
            return pDriverInterface->GetKernelBase();
        }
    }
    return 0;
}

// SessionManager wrapper functions
BOOL SessionManager_GetSession_Impl(DWORD dwSessionId)
{
    CustomLeechServer* pServer = CustomLeechServer::GetInstance();
    if (pServer) {
        SessionManager* pSessionManager = pServer->GetSessionManager();
        if (pSessionManager) {
            return pSessionManager->GetSession(dwSessionId);
        }
    }
    return FALSE;
}

BOOL SessionManager_AddSession_Impl(DWORD dwSessionId)
{
    CustomLeechServer* pServer = CustomLeechServer::GetInstance();
    if (pServer) {
        SessionManager* pSessionManager = pServer->GetSessionManager();
        if (pSessionManager) {
            return pSessionManager->AddSession(dwSessionId);
        }
    }
    return FALSE;
}

void SessionManager_RemoveSession_Impl(DWORD dwSessionId)
{
    CustomLeechServer* pServer = CustomLeechServer::GetInstance();
    if (pServer) {
        SessionManager* pSessionManager = pServer->GetSessionManager();
        if (pSessionManager) {
            pSessionManager->RemoveSession(dwSessionId);
        }
    }
}

void SessionManager_IncrementActiveRequests_Impl(DWORD dwSessionId)
{
    CustomLeechServer* pServer = CustomLeechServer::GetInstance();
    if (pServer) {
        SessionManager* pSessionManager = pServer->GetSessionManager();
        if (pSessionManager) {
            pSessionManager->IncrementActiveRequests(dwSessionId);
        }
    }
}

void SessionManager_DecrementActiveRequests_Impl(DWORD dwSessionId)
{
    CustomLeechServer* pServer = CustomLeechServer::GetInstance();
    if (pServer) {
        SessionManager* pSessionManager = pServer->GetSessionManager();
        if (pSessionManager) {
            pSessionManager->DecrementActiveRequests(dwSessionId);
        }
    }
}

// LeechRpc set functions (for backward compatibility)
void LeechRpc_SetDriverInterface(DriverInterface* pDriverInterface)
{
    // No longer needed - we use singleton pattern
}

void LeechRpc_SetSessionManager(SessionManager* pSessionManager)
{
    // No longer needed - we use singleton pattern
}

// Forward declaration of C function
extern "C" error_status_t LeechRpc_ReservedSubmitCommand_C(
    long cbIn,
    byte *pbIn,
    long *pcbOut,
    byte **ppbOut);

} // extern "C"

// RPC interface function (must be outside extern "C" for C++ linkage)
error_status_t LeechRpc_ReservedSubmitCommand(
    /* [in] */ handle_t hBinding,
    /* [in] */ long cbIn,
    /* [size_is][in] */ byte *pbIn,
    /* [out] */ long *pcbOut,
    /* [size_is][size_is][out] */ byte **ppbOut)
{
    // Ignore binding handle (not used in our implementation)
    // Call the C implementation
    return LeechRpc_ReservedSubmitCommand_C(cbIn, pbIn, pcbOut, ppbOut);
}
