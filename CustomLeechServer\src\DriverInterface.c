#include "../include/DriverInterface.h"

// Initialize the driver interface
BOOL DriverInterface_Initialize(PDRIVER_INTERFACE pDriverInterface, LPCSTR szDriverName)
{
    if (!pDriverInterface || !szDriverName) {
        DriverInterface_LogError("DriverInterface_Initialize", ERROR_INVALID_PARAMETER);
        return FALSE;
    }

    // Initialize structure
    ZeroMemory(pDriverInterface, sizeof(DRIVER_INTERFACE));
    strcpy_s(pDriverInterface->szDriverName, MAX_PATH_LENGTH, szDriverName);

    // Open driver handle
    pDriverInterface->hDriver = CreateFileA(
        szDriverName,
        GENERIC_READ | GENERIC_WRITE,
        0,
        NULL,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        NULL
    );

    if (pDriverInterface->hDriver == INVALID_HANDLE_VALUE) {
        DWORD dwError = GetLastError();
        DriverInterface_LogError("C<PERSON>File<PERSON>", dwError);
        return FALSE;
    }

    pDriverInterface->fInitialized = TRUE;
    DriverInterface_LogInfo("Driver interface initialized successfully");
    
    return TRUE;
}

// Close the driver interface
VOID DriverInterface_Close(PDRIVER_INTERFACE pDriverInterface)
{
    if (!pDriverInterface) {
        return;
    }

    if (pDriverInterface->hDriver != INVALID_HANDLE_VALUE) {
        CloseHandle(pDriverInterface->hDriver);
        pDriverInterface->hDriver = INVALID_HANDLE_VALUE;
    }

    pDriverInterface->fInitialized = FALSE;
    DriverInterface_LogInfo("Driver interface closed");
}

// Log error with function name and error code
VOID DriverInterface_LogError(LPCSTR szFunction, DWORD dwError)
{
    printf("[ERROR] %s failed with error code: 0x%08X\n", szFunction, dwError);
}

// Read physical memory through IOCTL
BOOL DriverInterface_ReadPhysicalMemory(PDRIVER_INTERFACE pDriverInterface, QWORD qwAddress, DWORD cbSize, PBYTE pbBuffer)
{
    if (!pDriverInterface || !pDriverInterface->fInitialized || !pbBuffer || cbSize == 0) {
        DriverInterface_LogError("DriverInterface_ReadPhysicalMemory", ERROR_INVALID_PARAMETER);
        return FALSE;
    }

    // Allocate request buffer
    DWORD cbRequestSize = sizeof(PHYSICAL_MEMORY_REQUEST) + cbSize - 1;
    PPHYSICAL_MEMORY_REQUEST pRequest = (PPHYSICAL_MEMORY_REQUEST)malloc(cbRequestSize);
    if (!pRequest) {
        DriverInterface_LogError("DriverInterface_ReadPhysicalMemory", ERROR_NOT_ENOUGH_MEMORY);
        return FALSE;
    }

    // Setup request
    pRequest->PhysicalAddress = qwAddress;
    pRequest->Size = cbSize;

    DWORD cbBytesReturned = 0;
    BOOL fResult = DeviceIoControl(
        pDriverInterface->hDriver,
        IOCTL_READ_PHYSICAL_MEMORY,
        pRequest,
        cbRequestSize,
        pRequest,
        cbRequestSize,
        &cbBytesReturned,
        NULL
    );

    if (fResult && cbBytesReturned >= sizeof(PHYSICAL_MEMORY_REQUEST)) {
        // Copy data to output buffer
        memcpy(pbBuffer, pRequest->Data, cbSize);
    } else {
        DWORD dwError = GetLastError();
        DriverInterface_LogError("DeviceIoControl (READ)", dwError);
        fResult = FALSE;
    }

    free(pRequest);
    return fResult;
}

// Write physical memory through IOCTL
BOOL DriverInterface_WritePhysicalMemory(PDRIVER_INTERFACE pDriverInterface, QWORD qwAddress, DWORD cbSize, PBYTE pbBuffer)
{
    if (!pDriverInterface || !pDriverInterface->fInitialized || !pbBuffer || cbSize == 0) {
        DriverInterface_LogError("DriverInterface_WritePhysicalMemory", ERROR_INVALID_PARAMETER);
        return FALSE;
    }

    // Allocate request buffer
    DWORD cbRequestSize = sizeof(PHYSICAL_MEMORY_REQUEST) + cbSize - 1;
    PPHYSICAL_MEMORY_REQUEST pRequest = (PPHYSICAL_MEMORY_REQUEST)malloc(cbRequestSize);
    if (!pRequest) {
        DriverInterface_LogError("DriverInterface_WritePhysicalMemory", ERROR_NOT_ENOUGH_MEMORY);
        return FALSE;
    }

    // Setup request
    pRequest->PhysicalAddress = qwAddress;
    pRequest->Size = cbSize;
    memcpy(pRequest->Data, pbBuffer, cbSize);

    DWORD cbBytesReturned = 0;
    BOOL fResult = DeviceIoControl(
        pDriverInterface->hDriver,
        IOCTL_WRITE_PHYSICAL_MEMORY,
        pRequest,
        cbRequestSize,
        NULL,
        0,
        &cbBytesReturned,
        NULL
    );

    if (!fResult) {
        DWORD dwError = GetLastError();
        DriverInterface_LogError("DeviceIoControl (WRITE)", dwError);
    }

    free(pRequest);
    return fResult;
}

// Log error with function name and error code
VOID DriverInterface_LogError(LPCSTR szFunction, DWORD dwError)
{
    printf("[ERROR] %s failed with error code: 0x%08X\n", szFunction, dwError);
}

// Read scatter - batch memory read operations
BOOL DriverInterface_ReadScatter(PDRIVER_INTERFACE pDriverInterface, DWORD cMEMs, PPMEM_SCATTER ppMEMs)
{
    if (!pDriverInterface || !pDriverInterface->fInitialized || !ppMEMs || cMEMs == 0) {
        DriverInterface_LogError("DriverInterface_ReadScatter", ERROR_INVALID_PARAMETER);
        return FALSE;
    }

    BOOL fOverallSuccess = TRUE;
    
    // Process each scatter request individually
    for (DWORD i = 0; i < cMEMs; i++) {
        PMEM_SCATTER pMEM = ppMEMs[i];
        if (!pMEM || !pMEM->pb) {
            continue;
        }

        // Attempt to read memory
        BOOL fSuccess = DriverInterface_ReadPhysicalMemory(
            pDriverInterface,
            pMEM->qwA,
            pMEM->cb,
            pMEM->pb
        );

        // Update scatter status
        if (fSuccess) {
            pMEM->f = TRUE;  // Mark as successful
        } else {
            pMEM->f = FALSE; // Mark as failed
            fOverallSuccess = FALSE;
        }
    }

    return fOverallSuccess;
}

// Write scatter - batch memory write operations
BOOL DriverInterface_WriteScatter(PDRIVER_INTERFACE pDriverInterface, DWORD cMEMs, PPMEM_SCATTER ppMEMs)
{
    if (!pDriverInterface || !pDriverInterface->fInitialized || !ppMEMs || cMEMs == 0) {
        DriverInterface_LogError("DriverInterface_WriteScatter", ERROR_INVALID_PARAMETER);
        return FALSE;
    }

    BOOL fOverallSuccess = TRUE;
    
    // Process each scatter request individually
    for (DWORD i = 0; i < cMEMs; i++) {
        PMEM_SCATTER pMEM = ppMEMs[i];
        if (!pMEM || !pMEM->pb) {
            continue;
        }

        // Attempt to write memory
        BOOL fSuccess = DriverInterface_WritePhysicalMemory(
            pDriverInterface,
            pMEM->qwA,
            pMEM->cb,
            pMEM->pb
        );

        // Update scatter status
        if (fSuccess) {
            pMEM->f = TRUE;  // Mark as successful
        } else {
            pMEM->f = FALSE; // Mark as failed
            fOverallSuccess = FALSE;
        }
    }

    return fOverallSuccess;
}

// Log error with function name and error code
VOID DriverInterface_LogError(LPCSTR szFunction, DWORD dwError)
{
    printf("[ERROR] %s failed with error code: 0x%08X\n", szFunction, dwError);
}

// Log informational message
VOID DriverInterface_LogInfo(LPCSTR szMessage)
{
    printf("[INFO] %s\n", szMessage);
}