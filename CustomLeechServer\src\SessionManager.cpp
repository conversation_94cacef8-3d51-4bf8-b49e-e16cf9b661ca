#include "../include/SessionManager.h"
#include "../include/CustomLeechServer.h"
#include "../include/VMP.h"
#include <iostream>

//-----------------------------------------------------------------------------
// Constructor/Destructor
//-----------------------------------------------------------------------------

SessionManager::SessionManager()
    : fInitialized(FALSE)
    , hCleanupThread(NULL)
    , fCleanupThreadRunning(FALSE)
{
    VMProtectBeginUltra("Session::Ctor");
    ZeroMemory(sessions, sizeof(sessions));
    VMProtectEnd();
}

SessionManager::~SessionManager()
{
    Shutdown();
}

//-----------------------------------------------------------------------------
// Initialization
//-----------------------------------------------------------------------------

BOOL SessionManager::Initialize()
{
    VMProtectBegin("Session::Initialize");
    if (fInitialized) {
        VMProtectEnd();
        return TRUE;
    }

    // Initialize critical section
    InitializeCriticalSection(&csLock);

    // Create cleanup thread
    hCleanupThread = CreateThread(
        NULL,
        0,
        CleanupThread,
        this,
        0,
        NULL
    );

    if (!hCleanupThread) {
        DeleteCriticalSection(&csLock);
        DBG_PRINTF("Error: Failed to create session cleanup thread\n");
        VMProtectEnd();
        return FALSE;
    }

    fInitialized = TRUE;
    DBG_PRINTF("SessionManager initialized successfully\n");
    VMProtectEnd();
    return TRUE;
}

VOID SessionManager::Shutdown()
{
    VMProtectBegin("Session::Shutdown");
    if (!fInitialized) {
        VMProtectEnd();
        return;
    }

    // Stop cleanup thread
    fCleanupThreadRunning = FALSE;
    if (hCleanupThread) {
        WaitForSingleObject(hCleanupThread, 5000);
        CloseHandle(hCleanupThread);
        hCleanupThread = NULL;
    }

    // Remove all sessions
    RemoveAllSessions();

    // Cleanup critical section
    DeleteCriticalSection(&csLock);

    fInitialized = FALSE;
    DBG_PRINTF("SessionManager shutdown completed\n");
    VMProtectEnd();
}

//-----------------------------------------------------------------------------
// Session Management
//-----------------------------------------------------------------------------

BOOL SessionManager::AddSession(DWORD dwClientID)
{
    VMProtectBeginVirtualization("Session::Add");
    if (!fInitialized || dwClientID == 0) {
        VMProtectEnd();
        return FALSE;
    }

    EnterCriticalSection(&csLock);

    // Find empty slot
    for (int i = 0; i < MAX_CLIENTS; i++) {
        if (!sessions[i].fActive) {
            sessions[i].dwClientID = dwClientID;
            sessions[i].qwLastActivity = GetTickCount64();
            sessions[i].cActiveRequests = 0;
            sessions[i].fActive = TRUE;
            
            LeaveCriticalSection(&csLock);
            DBG_PRINTF("Session added: Client ID %08lX\n", (unsigned long)dwClientID);
            VMProtectEnd();
            return TRUE;
        }
    }

    LeaveCriticalSection(&csLock);
    DBG_PRINTF("Error: No available session slots for Client ID %08lX\n", (unsigned long)dwClientID);
    VMProtectEnd();
    return FALSE;
}

BOOL SessionManager::GetSession(DWORD dwClientID)
{
    VMProtectBeginVirtualization("Session::Get");
    if (!fInitialized || dwClientID == 0) {
        VMProtectEnd();
        return FALSE;
    }

    EnterCriticalSection(&csLock);

    for (int i = 0; i < MAX_CLIENTS; i++) {
        if (sessions[i].fActive && sessions[i].dwClientID == dwClientID) {
            sessions[i].qwLastActivity = GetTickCount64();
            LeaveCriticalSection(&csLock);
            VMProtectEnd();
            return TRUE;
        }
    }

    LeaveCriticalSection(&csLock);
    VMProtectEnd();
    return FALSE;
}

VOID SessionManager::UpdateActivity(DWORD dwClientID)
{
    VMProtectBegin("Session::Update");
    if (!fInitialized || dwClientID == 0) {
        VMProtectEnd();
        return;
    }

    EnterCriticalSection(&csLock);

    for (int i = 0; i < MAX_CLIENTS; i++) {
        if (sessions[i].fActive && sessions[i].dwClientID == dwClientID) {
            sessions[i].qwLastActivity = GetTickCount64();
            break;
        }
    }

    LeaveCriticalSection(&csLock);
    VMProtectEnd();
}

VOID SessionManager::RemoveSession(DWORD dwClientID)
{
    VMProtectBegin("Session::Remove");
    if (!fInitialized || dwClientID == 0) {
        VMProtectEnd();
        return;
    }

    EnterCriticalSection(&csLock);

    for (int i = 0; i < MAX_CLIENTS; i++) {
        if (sessions[i].fActive && sessions[i].dwClientID == dwClientID) {
            // Wait for active requests to complete
            while (sessions[i].cActiveRequests > 0) {
                LeaveCriticalSection(&csLock);
                Sleep(10);
                EnterCriticalSection(&csLock);
            }

            sessions[i].fActive = FALSE;
            sessions[i].dwClientID = 0;
            sessions[i].qwLastActivity = 0;
            sessions[i].cActiveRequests = 0;
            
            DBG_PRINTF("Session removed: Client ID %08lX\n", (unsigned long)dwClientID);
            break;
        }
    }

    LeaveCriticalSection(&csLock);
    VMProtectEnd();
}

VOID SessionManager::IncrementActiveRequests(DWORD dwClientID)
{
    VMProtectBegin("Session::IncReq");
    if (!fInitialized || dwClientID == 0) {
        VMProtectEnd();
        return;
    }

    EnterCriticalSection(&csLock);

    for (int i = 0; i < MAX_CLIENTS; i++) {
        if (sessions[i].fActive && sessions[i].dwClientID == dwClientID) {
            sessions[i].cActiveRequests++;
            sessions[i].qwLastActivity = GetTickCount64();
            break;
        }
    }

    LeaveCriticalSection(&csLock);
    VMProtectEnd();
}

VOID SessionManager::DecrementActiveRequests(DWORD dwClientID)
{
    VMProtectBegin("Session::DecReq");
    if (!fInitialized || dwClientID == 0) {
        VMProtectEnd();
        return;
    }

    EnterCriticalSection(&csLock);

    for (int i = 0; i < MAX_CLIENTS; i++) {
        if (sessions[i].fActive && sessions[i].dwClientID == dwClientID) {
            if (sessions[i].cActiveRequests > 0) {
                sessions[i].cActiveRequests--;
            }
            break;
        }
    }

    LeaveCriticalSection(&csLock);
    VMProtectEnd();
}

//-----------------------------------------------------------------------------
// Utility
//-----------------------------------------------------------------------------

DWORD SessionManager::GetActiveSessionCount()
{
    VMProtectBeginVirtualization("Session::Count");
    if (!fInitialized) {
        VMProtectEnd();
        return 0;
    }

    DWORD count = 0;
    EnterCriticalSection(&csLock);

    for (int i = 0; i < MAX_CLIENTS; i++) {
        if (sessions[i].fActive) {
            count++;
        }
    }

    LeaveCriticalSection(&csLock);
    VMProtectEnd();
    return count;
}

BOOL SessionManager::HasActiveSessions()
{
    VMProtectBeginVirtualization("Session::HasActive");
    if (!fInitialized) {
        VMProtectEnd();
        return FALSE;
    }

    BOOL hasActive = FALSE;
    EnterCriticalSection(&csLock);

    for (int i = 0; i < MAX_CLIENTS; i++) {
        if (sessions[i].fActive) {
            hasActive = TRUE;
            break;
        }
    }

    LeaveCriticalSection(&csLock);
    VMProtectEnd();
    return hasActive;
}

VOID SessionManager::RemoveAllSessions()
{
    VMProtectBegin("Session::RemoveAll");
    if (!fInitialized) {
        VMProtectEnd();
        return;
    }

    EnterCriticalSection(&csLock);

    for (int i = 0; i < MAX_CLIENTS; i++) {
        if (sessions[i].fActive) {
            sessions[i].fActive = FALSE;
            sessions[i].dwClientID = 0;
            sessions[i].qwLastActivity = 0;
            sessions[i].cActiveRequests = 0;
        }
    }

    LeaveCriticalSection(&csLock);
    DBG_PRINTF("All sessions removed\n");
    VMProtectEnd();
}

//-----------------------------------------------------------------------------
// Private Methods
//-----------------------------------------------------------------------------

DWORD WINAPI SessionManager::CleanupThread(LPVOID lpParam)
{
    VMProtectBeginMutation("Session::CleanupThread");
    SessionManager* pManager = static_cast<SessionManager*>(lpParam);
    if (!pManager) {
        VMProtectEnd();
        return 1;
    }

    pManager->fCleanupThreadRunning = TRUE;

    while (pManager->fCleanupThreadRunning) {
        Sleep(5000); // Check every 5 seconds
        pManager->CleanupTimeoutSessions();
    }

    VMProtectEnd();
    return 0;
}

VOID SessionManager::CleanupTimeoutSessions()
{
    VMProtectBeginVirtualization("Session::CleanupTimeout");
    if (!fInitialized) {
        VMProtectEnd();
        return;
    }

    QWORD qwCurrentTime = GetTickCount64();
    
    EnterCriticalSection(&csLock);

    for (int i = 0; i < MAX_CLIENTS; i++) {
        if (sessions[i].fActive) {
            QWORD qwTimeSinceActivity = qwCurrentTime - sessions[i].qwLastActivity;
            
            if (qwTimeSinceActivity > SESSION_TIMEOUT_MS) {
                DWORD dwClientID = sessions[i].dwClientID;
                
                // Wait for active requests to complete
                while (sessions[i].cActiveRequests > 0) {
                    LeaveCriticalSection(&csLock);
                    Sleep(10);
                    EnterCriticalSection(&csLock);
                }

                sessions[i].fActive = FALSE;
                sessions[i].dwClientID = 0;
                sessions[i].qwLastActivity = 0;
                sessions[i].cActiveRequests = 0;
                
                DBG_PRINTF("Session timeout: Client ID %08lX (inactive for %llu s)\n", (unsigned long)dwClientID, (unsigned long long)(qwTimeSinceActivity / 1000));
            }
        }
    }

    LeaveCriticalSection(&csLock);
    VMProtectEnd();
}

// Global C function to get session manager (for use in C code)
extern "C" SessionManager* GetGlobalSessionManager() {
    CustomLeechServer* pServer = CustomLeechServer::GetInstance();
    if (pServer) {
        return pServer->GetSessionManager();
    }
    return nullptr;
}
