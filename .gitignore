﻿## Project specific
/LeechCore/
/libleechgrpc-master/
/MemProcFS/
/MemProcfs/
.vs/
BatchEncryption测试/

## Visual Studio / VS Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

## Mono / JetBrains Rider
*.userprefs
.idea/
*.sln.iml

## Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/
*.log
## Compiled binaries and libraries
*.exe
*.dll
*.lib
*.exp
*.sys
*.map
*.efi
*.bin
*.hex
*.zip
*.pdb
## Visual C++ intermediates
ipch/
*.aps
*.ncb
*.opendb
*.opensdf
*.sdf
*.cachefile
*.VC.db
*.VC.VC.opendb
*.idb
*.pdb
*.iobj
*.ipdb
*.ilk
*.tlog
*.pch
*.obj

## Test results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

## Installers
*.cab
*.msi
*.msm
*.msp

## Team Foundation Server
$tf/

## Code analysis
*.CodeAnalysisLog.xml
*.CodeAnalysisIssue.json

## CMake
[Cc]MakeCache.txt
[Cc]MakeFiles/
[Cc]MakeScripts/
[Cc]Test*/*
[Cc]MakeLists.txt.user
cmake-build-*/
CMakeSettings.json

## OS generated files
# Windows
Thumbs.db
ehthumbs.db
[Dd]esktop.ini
$RECYCLE.BIN/
# macOS
.DS_Store

## Misc
*.tmp
*.log
*.psess
*.vsp
*.vspx
*.sap
CustomLeechServer_cpp.log
vc143.pdb
CustomLeechServer/x64/Release/ReadPhys.map
CustomLeechServer/x64/Release/ReadPhys.sys
CustomLeechServer/x64/Release/ReadPhys/ReadPhys.sys

# YoYo AI version control directory
.yoyo/
