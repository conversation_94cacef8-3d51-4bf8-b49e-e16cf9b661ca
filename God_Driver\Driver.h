// God_Driver - Minimal kernel driver to read/write physical memory for LeechCore/MemProcFS
// This driver exposes IOCTLs compatible with existing user-mode stubs in this workspace.

#pragma once

#if !defined(__has_include)
#define __has_include(x) 0
#endif
#if __has_include(<ntddk.h>)
#include <ntddk.h>
#else
#include "kernel_stubs.h" // lint-only fallback stubs (does not replace WDK)
#endif

// Device names
#define GODDRV_DEVICE_NAME      L"\\Device\\YourDmaDriver"
#define GODDRV_SYMLINK_NAME     L"\\DosDevices\\YourDmaDriver"

// Named pipe for R3 communication (aligns with our IDA analysis: ZwCreate* + ZwRead/WriteFile)
#define GODDRV_PIPE_NAME         L"\\Device\\NamedPipe\\GodLeechCore"

// Simple DMA command header (pipe protocol)
typedef struct _GODDMA_HEADER {
    ULONG32 Magic;      // 'GDMA'
    ULONG32 Command;    // 1=READ, 2=WRITE, 0x10=GET_DTB
    QWORD64 Address;    // physical address
    ULONG32 Length;     // length of payload or requested bytes
    ULONG32 Reserved;   // alignment/padding
} GODDMA_HEADER, *PGODDMA_HEADER;

#define GODDMA_MAGIC 0x414D4447u // 'GDMA'
#define GODDMA_CMD_READ  1u
#define GODDMA_CMD_WRITE 2u
#define GODDMA_CMD_GETDTB 0x10u

// IOCTLs (keep exactly in sync with WinUsbHook/dma_network_server.c)
#ifndef FILE_DEVICE_UNKNOWN
#define FILE_DEVICE_UNKNOWN 0x00000022
#endif

#ifndef CTL_CODE
#define CTL_CODE(DeviceType, Function, Method, Access) (                \
    ((DeviceType) << 16) | ((Access) << 14) | ((Function) << 2) | (Method) )
#endif

#ifndef METHOD_BUFFERED
#define METHOD_BUFFERED 0
#endif

#ifndef FILE_ANY_ACCESS
#define FILE_ANY_ACCESS 0
#endif

#define IOCTL_READ_PHYSICAL_MEMORY   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_WRITE_PHYSICAL_MEMORY  CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_DMA_GET_DTB            CTL_CODE(FILE_DEVICE_UNKNOWN, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)

typedef unsigned char  UCHAR8;
typedef unsigned short USHORT16;
typedef unsigned long  ULONG32;
typedef unsigned long long QWORD64;

// I/O structure used by user-mode helpers in this workspace
typedef struct _PHYSICAL_MEMORY_REQUEST {
    QWORD64 PhysicalAddress;
    ULONG32 Length;
    UCHAR8  Data[1];
} PHYSICAL_MEMORY_REQUEST, *PPHYSICAL_MEMORY_REQUEST;

NTSTATUS DriverEntry(_In_ PDRIVER_OBJECT DriverObject, _In_ PUNICODE_STRING RegistryPath);
void     GodDrvUnload(_In_ PDRIVER_OBJECT DriverObject);

_Dispatch_type_(IRP_MJ_CREATE)
NTSTATUS GodDrvCreateClose(_In_ PDEVICE_OBJECT DeviceObject, _In_ PIRP Irp);

_Dispatch_type_(IRP_MJ_DEVICE_CONTROL)
NTSTATUS GodDrvDeviceControl(_In_ PDEVICE_OBJECT DeviceObject, _In_ PIRP Irp);

// Helpers
NTSTATUS GodReadPhysical(_In_ PHYSICAL_ADDRESS phys, _Out_writes_bytes_(length) PVOID outBuffer, _In_ SIZE_T length);
NTSTATUS GodWritePhysical(_In_ PHYSICAL_ADDRESS phys, _In_reads_bytes_(length) PVOID inBuffer, _In_ SIZE_T length);

// Limit a single request to a reasonable size to avoid excessive mapping
#define GODDRV_MAX_TRANSFER (1u * 1024u * 1024u) // 1 MiB per request


