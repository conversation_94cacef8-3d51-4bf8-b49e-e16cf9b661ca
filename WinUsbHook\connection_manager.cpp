// connection_manager.cpp - 全局连接管理器实现
#include "connection_manager.h"
#include "console_ui.h"
#include <iostream>
#include <WS2tcpip.h>
#include <Iphlpapi.h>

ConnectionManager* ConnectionManager::instance = nullptr;
CRITICAL_SECTION ConnectionManager::cs = {0};
bool ConnectionManager::csInitialized = false;

ConnectionManager::ConnectionManager() : initialized(false), userInteractionCompleted(false) {
    memset(serverIP, 0, sizeof(serverIP));
    memset(remoteUri, 0, sizeof(remoteUri));
}

ConnectionManager* ConnectionManager::GetInstance() {
    if (!csInitialized) {
        InitializeCriticalSection(&cs);
        csInitialized = true;
    }
    
    EnterCriticalSection(&cs);
    if (!instance) {
        instance = new ConnectionManager();
    }
    LeaveCriticalSection(&cs);
    return instance;
}

void ConnectionManager::Cleanup() {
    if (csInitialized) {
        EnterCriticalSection(&cs);
        if (instance) {
            delete instance;
            instance = nullptr;
        }
        LeaveCriticalSection(&cs);
        DeleteCriticalSection(&cs);
        csInitialized = false;
    }
}

// 从原始代码中提取并改造的工具函数
static BOOL GetPrimaryIPv4AndPrefix(ULONG* pIpHostOrder, ULONG* pPrefixLen) {
    if (!pIpHostOrder || !pPrefixLen) return FALSE;
    *pIpHostOrder = 0; *pPrefixLen = 0;

    ULONG flags = GAA_FLAG_SKIP_ANYCAST | GAA_FLAG_SKIP_MULTICAST | GAA_FLAG_SKIP_DNS_SERVER | GAA_FLAG_INCLUDE_PREFIX;
    ULONG cb = 0;
    GetAdaptersAddresses(AF_INET, flags, NULL, NULL, &cb);
    PIP_ADAPTER_ADDRESSES pAddrs = (PIP_ADAPTER_ADDRESSES)LocalAlloc(LMEM_ZEROINIT, cb);
    if (!pAddrs) return FALSE;
    DWORD rv = GetAdaptersAddresses(AF_INET, flags, NULL, pAddrs, &cb);
    if (rv != NO_ERROR) { LocalFree(pAddrs); return FALSE; }
    BOOL found = FALSE;
    for (PIP_ADAPTER_ADDRESSES p = pAddrs; p; p = p->Next) {
        if (p->OperStatus != IfOperStatusUp) continue;
        if (p->IfType == IF_TYPE_SOFTWARE_LOOPBACK) continue;
        if (p->IfType != IF_TYPE_ETHERNET_CSMACD && p->IfType != IF_TYPE_IEEE80211) continue;
        for (PIP_ADAPTER_UNICAST_ADDRESS ua = p->FirstUnicastAddress; ua; ua = ua->Next) {
            if (!ua->Address.lpSockaddr) continue;
            if (ua->Address.lpSockaddr->sa_family != AF_INET) continue;
            ULONG prefix = ua->OnLinkPrefixLength;
            if (prefix > 32) prefix = 24;
            SOCKADDR_IN* si = (SOCKADDR_IN*)ua->Address.lpSockaddr;
            ULONG ipHostOrder = ntohl(si->sin_addr.s_addr);
            if (ipHostOrder == 0) continue;
            *pIpHostOrder = ipHostOrder;
            *pPrefixLen = prefix;
            found = TRUE;
            break;
        }
        if (found) break;
    }
    LocalFree(pAddrs);
    return found;
}

static BOOL IsTcpPortOpenFast(const char* ipStr, USHORT port, int timeoutMs) {
    if (!ipStr) return FALSE;
    SOCKET s = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (s == INVALID_SOCKET) return FALSE;
    u_long nonBlock = 1; ioctlsocket(s, FIONBIO, &nonBlock);
    SOCKADDR_IN addr = { 0 };
    addr.sin_family = AF_INET;
    addr.sin_port = htons(port);
    if (InetPtonA(AF_INET, ipStr, &addr.sin_addr) != 1) { closesocket(s); return FALSE; }
    int r = connect(s, (SOCKADDR*)&addr, sizeof(addr));
    if (r == 0) { closesocket(s); return TRUE; }
    int wsaErr = WSAGetLastError();
    if (wsaErr != WSAEWOULDBLOCK && wsaErr != WSAEINPROGRESS) { closesocket(s); return FALSE; }
    fd_set wfds; FD_ZERO(&wfds); FD_SET(s, &wfds);
    TIMEVAL tv; tv.tv_sec = 0; tv.tv_usec = timeoutMs * 1000;
    int sel = select(0, NULL, &wfds, NULL, &tv);
    if (sel > 0 && FD_ISSET(s, &wfds)) {
        int soErr = 0; int optLen = sizeof(soErr);
        if (getsockopt(s, SOL_SOCKET, SO_ERROR, (char*)&soErr, &optLen) == 0 && soErr == 0) {
            closesocket(s); return TRUE;
        }
    }
    closesocket(s);
    return FALSE;
}

static BOOL DiscoverServerIpInLAN(char* outIp, size_t cchOut, USHORT port) {
    if (!outIp || cchOut < 16) return FALSE;
    outIp[0] = '\0';
    WSADATA w; if (WSAStartup(MAKEWORD(2, 2), &w) != 0) { return FALSE; }
    ULONG ipHost = 0, prefix = 0;
    if (!GetPrimaryIPv4AndPrefix(&ipHost, &prefix)) { WSACleanup(); return FALSE; }
    ULONG net24 = ipHost & 0xFFFFFF00UL;
    const int timeoutMs = 120;
    for (ULONG host = 1; host <= 254; ++host) {
        ULONG cand = net24 | host;
        if (cand == ipHost) continue;
        IN_ADDR a; a.s_addr = htonl(cand);
        char ipStr[INET_ADDRSTRLEN] = { 0 };
        if (!InetNtopA(AF_INET, &a, ipStr, sizeof(ipStr))) continue;
        if (IsTcpPortOpenFast(ipStr, port, timeoutMs)) {
            strncpy_s(outIp, cchOut, ipStr, _TRUNCATE);
            LOG_INFO("Discovered server at %s", ipStr);
            WSACleanup();
            return TRUE;
        }
    }
    WSACleanup();
    return FALSE;
}

bool ConnectionManager::GetServerConnection(char* outIP, size_t ipSize, char* outUri, size_t uriSize) {
    EnterCriticalSection(&cs);
    
    // ???????????????????????????
    if (initialized && userInteractionCompleted) {
        if (outIP && ipSize > 0) {
            strncpy_s(outIP, ipSize, serverIP, _TRUNCATE);
        }
        if (outUri && uriSize > 0) {
            strncpy_s(outUri, uriSize, remoteUri, _TRUNCATE);
        }
        LeaveCriticalSection(&cs);
        return true;
    }
    
    // 第一次调用：需要用户交互
    if (!userInteractionCompleted) {
        char discoveredIp[16] = { 0 };
        const USHORT rpcPort = 28473;
        BOOL fFound = FALSE;

        // ??????????
        std::cout << "============================================" << std::endl;
        std::cout << "???????" << std::endl;
        std::cout << "============================================" << std::endl;
        std::cout << "1. ???????????????" << std::endl;
        std::cout << "2. ???????????IP???" << std::endl;
        std::cout << "????? (1-2): ";
        
        int choice = 1;
        char input[16] = {0};
        if (fgets(input, sizeof(input), stdin)) {
            choice = atoi(input);
        }

        if (choice == 2) {
            // ???????IP
            LOG_INFO("??????????IP???:");
            char ipInput[32] = {0};
            if (fgets(ipInput, sizeof(ipInput), stdin)) {
                ipInput[strcspn(ipInput, "\r\n")] = 0;
                LOG_INFO("????????????:%s", ipInput);
                
                if (IsTcpPortOpenFast(ipInput, rpcPort, 1000)) {
                    strcpy_s(discoveredIp, sizeof(discoveredIp), ipInput);
                    fFound = TRUE;
                    LOG_SUCCESS("????????????:%s", ipInput);
                } else {
                    LOG_ERROR("???????????????:%s", ipInput);
                }
            }
        } else {
            // ??????
            LOG_INFO("????????????????...");
            fFound = DiscoverServerIpInLAN(discoveredIp, sizeof(discoveredIp), rpcPort);
            if (fFound) {
                LOG_SUCCESS("????????:%s", discoveredIp);
            }
        }

        if (!fFound || discoveredIp[0] == '\0') {
            LOG_ERROR("δ???????????");
            MessageBoxA(NULL, "δ???????????", "???????", MB_ICONERROR | MB_OK);
            LeaveCriticalSection(&cs);
            return false;
        }

        // ??????
        strcpy_s(serverIP, sizeof(serverIP), discoveredIp);
        sprintf_s(remoteUri, sizeof(remoteUri), "rpc://insecure:%s:nocompress", discoveredIp);
        
        userInteractionCompleted = true;
        initialized = true;
        
        LOG_SUCCESS("?????????????????: %s", serverIP);
    }
    
    // ??????
    if (outIP && ipSize > 0) {
        strncpy_s(outIP, ipSize, serverIP, _TRUNCATE);
    }
    if (outUri && uriSize > 0) {
        strncpy_s(outUri, uriSize, remoteUri, _TRUNCATE);
    }
    
    LeaveCriticalSection(&cs);
    return true;
}

void ConnectionManager::Reset() {
    EnterCriticalSection(&cs);
    initialized = false;
    userInteractionCompleted = false;
    memset(serverIP, 0, sizeof(serverIP));
    memset(remoteUri, 0, sizeof(remoteUri));
    LeaveCriticalSection(&cs);
}