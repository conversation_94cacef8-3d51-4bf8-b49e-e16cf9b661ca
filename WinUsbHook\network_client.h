#pragma once

#include <windows.h>
#include <ws2tcpip.h>
#include <iphlpapi.h>
#include <string>

#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "iphlpapi.lib")

namespace NetworkClient {

    // 网络连接配置
    struct ConnectionConfig {
        USHORT handshakePort = 28474;      // 握手端口
        USHORT rpcPort = 28473;            // RPC服务端口
        int timeoutMs = 1000;              // 连接超时时间
        bool enableInteractiveMode = true;  // 是否启用交互式选择
    };

    // 连接结果
    struct ConnectionResult {
        bool success = false;
        std::string serverIP;
        std::string errorMessage;
        std::string rpcUri;  // 生成的RPC连接字符串
    };

    // 初始化网络模块
    bool Initialize();

    // 清理网络模块
    void Cleanup();

    // 获取本机主要IPv4地址和前缀
    bool GetPrimaryIPv4AndPrefix(ULONG* ipHost, ULONG* prefix);

    // 验证服务器握手
    bool VerifyServerHandshake(const char* serverIP, USHORT port, int timeoutMs);

    // 在局域网中发现服务器
    bool DiscoverServerInLAN(std::string& outServerIP, USHORT handshakePort);

    // 交互式连接到服务器（支持自动发现和手动输入）
    ConnectionResult ConnectToServer(const ConnectionConfig& config = ConnectionConfig{});

    // 手动连接到指定服务器
    ConnectionResult ConnectToSpecificServer(const std::string& serverIP, const ConnectionConfig& config = ConnectionConfig{});

    // 生成RPC连接URI
    std::string GenerateRpcUri(const std::string& serverIP, bool insecure = true, bool nocompress = true);

} // namespace NetworkClient