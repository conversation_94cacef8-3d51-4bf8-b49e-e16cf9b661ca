// CustomLeechServer.c : implementation of CustomLeechServer class
//
#include "../include/CustomLeechServer.h"
#include "../include/leechrpc_h.h"
#include <leechgrpc.h>
#include <rpc.h>
#include <rpcndr.h>

// Global server instance
static CUSTOM_LEECH_SERVER g_Server = { 0 };

//-----------------------------------------------------------------------------
// CONFIGURATION FUNCTIONS
//-----------------------------------------------------------------------------

VOID CustomLeechServer_InitializeDefaultConfig(PCUSTOM_SERVER_CONFIG pConfig)
{
    if (!pConfig) return;
    
    ZeroMemory(pConfig, sizeof(CUSTOM_SERVER_CONFIG));
    strcpy_s(pConfig->szListenAddress, MAX_PATH_LENGTH, "0.0.0.0");
    pConfig->dwPort = DEFAULT_GRPC_PORT;
    pConfig->fInsecure = TRUE;  // Default to insecure for testing
    strcpy_s(pConfig->szDriverName, MAX_PATH_LENGTH, "\\\\.\\CustomMemoryDriver");
}

BOOL CustomLeechServer_ParseCommandLine(int argc, char* argv[], PCUSTOM_SERVER_CONFIG pConfig)
{
    int i;
    
    if (!pConfig) return FALSE;
    
    // Initialize with defaults first
    CustomLeechServer_InitializeDefaultConfig(pConfig);
    
    for (i = 1; i < argc; i++) {
        if (_stricmp(argv[i], "-h") == 0 || _stricmp(argv[i], "--help") == 0) {
            CustomLeechServer_PrintUsage();
            return FALSE;
        }
        else if (_stricmp(argv[i], "-p") == 0 || _stricmp(argv[i], "--port") == 0) {
            if (i + 1 < argc) {
                pConfig->dwPort = (DWORD)atoi(argv[++i]);
                if (pConfig->dwPort == 0 || pConfig->dwPort > 65535) {
                    printf("Error: Invalid port number: %s\n", argv[i]);
                    return FALSE;
                }
            } else {
                printf("Error: --port requires a value\n");
                return FALSE;
            }
        }
        else if (_stricmp(argv[i], "-a") == 0 || _stricmp(argv[i], "--address") == 0) {
            if (i + 1 < argc) {
                strcpy_s(pConfig->szListenAddress, MAX_PATH_LENGTH, argv[++i]);
            } else {
                printf("Error: --address requires a value\n");
                return FALSE;
            }
        }
        else if (_stricmp(argv[i], "-d") == 0 || _stricmp(argv[i], "--driver") == 0) {
            if (i + 1 < argc) {
                strcpy_s(pConfig->szDriverName, MAX_PATH_LENGTH, argv[++i]);
            } else {
                printf("Error: --driver requires a value\n");
                return FALSE;
            }
        }
        else if (_stricmp(argv[i], "--secure") == 0) {
            pConfig->fInsecure = FALSE;
        }
        else if (_stricmp(argv[i], "--insecure") == 0) {
            pConfig->fInsecure = TRUE;
        }
        else {
            printf("Error: Unknown argument: %s\n", argv[i]);
            return FALSE;
        }
    }
    
    return TRUE;
}

VOID CustomLeechServer_PrintUsage(void)
{
    printf("CustomLeechServer - gRPC Memory Access Server\n");
    printf("Usage: CustomLeechServer [options]\n");
    printf("\nOptions:\n");
    printf("  -h, --help              Show this help message\n");
    printf("  -p, --port <port>       Listen port (default: %d)\n", DEFAULT_GRPC_PORT);
    printf("  -a, --address <addr>    Listen address (default: 0.0.0.0)\n");
    printf("  -d, --driver <name>     Driver device name (default: \\\\.\\CustomMemoryDriver)\n");
    printf("  --secure                Use secure gRPC connection (requires certificates)\n");
    printf("  --insecure              Use insecure gRPC connection (default)\n");
    printf("\nExample:\n");
    printf("  CustomLeechServer --port 28474 --address 127.0.0.1 --insecure\n");
}//
-----------------------------------------------------------------------------
// SERVER INITIALIZATION FUNCTIONS
//-----------------------------------------------------------------------------

BOOL CustomLeechServer_Initialize(PCUSTOM_LEECH_SERVER pServer, PCUSTOM_SERVER_CONFIG pConfig)
{
    if (!pServer || !pConfig) {
        printf("Error: Invalid parameters for server initialization\n");
        return FALSE;
    }
    
    // Initialize server structure
    ZeroMemory(pServer, sizeof(CUSTOM_LEECH_SERVER));
    memcpy(&pServer->Config, pConfig, sizeof(CUSTOM_SERVER_CONFIG));
    
    // Initialize driver interface
    if (!DriverInterface_Initialize(&pServer->DriverInterface, pConfig->szDriverName)) {
        printf("Error: Failed to initialize driver interface\n");
        return FALSE;
    }
    
    // Initialize RPC system
    LeechRpcOnLoadInitialize();
    LeechRpc_SetDriverInterface(&pServer->DriverInterface);
    
    // Initialize gRPC server
    if (!CustomLeechServer_InitializeGrpcServer(pServer)) {
        printf("Error: Failed to initialize gRPC server\n");
        DriverInterface_Close(&pServer->DriverInterface);
        LeechRpcOnUnloadClose();
        return FALSE;
    }
    
    pServer->fInitialized = TRUE;
    printf("CustomLeechServer initialized successfully\n");
    printf("  Listen Address: %s\n", pServer->Config.szListenAddress);
    printf("  Listen Port: %d\n", pServer->Config.dwPort);
    printf("  Security: %s\n", pServer->Config.fInsecure ? "Insecure" : "Secure");
    printf("  Driver: %s\n", pServer->Config.szDriverName);
    
    return TRUE;
}

BOOL CustomLeechServer_Start(PCUSTOM_LEECH_SERVER pServer)
{
    if (!pServer || !pServer->fInitialized) {
        printf("Error: Server not initialized\n");
        return FALSE;
    }
    
    if (pServer->fRunning) {
        printf("Warning: Server is already running\n");
        return TRUE;
    }
    
    // Create server thread
    pServer->hServerThread = CreateThread(
        NULL,
        0,
        CustomLeechServer_GrpcServerThread,
        pServer,
        0,
        &pServer->dwServerThreadId
    );
    
    if (!pServer->hServerThread) {
        printf("Error: Failed to create server thread\n");
        return FALSE;
    }
    
    pServer->fRunning = TRUE;
    printf("CustomLeechServer started successfully\n");
    return TRUE;
}

VOID CustomLeechServer_Stop(PCUSTOM_LEECH_SERVER pServer)
{
    if (!pServer) return;
    
    if (pServer->fRunning) {
        pServer->fRunning = FALSE;
        
        if (pServer->hServerThread) {
            // Wait for server thread to finish
            WaitForSingleObject(pServer->hServerThread, 5000);
            CloseHandle(pServer->hServerThread);
            pServer->hServerThread = NULL;
        }
        
        printf("CustomLeechServer stopped\n");
    }
}

VOID CustomLeechServer_Cleanup(PCUSTOM_LEECH_SERVER pServer)
{
    if (!pServer) return;
    
    // Stop server if running
    CustomLeechServer_Stop(pServer);
    
    // Cleanup gRPC server
    CustomLeechServer_CleanupGrpcServer(pServer);
    
    // Cleanup RPC system
    LeechRpcOnUnloadClose();
    
    // Cleanup driver interface
    DriverInterface_Close(&pServer->DriverInterface);
    
    pServer->fInitialized = FALSE;
    printf("CustomLeechServer cleanup completed\n");
}//
-----------------------------------------------------------------------------
// GRPC SERVER FUNCTIONS
//-----------------------------------------------------------------------------

BOOL CustomLeechServer_InitializeGrpcServer(PCUSTOM_LEECH_SERVER pServer)
{
    RPC_STATUS status;
    CHAR szPort[16];

    if (!pServer) return FALSE;

    printf("Initializing RPC server...\n");
    printf("  Address: %s\n", pServer->Config.szListenAddress);
    printf("  Port: %d\n", pServer->Config.dwPort);
    printf("  Mode: %s\n", pServer->Config.fInsecure ? "Insecure" : "Secure");

    // Convert port to string
    sprintf_s(szPort, sizeof(szPort), "%d", pServer->Config.dwPort);

    // Register the RPC interface
    status = RpcServerRegisterIf2(
        LeechRpc_v1_0_s_ifspec,
        NULL,
        NULL,
        RPC_IF_ALLOW_CALLBACKS_WITH_NO_AUTH,
        RPC_C_LISTEN_MAX_CALLS_DEFAULT,
        (unsigned)-1,
        NULL
    );
    if (status != RPC_S_OK) {
        printf("Error: Failed to register RPC interface (0x%08x)\n", status);
        return FALSE;
    }

    // Start listening on TCP
    status = RpcServerUseProtseqEpA(
        (RPC_CSTR)"ncacn_ip_tcp",
        RPC_C_PROTSEQ_MAX_REQS_DEFAULT,
        (RPC_CSTR)szPort,
        NULL
    );
    if (status != RPC_S_OK) {
        printf("Error: Failed to setup TCP protocol sequence (0x%08x)\n", status);
        RpcServerUnregisterIf(LeechRpc_v1_0_s_ifspec, NULL, FALSE);
        return FALSE;
    }

    // Start listening on named pipe (optional, for local connections)
    status = RpcServerUseProtseqEpA(
        (RPC_CSTR)"ncacn_np",
        RPC_C_PROTSEQ_MAX_REQS_DEFAULT,
        (RPC_CSTR)"\\pipe\\CustomLeechServer",
        NULL
    );
    if (status != RPC_S_OK) {
        printf("Warning: Failed to setup named pipe protocol sequence (0x%08x)\n", status);
        // Continue without named pipe support
    }

    printf("RPC server initialized successfully\n");
    return TRUE;
}

VOID CustomLeechServer_CleanupGrpcServer(PCUSTOM_LEECH_SERVER pServer)
{
    if (!pServer) return;
    
    if (pServer->GrpcServer.hDll) {
        FreeLibrary(pServer->GrpcServer.hDll);
        pServer->GrpcServer.hDll = NULL;
    }
    
    if (pServer->GrpcServer.hGRPC) {
        // In a full implementation, this would properly close the gRPC server
        pServer->GrpcServer.hGRPC = NULL;
    }
    
    printf("gRPC server cleanup completed\n");
}

DWORD WINAPI CustomLeechServer_GrpcServerThread(LPVOID lpParam)
{
    PCUSTOM_LEECH_SERVER pServer = (PCUSTOM_LEECH_SERVER)lpParam;
    RPC_STATUS status;

    if (!pServer) {
        printf("Error: Invalid server parameter in thread\n");
        return 1;
    }

    printf("RPC server thread started\n");
    printf("Listening on %s:%d (%s)\n",
           pServer->Config.szListenAddress,
           pServer->Config.dwPort,
           pServer->Config.fInsecure ? "insecure" : "secure");

    // Start accepting RPC calls
    status = RpcServerListen(1, RPC_C_LISTEN_MAX_CALLS_DEFAULT, FALSE);
    if (status != RPC_S_OK) {
        printf("Error: RpcServerListen failed (0x%08x)\n", status);
        return 1;
    }

    printf("RPC server thread stopping\n");
    return 0;
}

//-----------------------------------------------------------------------------
// PUBLIC API FUNCTIONS
//-----------------------------------------------------------------------------

// Get the global server instance
PCUSTOM_LEECH_SERVER CustomLeechServer_GetInstance(void)
{
    return &g_Server;
}