#include "../include/Common.h"
#include <cstdarg>
#include <cstdio>
#include <windows.h>

void DebugLogA(const char* fmt, ...)
{
    if (!fmt) return;
    char buffer[2048];
    va_list ap;
    va_start(ap, fmt);
    int n = _vsnprintf_s(buffer, sizeof(buffer), _TRUNCATE, fmt, ap);
    va_end(ap);
    if (n < 0) {
        // Ensure null-terminated
        buffer[sizeof(buffer) - 1] = '\0';
    }
    // Write to console without disturbing stdout/stderr bindings
    HANDLE hOut = GetStdHandle(STD_OUTPUT_HANDLE);
    if (hOut != NULL && hOut != INVALID_HANDLE_VALUE) {
        DWORD cb = (DWORD)strlen(buffer);
        DWORD written = 0;
        WriteFile(hOut, buffer, cb, &written, NULL);
    }
}


