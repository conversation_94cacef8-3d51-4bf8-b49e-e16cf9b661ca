{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "c:/Users/<USER>/Desktop/请勿下载到游戏机/副机/newleechcore/ReadPhys-master/ReadPhys", "program": "c:/Users/<USER>/Desktop/请勿下载到游戏机/副机/newleechcore/ReadPhys-master/ReadPhys/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}