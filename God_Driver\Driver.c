#include "Driver.h"

static PDEVICE_OBJECT g_DeviceObject = NULL;
static HANDLE g_PipeHandle = (HANDLE)(ULONG_PTR)0;

static void CompleteIrp(_In_ PIRP Irp, _In_ NTSTATUS Status, _In_ ULONG_PTR Info)
{
    Irp->IoStatus.Status = Status;
    Irp->IoStatus.Information = Info;
    IoCompleteRequest(Irp, IO_NO_INCREMENT);
}

// Minimal Zw* decls (real build: include ntifs.h/ntddk.h)
extern NTSTATUS ZwCreateFile(
    _Out_ PHANDLE FileHandle,
    _In_  ULONG DesiredAccess,
    _In_  PVOID ObjectAttributes, // use opaque for lint
    _Out_ PIO_STATUS_BLOCK IoStatusBlock,
    _In_opt_ PVOID AllocationSize,
    _In_ ULONG FileAttributes,
    _In_ ULONG ShareAccess,
    _In_ ULONG CreateDisposition,
    _In_ ULONG CreateOptions,
    _In_opt_ PVOID EaBuffer,
    _In_ ULONG EaLength
);
extern NTSTATUS ZwReadFile(
    _In_ HANDLE FileHandle,
    _In_opt_ HANDLE Event,
    _In_opt_ PVOID ApcRoutine,
    _In_opt_ PVOID ApcContext,
    _Out_ PIO_STATUS_BLOCK IoStatusBlock,
    _Out_writes_bytes_(Length) PVOID Buffer,
    _In_ ULONG Length,
    _In_opt_ PVOID ByteOffset,
    _In_opt_ PVOID Key
);
extern NTSTATUS ZwWriteFile(
    _In_ HANDLE FileHandle,
    _In_opt_ HANDLE Event,
    _In_opt_ PVOID ApcRoutine,
    _In_opt_ PVOID ApcContext,
    _Out_ PIO_STATUS_BLOCK IoStatusBlock,
    _In_reads_bytes_(Length) PVOID Buffer,
    _In_ ULONG Length,
    _In_opt_ PVOID ByteOffset,
    _In_opt_ PVOID Key
);
extern NTSTATUS ZwClose(_In_ HANDLE Handle);

// Build a UNICODE_STRING quickly (stack)
static UNICODE_STRING MakeUS(const wchar_t* s){ UNICODE_STRING u; RtlInitUnicodeString(&u, s); return u; }

// Open or create a named pipe for R3 communications (server side): \\Device\\NamedPipe\\GodLeechCore
static NTSTATUS GodOpenPipeServer(HANDLE* outHandle)
{
    if (!outHandle) return STATUS_INVALID_PARAMETER;
    IO_STATUS_BLOCK ios = {0};
    // ObjectAttributes opaque here to keep lint simple; in real code use OBJECT_ATTRIBUTES + InitializeObjectAttributes
    PVOID oa = (PVOID)MakeUS(GODDRV_PIPE_NAME).Buffer; // placeholder for lint
    NTSTATUS st = ZwCreateFile(outHandle,
        0x00120089 /*GENERIC_READ|GENERIC_WRITE + SYNCHRONIZE (placeholder)*/,
        oa,
        &ios,
        NULL,
        0,
        0x3, /*FILE_SHARE_READ|FILE_SHARE_WRITE*/
        0x1, /*FILE_CREATE*/
        0x40,/*FILE_SYNCHRONOUS_IO_NONALERT*/
        NULL,
        0);
    return st;
}

// Blocking read a single command header from pipe
static NTSTATUS GodPipeReadHeader(HANDLE h, GODDMA_HEADER* hdr)
{
    if (!h || !hdr) return STATUS_INVALID_PARAMETER;
    IO_STATUS_BLOCK ios = {0};
    NTSTATUS st = ZwReadFile(h, NULL, NULL, NULL, &ios, hdr, sizeof(*hdr), NULL, NULL);
    if (!NT_SUCCESS(st)) return st;
    if (ios.Information != sizeof(*hdr)) return STATUS_BUFFER_TOO_SMALL;
    if (hdr->Magic != GODDMA_MAGIC) return STATUS_INVALID_DEVICE_REQUEST;
    return STATUS_SUCCESS;
}

static NTSTATUS GodPipeWrite(HANDLE h, const void* buf, ULONG len)
{
    IO_STATUS_BLOCK ios = {0};
    return ZwWriteFile(h, NULL, NULL, NULL, &ios, (PVOID)buf, len, NULL, NULL);
}

// Main service loop: emulate the analyzed obfuscated driver: use file-based I/O, not IOCTL.
// In a production driver this would run in a system thread created at DriverEntry.
static NTSTATUS GodServiceLoop(void)
{
    NTSTATUS st = GodOpenPipeServer(&g_PipeHandle);
    if (!NT_SUCCESS(st)) return st;
    for (;;) {
        GODDMA_HEADER hdr = {0};
        st = GodPipeReadHeader(g_PipeHandle, &hdr);
        if (!NT_SUCCESS(st)) break;
        if (hdr.Command == GODDMA_CMD_GETDTB) {
            QWORD64 dtb = 0ULL; // design consistent with our analysis (R3 resolves VA->PA)
            (void)GodPipeWrite(g_PipeHandle, &dtb, sizeof(dtb));
            continue;
        }
        if (hdr.Length == 0 || hdr.Length > GODDRV_MAX_TRANSFER) { st = STATUS_INVALID_PARAMETER; break; }
        PHYSICAL_ADDRESS pa; pa.QuadPart = (LONGLONG)hdr.Address;
        if (hdr.Command == GODDMA_CMD_READ) {
            // Allocate non-paged buffer on stack per chunk
            UCHAR stackBuf[PAGE_SIZE];
            ULONG remaining = hdr.Length;
            QWORD64 cur = hdr.Address;
            while (remaining) {
                ULONG chunk = remaining > PAGE_SIZE ? PAGE_SIZE : remaining;
                pa.QuadPart = (LONGLONG)cur;
                st = GodReadPhysical(pa, stackBuf, chunk);
                if (!NT_SUCCESS(st)) break;
                st = GodPipeWrite(g_PipeHandle, stackBuf, chunk);
                if (!NT_SUCCESS(st)) break;
                remaining -= chunk; cur += chunk;
            }
            if (!NT_SUCCESS(st)) break;
        } else if (hdr.Command == GODDMA_CMD_WRITE) {
            // Read payload in chunks into a small stack buffer and write to PA
            UCHAR stackBuf[PAGE_SIZE];
            ULONG remaining = hdr.Length;
            QWORD64 cur = hdr.Address;
            while (remaining) {
                ULONG chunk = remaining > PAGE_SIZE ? PAGE_SIZE : remaining;
                IO_STATUS_BLOCK ios = {0};
                st = ZwReadFile(g_PipeHandle, NULL, NULL, NULL, &ios, stackBuf, chunk, NULL, NULL);
                if (!NT_SUCCESS(st) || ios.Information != chunk) { st = STATUS_INVALID_PARAMETER; break; }
                pa.QuadPart = (LONGLONG)cur;
                st = GodWritePhysical(pa, stackBuf, chunk);
                if (!NT_SUCCESS(st)) break;
                remaining -= chunk; cur += chunk;
            }
            if (!NT_SUCCESS(st)) break;
        } else {
            st = STATUS_INVALID_DEVICE_REQUEST; break;
        }
    }
    if (g_PipeHandle) { ZwClose(g_PipeHandle); g_PipeHandle = (HANDLE)(ULONG_PTR)0; }
    return st;
}

NTSTATUS GodReadPhysical(_In_ PHYSICAL_ADDRESS phys, _Out_writes_bytes_(length) PVOID outBuffer, _In_ SIZE_T length)
{
    if (length == 0 || outBuffer == NULL) return STATUS_INVALID_PARAMETER;
    SIZE_T remaining = length;
    PUCHAR out = (PUCHAR)outBuffer;
    while (remaining > 0) {
        SIZE_T chunk = remaining;
        if (chunk > PAGE_SIZE) chunk = PAGE_SIZE; // map 1 page per iteration
        PHYSICAL_ADDRESS pagePhys;
        pagePhys.QuadPart = (phys.QuadPart & ~(LONGLONG)(PAGE_SIZE - 1));
        SIZE_T pageOffset = (SIZE_T)(phys.QuadPart & (PAGE_SIZE - 1));
        SIZE_T bytesThisMap = PAGE_SIZE - pageOffset;
        if (bytesThisMap > chunk) bytesThisMap = chunk;

        PVOID va = MmMapIoSpace(pagePhys, PAGE_SIZE, MmCached);
        if (!va) return STATUS_INSUFFICIENT_RESOURCES;
        __try {
            RtlCopyMemory(out, (PUCHAR)va + pageOffset, bytesThisMap);
        } __except (EXCEPTION_EXECUTE_HANDLER) {
            MmUnmapIoSpace(va, PAGE_SIZE);
            return GetExceptionCode();
        }
        MmUnmapIoSpace(va, PAGE_SIZE);

        remaining -= bytesThisMap;
        out += bytesThisMap;
        phys.QuadPart += bytesThisMap;
    }
    return STATUS_SUCCESS;
}

NTSTATUS GodWritePhysical(_In_ PHYSICAL_ADDRESS phys, _In_reads_bytes_(length) PVOID inBuffer, _In_ SIZE_T length)
{
    if (length == 0 || inBuffer == NULL) return STATUS_INVALID_PARAMETER;
    SIZE_T remaining = length;
    PUCHAR in = (PUCHAR)inBuffer;
    while (remaining > 0) {
        SIZE_T chunk = remaining;
        if (chunk > PAGE_SIZE) chunk = PAGE_SIZE;
        PHYSICAL_ADDRESS pagePhys;
        pagePhys.QuadPart = (phys.QuadPart & ~(LONGLONG)(PAGE_SIZE - 1));
        SIZE_T pageOffset = (SIZE_T)(phys.QuadPart & (PAGE_SIZE - 1));
        SIZE_T bytesThisMap = PAGE_SIZE - pageOffset;
        if (bytesThisMap > chunk) bytesThisMap = chunk;

        // Map with write capability. On modern Windows, MmMapIoSpace already maps with RW for memory resources.
        PVOID va = MmMapIoSpace(pagePhys, PAGE_SIZE, MmCached);
        if (!va) return STATUS_INSUFFICIENT_RESOURCES;
        __try {
            RtlCopyMemory((PUCHAR)va + pageOffset, in, bytesThisMap);
        } __except (EXCEPTION_EXECUTE_HANDLER) {
            MmUnmapIoSpace(va, PAGE_SIZE);
            return GetExceptionCode();
        }
        MmUnmapIoSpace(va, PAGE_SIZE);

        remaining -= bytesThisMap;
        in += bytesThisMap;
        phys.QuadPart += bytesThisMap;
    }
    return STATUS_SUCCESS;
}

_Use_decl_annotations_
NTSTATUS GodDrvCreateClose(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
    UNREFERENCED_PARAMETER(DeviceObject);
    CompleteIrp(Irp, STATUS_SUCCESS, 0);
    return STATUS_SUCCESS;
}

_Use_decl_annotations_
NTSTATUS GodDrvDeviceControl(PDEVICE_OBJECT DeviceObject, PIRP Irp)
{
    UNREFERENCED_PARAMETER(DeviceObject);
    PIO_STACK_LOCATION irpSp = IoGetCurrentIrpStackLocation(Irp);
    ULONG code = irpSp->Parameters.DeviceIoControl.IoControlCode;
    NTSTATUS status = STATUS_INVALID_DEVICE_REQUEST;
    ULONG_PTR info = 0;

    __try {
        switch (code) {
        case IOCTL_DMA_GET_DTB:
        case IOCTL_READ_PHYSICAL_MEMORY:
        case IOCTL_WRITE_PHYSICAL_MEMORY:
            // Obfuscated driver behavior: do not service IOCTL. Complete immediately.
            status = STATUS_INVALID_DEVICE_REQUEST;
            break;
        default:
            status = STATUS_INVALID_DEVICE_REQUEST;
            break;
        }
    } __except (EXCEPTION_EXECUTE_HANDLER) {
        status = GetExceptionCode();
    }

    CompleteIrp(Irp, status, info);
    return status;
}

_Use_decl_annotations_
void GodDrvUnload(PDRIVER_OBJECT DriverObject)
{
    UNICODE_STRING sym;
    RtlInitUnicodeString(&sym, GODDRV_SYMLINK_NAME);
    IoDeleteSymbolicLink(&sym);
    if (g_DeviceObject) {
        IoDeleteDevice(g_DeviceObject);
        g_DeviceObject = NULL;
    }
    UNREFERENCED_PARAMETER(DriverObject);
}

_Use_decl_annotations_
NTSTATUS DriverEntry(PDRIVER_OBJECT DriverObject, PUNICODE_STRING RegistryPath)
{
    UNREFERENCED_PARAMETER(RegistryPath);
    UNICODE_STRING dev, sym;
    RtlInitUnicodeString(&dev, GODDRV_DEVICE_NAME);
    RtlInitUnicodeString(&sym, GODDRV_SYMLINK_NAME);

    NTSTATUS status = IoCreateDevice(
        DriverObject,
        0,
        &dev,
        FILE_DEVICE_UNKNOWN,
        0,
        FALSE,
        &g_DeviceObject);
    if (!NT_SUCCESS(status)) {
        return status;
    }
    status = IoCreateSymbolicLink(&sym, &dev);
    if (!NT_SUCCESS(status)) {
        IoDeleteDevice(g_DeviceObject);
        g_DeviceObject = NULL;
        return status;
    }

    DriverObject->MajorFunction[IRP_MJ_CREATE] = GodDrvCreateClose;
    DriverObject->MajorFunction[IRP_MJ_CLOSE]  = GodDrvCreateClose;
    // Keep IOCTL entry, but we will not expose real functionality here to align with obfuscated driver design
    DriverObject->MajorFunction[IRP_MJ_DEVICE_CONTROL] = GodDrvDeviceControl;
    DriverObject->DriverUnload = GodDrvUnload;

    // In the analyzed driver, the service loop is likely started in a system thread.
    // For simplicity here we call it synchronously (blocking). In production use PsCreateSystemThread.
    (void)RegistryPath;
    (void)GodServiceLoop();
    return STATUS_SUCCESS;
}


