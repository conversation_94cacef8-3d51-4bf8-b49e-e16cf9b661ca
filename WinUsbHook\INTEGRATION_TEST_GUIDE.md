# Hook Strategy Integration Test Guide

## 概述

本文档描述了如何测试新集成的分层Hook策略，该策略可自动检测FAST/EVI/FOAH软件并应用相应的Hook方法。

## 集成的功能

### 1. 软件自动检测 (software_detector.hpp)
- **FAST软件检测**: 检查 `当前目录/cheat/cheatfast.exe`
- **EVI软件检测**: 检查 `当前目录/Java_Radar/` 目录
- **FOAH软件检测**: 检查 `当前目录/models/Training.dat`

### 2. Hook策略选择
- **LEECHCORE_EXPORT**: 用于FAST/EVI，优先Hook LcCreateEx导出表
- **VMM_RVA**: 用于FOAH，优先Hook vmmdll_init通过RVA
- **VMM_SIGNATURE**: 备用特征码Hook策略

### 3. 分层Hook执行
1. **第一层**: 根据软件类型选择主要Hook策略
2. **第二层**: 主要策略失败时的备用策略
3. **第三层**: 后台重试和特征码扫描

## 测试步骤

### 测试环境1: FAST软件模拟
```bash
# 创建FAST软件特征文件
mkdir cheat
echo "test" > cheat/cheatfast.exe

# 将winusb.dll替换到FAST软件目录
# 启动FAST软件，观察日志输出
```

预期日志输出：
```
[SoftDetector] Detected: FAST
[Hook Strategy] Selected strategy: LEECHCORE_EXPORT
[Hook Strategy] Attempting LeechCore export hook...
[LcCreateEx] Found leechcore.dll module: 0x...
```

### 测试环境2: EVI软件模拟
```bash
# 创建EVI软件特征目录
mkdir Java_Radar

# 将winusb.dll替换到EVI软件目录
# 启动EVI软件，观察日志输出
```

预期日志输出：
```
[SoftDetector] Detected: EVI
[Hook Strategy] Selected strategy: LEECHCORE_EXPORT
[Hook Strategy] Attempting LeechCore export hook...
```

### 测试环境3: FOAH软件模拟
```bash
# 创建FOAH软件特征文件
mkdir models
echo "training_data" > models/Training.dat

# 将winusb.dll替换到FOAH软件目录
# 启动FOAH软件，观察日志输出
```

预期日志输出：
```
[SoftDetector] Detected: FOAH
[Hook Strategy] Selected strategy: VMM_RVA
[Hook Strategy] Attempting VMM RVA hook for FOAH...
```

### 测试环境4: 未知软件
```bash
# 不创建任何特征文件
# 将winusb.dll替换到未知软件目录
# 启动软件，观察日志输出
```

预期日志输出：
```
[SoftDetector] Unknown software detected
[Hook Strategy] Selected strategy: LEECHCORE_EXPORT (fallback for unknown)
[Hook Strategy] Unknown software, trying comprehensive strategy...
```

## 验证要点

### 1. 软件检测验证
- [ ] FAST软件正确识别（cheat/cheatfast.exe存在）
- [ ] EVI软件正确识别（Java_Radar目录存在）
- [ ] FOAH软件正确识别（models/Training.dat存在）
- [ ] 未知软件使用默认策略

### 2. Hook策略验证
- [ ] FAST/EVI使用LEECHCORE_EXPORT策略
- [ ] FOAH使用VMM_RVA策略
- [ ] 主要策略失败时正确切换到备用策略
- [ ] 所有策略失败时启动后台重试

### 3. Hook功能验证
- [ ] LcCreateEx Hook成功拦截并重写为RPC配置
- [ ] VMM Hook成功拦截并重写为RPC配置
- [ ] 服务器自动发现功能正常
- [ ] RPC通信建立成功

### 4. 错误处理验证
- [ ] Hook失败时的优雅降级
- [ ] 内存保护操作的错误处理
- [ ] PEB遍历失败的处理
- [ ] PE解析失败的处理

## 调试输出说明

### 关键日志标签
- `[SoftDetector]`: 软件检测相关
- `[Hook Strategy]`: Hook策略选择和执行
- `[LcCreateEx]`: LcCreateEx Hook相关
- `[LcCreateEx RVA]`: RVA方式Hook相关
- `[MinHook]`: MinHook操作相关
- `[RPC]`: RPC服务器发现和连接

### 错误排查
1. **软件检测失败**: 检查文件/目录路径是否正确
2. **Hook安装失败**: 检查内存保护和MinHook初始化
3. **RPC连接失败**: 检查网络和服务器状态
4. **崩溃问题**: 检查函数指针和内存访问

## 性能测试

### Hook延迟测试
测试不同Hook策略的安装时间和执行开销。

### 内存使用测试
监控Hook安装后的内存使用变化。

### 稳定性测试
长时间运行测试，验证Hook的持久性和稳定性。

## 已知限制

1. **RVA地址硬编码**: FOAH软件的RVA地址（0xDAF9F0）可能需要根据不同版本调整
2. **特征检测局限**: 基于文件/目录存在的检测可能不够精确
3. **Hook兼容性**: 某些加壳或混淆的程序可能需要额外适配

## 故障排除

### 常见问题
1. **DLL加载失败**: 检查依赖库和权限
2. **Hook不生效**: 检查目标函数地址和Hook时机
3. **RPC服务器未找到**: 检查网络配置和服务器状态

### 日志分析
启用DBG_LOG编译选项获取详细调试信息，关注错误和警告消息。
