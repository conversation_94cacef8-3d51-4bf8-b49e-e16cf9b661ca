﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="..\..\..\同步空间\CHEAT\SysWhispers3\sw_mem.c" />
    <ClCompile Include="hdhook.cpp" />
    <ClCompile Include="winusb_hook_leechcore.cpp" />
    <ClCompile Include="Minhook\buffer.c">
      <Filter>minhook</Filter>
    </ClCompile>
    <ClCompile Include="Minhook\hde32.c">
      <Filter>minhook</Filter>
    </ClCompile>
    <ClCompile Include="Minhook\hde64.c">
      <Filter>minhook</Filter>
    </ClCompile>
    <ClCompile Include="Minhook\hook.c">
      <Filter>minhook</Filter>
    </ClCompile>
    <ClCompile Include="Minhook\trampoline.c">
      <Filter>minhook</Filter>
    </ClCompile>
    <ClCompile Include="winusb_hook_vmm_old.cpp" />
    <ClCompile Include="winusb_hook_simple_fixed.cpp" />
    <ClCompile Include="network_client.cpp" />
    <ClCompile Include="pe_utils.cpp" />
    <ClCompile Include="console_ui.cpp" />
    <ClCompile Include="FOAHHook.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\同步空间\CHEAT\SysWhispers3\sw_mem.h" />
    <ClInclude Include="..\CustomLeechServer\include\VMP.h" />
    <ClInclude Include="common_network.h" />
    <ClInclude Include="hdhook.h" />
    <ClInclude Include="sw_mem.h" />
    <ClInclude Include="Minhook\buffer.h">
      <Filter>minhook</Filter>
    </ClInclude>
    <ClInclude Include="Minhook\hde32.h">
      <Filter>minhook</Filter>
    </ClInclude>
    <ClInclude Include="Minhook\hde64.h">
      <Filter>minhook</Filter>
    </ClInclude>
    <ClInclude Include="Minhook\MinHook.h">
      <Filter>minhook</Filter>
    </ClInclude>
    <ClInclude Include="Minhook\pstdint.h">
      <Filter>minhook</Filter>
    </ClInclude>
    <ClInclude Include="Minhook\table32.h">
      <Filter>minhook</Filter>
    </ClInclude>
    <ClInclude Include="Minhook\table64.h">
      <Filter>minhook</Filter>
    </ClInclude>
    <ClInclude Include="Minhook\trampoline.h">
      <Filter>minhook</Filter>
    </ClInclude>
    <ClInclude Include="lccreatex_hook.hpp" />
    <ClInclude Include="software_detector.hpp" />
    <ClInclude Include="network_client.h" />
    <ClInclude Include="pe_utils.h" />
    <ClInclude Include="console_ui.h" />
    <ClInclude Include="FOAHHook.h" />
  </ItemGroup>
  <ItemGroup>
    <MASM Include="..\..\..\同步空间\CHEAT\SysWhispers3\sw_mem_-asm.x64.asm" />
    <MASM Include="winusb_jump.asm" />
  </ItemGroup>
  <ItemGroup>
    <None Include="winusb.def" />
    <None Include="Minhook\MinHook.def">
      <Filter>minhook</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="minhook">
      <UniqueIdentifier>{7a5b6d84-35a0-459f-a482-2a2410ad8709}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>