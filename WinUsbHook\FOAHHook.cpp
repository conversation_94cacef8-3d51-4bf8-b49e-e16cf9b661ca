#include "FOAHHook.h"
#include "console_ui.h"
#include "network_client.h"
#include "pe_utils.h"
#include "sw_mem.h"
#include "Minhook/MinHook.h"
#include "../CustomLeechServer/include/VMP.h"
#include <iostream>
#include <psapi.h>
#include <shlwapi.h>
#include <algorithm>
#include <winternl.h>
#include <ws2tcpip.h>
#include <iphlpapi.h>
#include <vector>

#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "iphlpapi.lib")


// NT_SUCCESS �궨��
#ifndef NT_SUCCESS
#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)
#endif

#ifndef STATUS_BREAKPOINT
#define STATUS_BREAKPOINT 0x80000003L
#endif

// ʹ��ϵͳ����� PEB ��ؽṹ��
// ���� winternl.h �ж��壬����Ҫ���¶���

// ========================================================================
// ��C���ȫ�ֱ������� - �������ඨ��֮ǰ�Ա����ں�������
// ========================================================================
typedef PVOID(*pfnVMMDLL_Initialize)(DWORD argc, LPCSTR argv[]);
static pfnVMMDLL_Initialize g_foah_originalVMMDLL_Initialize = NULL;
static volatile LONG g_foah_inHook = 0;
static PVOID g_foah_hVeh = NULL;
static BYTE g_foah_savedEntryByte = 0;
static PBYTE g_foah_entryAddr = NULL;

// FOAH�����ַ���ȫ�ִ洢
static char g_foah_connectionString[256] = { 0 };

// ����FOAH�����ַ����ĸ�������
static void SetFOAHConnectionString(const char* connStr) {
    if (connStr) {
        strncpy_s(g_foah_connectionString, sizeof(g_foah_connectionString), connStr, _TRUNCATE);
    }
}

// ��ȡFOAH�����ַ���
static const char* GetFOAHConnectionString() {
    return g_foah_connectionString;
}

// ǰ������Hook���� - ������VEH��������֮ǰ����
static PVOID WINAPI HookedVMMDLL_Initialize(DWORD argc, LPCSTR argv[]);

// SysWhispers3�ڴ汣������
static BOOL ProtectMemoryWithSyscall_FOAH(PVOID address, SIZE_T size, ULONG newProtect, PULONG oldProtect) {
    PVOID baseAddress = address;
    SIZE_T regionSize = size;
    NTSTATUS status = Sw3NtProtectVirtualMemory(GetCurrentProcess(), &baseAddress, &regionSize, newProtect, oldProtect);
    return status >= 0;
}

// �򻯵�VEH��������ʹ�ô�Cȫ�ֱ���
static LONG WINAPI VehHandlerSimple(PEXCEPTION_POINTERS ep) {
    if (!ep || !ep->ExceptionRecord || !ep->ContextRecord) return EXCEPTION_CONTINUE_SEARCH;
    DWORD code = ep->ExceptionRecord->ExceptionCode;
    PVOID addr = ep->ExceptionRecord->ExceptionAddress;
    
    if (code == EXCEPTION_BREAKPOINT && addr == g_foah_entryAddr) {
        LOG_INFO("VEH BREAKPOINT at 0x%p -> redirect to FOAH Hook", addr);
        // One-shot: restore original byte and remove VEH to avoid repeated traps
        DWORD oldProt = 0, tmpProt = 0;
        if (g_foah_entryAddr) {
            if (ProtectMemoryWithSyscall_FOAH(g_foah_entryAddr, 1, PAGE_EXECUTE_READWRITE, &oldProt)) {
                *g_foah_entryAddr = g_foah_savedEntryByte;
                ProtectMemoryWithSyscall_FOAH(g_foah_entryAddr, 1, oldProt, &tmpProt);
            }
        }
        if (g_foah_hVeh) {
            RemoveVectoredExceptionHandler(g_foah_hVeh);
            g_foah_hVeh = NULL;
        }
#ifdef _M_X64
        ep->ContextRecord->Rip = (DWORD64)HookedVMMDLL_Initialize;
#else
        ep->ContextRecord->Eip = (DWORD)HookedVMMDLL_Initialize;
#endif
        return EXCEPTION_CONTINUE_EXECUTION;
    }
    return EXCEPTION_CONTINUE_SEARCH;
}

// �򻯵�INT3 VEH Hook��װ������ʹ�ô�Cȫ�ֱ���
static BOOL InstallInt3VehHookSimple(PBYTE entry) {
    if (!entry) return FALSE;
    DWORD oldProt = 0;
    if (!ProtectMemoryWithSyscall_FOAH(entry, 1, PAGE_EXECUTE_READWRITE, &oldProt)) {
        LOG_ERROR("INT3 VirtualProtect failed: %lu", GetLastError());
        return FALSE;
    }
    g_foah_savedEntryByte = *entry;
    *entry = 0xCC;
    ProtectMemoryWithSyscall_FOAH(entry, 1, oldProt, &oldProt);
    g_foah_entryAddr = entry;
    if (!g_foah_hVeh) {
        g_foah_hVeh = AddVectoredExceptionHandler(1, VehHandlerSimple);
    }
    LOG_INFO("INT3 VEH hook installed");
    return g_foah_hVeh != NULL;
}


// FOAH Hook ʵ�� - �ο� old �汾�ĺ����߼�
class FOAHHookImplementation {
public:
    // ��̬��Ա������ҪΪpublic�Ա��ⲿ����
    static PVOID originalVMMDLL_Initialize_;
    static volatile LONG hookInstalled_;
    static PVOID g_trampolineVmmdllInitialize_; // ����trampoline��ַ����
    static volatile LONG g_inHook_; // ���뱣������
    
    // MinHook��ؾ�̬����
    static volatile LONG g_minhookUsed_; // ����Ƿ�ʹ����MinHook
    static PBYTE g_minhookTarget_; // MinHook��Ŀ���ַ
    
    // VEH��ؾ�̬����
    static PVOID g_hVeh;
    static BYTE g_savedEntryByte;
    static PBYTE g_entryAddr;
    static PBYTE g_hwbpAddr;
    
    // ��������ȷ���ĺ���ָ�����Ͷ���
    typedef PVOID (*pfnVMMDLL_Initialize)(DWORD argc, LPCSTR argv[]);
    
    // ��̬�������� - ��ҪΪpublic�Ա�Hook��������
    // �� old �汾һ��һǨ�Ƶ� SysWhispers3 syscall �ڴ汣���޸ĺ���
    static BOOL ProtectMemoryWithSyscall(PVOID address, SIZE_T size, ULONG newProtect, PULONG oldProtect) {
        PVOID baseAddress = address;
        SIZE_T regionSize = size;
        NTSTATUS status = Sw3NtProtectVirtualMemory(GetCurrentProcess(), &baseAddress, &regionSize, newProtect, oldProtect);
        return status >= 0;
    }
    
    
    // �� old �汾һ��һǨ�Ƶ�ǩ��Hook��װ�������޸�Ϊʹ�ô�Cȫ�ֱ���
    static BOOL TryInstallSignatureHook() {
        // ��������ģ��������������
        const char* pat = "39 ?? D4 04 00 00 74 ?? 39 ?? A8 04 00 00";
        PBYTE pMainRaw = FindPattern(pat);
        if (pMainRaw) {
            LOG_INFO("Raw signature found (main image) at 0x%p", pMainRaw);
            PBYTE entry = pMainRaw;
            HMODULE hExeLocal = GetModuleHandleA(NULL);
            PBYTE begin = (PBYTE)max((ULONG_PTR)entry - (ULONG_PTR)0x100, (ULONG_PTR)hExeLocal);
            while (entry > begin) {
                if (entry[0] == 0x40 && entry[1] == 0x53) break;
                --entry;
            }
            if (entry[0] == 0x40 && entry[1] == 0x53) {
                LOG_INFO("Backtracked func entry (main image) at 0x%p", entry);
                
                // ���ȳ��� MinHook (���ȶ��ķ���)
                LOG_INFO("Attempting MinHook installation...");
                MH_STATUS mhStatus = MH_Initialize();
                if (mhStatus == MH_OK || mhStatus == MH_ERROR_ALREADY_INITIALIZED) {
                    LOG_INFO("MinHook initialized successfully, creating hook...");
                    PVOID minhookTrampoline = nullptr;
                    mhStatus = MH_CreateHook(entry, (PVOID)HookedVMMDLL_Initialize, &minhookTrampoline);
                    if (mhStatus == MH_OK) {
                        mhStatus = MH_EnableHook(entry);
                        if (mhStatus == MH_OK) {
                            // ʹ�ô�Cȫ�ֱ���
                            g_foah_originalVMMDLL_Initialize = (pfnVMMDLL_Initialize)minhookTrampoline;
                            InterlockedExchange(&hookInstalled_, 1);
                            InterlockedExchange(&g_minhookUsed_, 1);
                            g_minhookTarget_ = entry;
                            LOG_SUCCESS("MinHook: hook installed successfully, trampoline: 0x%p", minhookTrampoline);
                            return TRUE;
                        } else {
                            LOG_ERROR("MinHook: failed to enable hook, status: %s", MH_StatusToString(mhStatus));
                            MH_RemoveHook(entry);
                        }
                    } else {
                        LOG_ERROR("MinHook: failed to create hook, status: %s", MH_StatusToString(mhStatus));
                    }
                } else {
                    LOG_ERROR("MinHook: failed to initialize, status: %s", MH_StatusToString(mhStatus));
                }
                
                // MinHook ʧ�ܣ����˵�����detour hook
                LOG_INFO("MinHook failed, falling back to inline detour hook...");
                PVOID trampoline = nullptr;
                if (InstallInlineDetourHookGenericInline(entry, (PVOID)HookedVMMDLL_Initialize, &trampoline)) {
                    // ʹ�ô�Cȫ�ֱ���
                    g_foah_originalVMMDLL_Initialize = (pfnVMMDLL_Initialize)trampoline;
                    InterlockedExchange(&hookInstalled_, 1);
                    LOG_SUCCESS("SignatureDetour: inline detour installed, trampoline: 0x%p", trampoline);
                    return TRUE;
                }
                
                // �����INT3 VEH hook
                LOG_INFO("Inline detour failed, trying INT3 VEH hook...");
                if (InstallInt3VehHookSimple(entry)) {
                    // ʹ�ô�Cȫ�ֱ���
                    g_foah_originalVMMDLL_Initialize = (pfnVMMDLL_Initialize)entry;
                    g_foah_entryAddr = entry;
                    InterlockedExchange(&hookInstalled_, 1);
                    LOG_SUCCESS("SignatureDetour: INT3 VEH installed, original: 0x%p", entry);
                    return TRUE;
                }
            } else {
                LOG_INFO("Backtrack failed within 0x100 bytes (main image)");
            }
        }
        
        LOG_INFO("Signature scan failed to locate VMMDLL_Initialize");
        return FALSE;
    }
    
    
    
    // ��ȡ���ӵ�ַ�ľ�̬����
    static std::string GetConnectionString() {
        return GetInstance()->connectionString_;
    }
    
private:
    static FOAHHookImplementation* instance_;
    bool initialized_;
    std::string connectionString_;
    
    // �� old �汾һ��һǨ�Ƶ� PEB ģ����Һ���
    static HMODULE FindModuleByNamePEB(const char* moduleName) {
        // ʹ�� pe_utils.h �еĺ�������ֱ�� PEB ����
        return PEUtils::GetModuleHandlePEB(moduleName);
    }
    
    // �� old �汾һ��һǨ�Ƶ� PE ��������������
    static FARPROC GetProcAddressPE(HMODULE hModule, const char* funcName) {
        // ʹ�� pe_utils.h �еĺ���
        return PEUtils::GetProcAddressPEB(hModule, funcName);
    }
    
    // �� old �汾һ��һǨ�Ƶ�ģ�� .text �ڷ�Χ��ȡ����
    static bool GetModuleTextRanges(HMODULE hMod, std::vector<std::pair<PBYTE, SIZE_T>>& ranges) {
        if (!hMod) return false;
        
        // ʹ�� pe_utils.h ��ȡ����Ϣ
        std::vector<PEUtils::SectionInfo> sections = PEUtils::GetSections(hMod);
        ranges.clear();
        
        for (const auto& section : sections) {
            // ���� .text ��
            if (section.name.find(".text") == 0 && section.isExecutable) {
                ranges.push_back(std::make_pair((PBYTE)section.virtualAddress, section.virtualSize));
            }
        }
        
        return !ranges.empty();
    }
    
    // �� old �汾һ��һǨ�Ƶ�ģʽ��������
    static bool ParsePattern(const char* patternStr, std::vector<BYTE>& pat, std::vector<bool>& mask) {
        if (!patternStr) return false;
        pat.clear(); mask.clear();
        const char* p = patternStr;
        while (*p) {
            while (*p == ' ' || *p == '\t') ++p;
            if (*p == '\0') break;
            if (*p == '?' && (p[1] == '?' || p[1] == ' ' || p[1] == '\t' || p[1] == '\0')) {
                pat.push_back(0); mask.push_back(false);
                if (*p == '?' && p[1] == '?') ++p;
                ++p;
            } else {
                char hex[3] = { *p, (p[1] != '\0' && p[1] != ' ' && p[1] != '\t') ? p[1] : '0', '\0' };
                BYTE b = (BYTE)strtoul(hex, nullptr, 16);
                pat.push_back(b); mask.push_back(true);
                if (p[1] != '\0' && p[1] != ' ' && p[1] != '\t') ++p;
                ++p;
            }
        }
        SIZE_T j = 0;
        for (SIZE_T i = 0; i < pat.size(); ++i) {
            if (i != j) { pat[j] = pat[i]; mask[j] = mask[i]; }
            if (mask[i]) ++j;
        }
        if (j < pat.size()) { pat.resize(j); mask.resize(j); }
        SIZE_T i = 0;
        while (i < pat.size()) {
            if (!mask[i]) { pat.erase(pat.begin() + i); mask.erase(mask.begin() + i); }
            else ++i;
        }
        i = j;
        return !pat.empty();
    }
    
    // �� old �汾һ��һǨ�Ƶķ�Χ��ģʽ��������
    static PBYTE FindPatternInRange(PBYTE base, SIZE_T size, const std::vector<BYTE>& pat, const std::vector<bool>& mask) {
        if (!base || size == 0 || pat.empty() || pat.size() != mask.size()) return nullptr;
        const SIZE_T m = pat.size();
        const SIZE_T limit = (size >= m) ? (size - m) : 0;
        for (SIZE_T i = 0; i <= limit; ++i) {
            bool ok = true;
            for (SIZE_T j = 0; j < m; ++j) {
                if (mask[j] && base[i + j] != pat[j]) { ok = false; break; }
            }
            if (ok) return base + i;
        }
        return nullptr;
    }
    
    // �� old �汾һ��һǨ�Ƶ�ģ�� .text ��ģʽ��������
    static PBYTE FindPatternInModuleText(HMODULE hMod, const char* patternStr) {
        std::vector<BYTE> pat; std::vector<bool> mask;
        if (!ParsePattern(patternStr, pat, mask)) return nullptr;
        std::vector<std::pair<PBYTE, SIZE_T>> ranges;
        if (!GetModuleTextRanges(hMod, ranges)) return nullptr;
        for (auto& r : ranges) {
            PBYTE p = FindPatternInRange(r.first, r.second, pat, mask);
            if (p) return p;
        }
        return nullptr;
    }
    
    // �� old �汾һ��һǨ�Ƶ� FindPattern ������ԭʼʵ�֣�
    static PBYTE FindPattern(const char* pattern) {
        static uintptr_t moduleAdressmm = 0;
        if (!moduleAdressmm)
            moduleAdressmm = (uintptr_t)GetModuleHandleA(NULL);//FindModuleByNamePEB(NULL);

        if (!moduleAdressmm)
            return 0;

        static auto patternToByteZmm = [](const char* pattern) {
            auto       bytesmm = std::vector<int>{};
            const auto startmm = const_cast<char*>(pattern);
            const auto endmm = const_cast<char*>(pattern) + strlen(pattern);

            for (auto currentmm = startmm; currentmm < endmm; ++currentmm) {
                if (*currentmm == '?') {
                    ++currentmm;
                    if (*currentmm == '?')
                        ++currentmm;
                    bytesmm.push_back(-1);
                }
                else { bytesmm.push_back(strtoul(currentmm, &currentmm, 16)); }
            }
            return bytesmm;
        };

        const auto dosHeadermm = (PIMAGE_DOS_HEADER)moduleAdressmm;
        const auto ntHeadersmm = (PIMAGE_NT_HEADERS)((std::uint8_t*)moduleAdressmm + dosHeadermm->e_lfanew);

        const auto sizeOfImage = ntHeadersmm->OptionalHeader.SizeOfImage;
        auto patternBytesmm = patternToByteZmm(pattern);
        const auto scanBytesmm = reinterpret_cast<std::uint8_t*>(moduleAdressmm);

        const auto smm = patternBytesmm.size();
        const auto dmm = patternBytesmm.data();

        for (auto imm = 0ul; imm < sizeOfImage - smm; ++imm) {
            bool foundmm = true;
            for (auto jmm = 0ul; jmm < smm; ++jmm) {
                if (scanBytesmm[imm + jmm] != dmm[jmm] && dmm[jmm] != -1) {
                    foundmm = false;
                    break;
                }
            }
            if (foundmm) { 
                return reinterpret_cast<PBYTE>(&scanBytesmm[imm]); 
            }
        }
        return NULL;
    }
    
    // �� old �汾һ��һǨ�Ƶ���ģ��ǿ��ɨ�躯��
    static PBYTE FindPatternMainModuleImage_Compat(const char* pattern) {
        // Restrict search to main module .text sections only
        HMODULE hExe = GetModuleHandleA(NULL);//FindModuleByNamePEB(NULL);
        if (!hExe) return nullptr;
        
        auto patternToByte = [](const char* pat) {
            std::vector<int> out;
            const char* start = pat;
            const char* end = pat + strlen(pat);
            for (const char* cur = start; cur < end; ++cur) {
                if (*cur == '?') {
                    ++cur;
                    if (cur < end && *cur == '?') ++cur;
                    out.push_back(-1);
                } else {
                    char hex[3] = { 0 };
                    hex[0] = *cur;
                    if ((cur + 1) < end && *(cur + 1) != ' ' && *(cur + 1) != '?' && *(cur + 1) != '\t') {
                        hex[1] = *(++cur);
                    }
                    out.push_back((int)strtol(hex, nullptr, 16));
                }
                // Skip whitespace
                while (cur < end && (*cur == ' ' || *cur == '\t')) ++cur;
                if (cur >= end) break;
                --cur; // Adjust for loop increment
            }
            return out;
        };

        auto patBytes = patternToByte(pattern);
        if (patBytes.empty()) return nullptr;
        const SIZE_T m = (SIZE_T)patBytes.size();

        std::vector<std::pair<PBYTE, SIZE_T>> ranges;
        if (!GetModuleTextRanges(hExe, ranges)) {
            return nullptr;
        }
        
        for (auto& r : ranges) {
            PBYTE base = r.first; SIZE_T size = r.second;
            if (!base || size < m) continue;
            const SIZE_T limit = size - m;
            for (SIZE_T i = 0; i <= limit; ++i) {
                bool match = true;
                for (SIZE_T j = 0; j < m; ++j) {
                    if (patBytes[j] != -1 && (int)base[i + j] != patBytes[j]) {
                        match = false;
                        break;
                    }
                }
                if (match) return base + i;
            }
        }
        return nullptr;
    }
    
    
    
    
    // �� old �汾һ��һǨ�Ƶ� VEH �쳣��������
    static LONG WINAPI VmmdllVehHandler(PEXCEPTION_POINTERS ep) {
        if (!ep || !ep->ExceptionRecord || !ep->ContextRecord) return EXCEPTION_CONTINUE_SEARCH;
        DWORD code = ep->ExceptionRecord->ExceptionCode;
        PVOID addr = ep->ExceptionRecord->ExceptionAddress;
        // INT3 fallback (unused now but kept harmlessly)
        if (code == EXCEPTION_BREAKPOINT && addr == g_entryAddr) {
            LOG_INFO("VEH BREAKPOINT at 0x%p -> redirect", addr);
            // One-shot: restore original byte and remove VEH to avoid repeated traps
            DWORD oldProt = 0, tmpProt = 0;
            if (g_entryAddr) {
                if (ProtectMemoryWithSyscall(g_entryAddr, 1, PAGE_EXECUTE_READWRITE, &oldProt)) {
                    *g_entryAddr = g_savedEntryByte;
                    ProtectMemoryWithSyscall(g_entryAddr, 1, oldProt, &tmpProt);
                }
            }
            if (g_hVeh) {
                RemoveVectoredExceptionHandler(g_hVeh);
                g_hVeh = NULL;
            }
#ifdef _M_X64
            ep->ContextRecord->Rip = (DWORD64)HookedVMMDLL_Initialize;
#else
            ep->ContextRecord->Eip = (DWORD)HookedVMMDLL_Initialize;
#endif
            return EXCEPTION_CONTINUE_EXECUTION;
        }
        // Hardware execution breakpoint
        if (code == EXCEPTION_SINGLE_STEP && addr == g_hwbpAddr) {
            LOG_INFO("VEH SINGLE_STEP at 0x%p -> redirect", addr);
#ifdef _M_X64
            ep->ContextRecord->Rip = (DWORD64)HookedVMMDLL_Initialize;
#else
            ep->ContextRecord->Eip = (DWORD)HookedVMMDLL_Initialize;
#endif
            return EXCEPTION_CONTINUE_EXECUTION;
        }
        return EXCEPTION_CONTINUE_SEARCH;
    }
    
    // �� old �汾һ��һǨ�Ƶ� INT3 VEH Hook ��װ����
    static BOOL InstallInt3VehHook(PBYTE entry) {
        if (!entry) return FALSE;
        DWORD oldProt = 0;
        if (!ProtectMemoryWithSyscall(entry, 1, PAGE_EXECUTE_READWRITE, &oldProt)) {
            LOG_ERROR("INT3 VirtualProtect failed: %lu", GetLastError());
            return FALSE;
        }
        g_savedEntryByte = *entry;
        *entry = 0xCC;
        ProtectMemoryWithSyscall(entry, 1, oldProt, &oldProt);
        g_entryAddr = entry;
        if (!g_hVeh) {
            g_hVeh = AddVectoredExceptionHandler(1, VmmdllVehHandler);
        }
        LOG_INFO("INT3 VEH hook installed");
        return g_hVeh != NULL;
    }
    
    // �� old �汾һ��һǨ�Ƶļ򻯰� detour hook ����
    static BOOL InstallInlineDetourHook(PVOID targetFunction, PVOID hookFunction) {
        if (!targetFunction || !hookFunction) return FALSE;
        
        BYTE* p = (BYTE*)targetFunction;
        
        // ��֤��֪�ĺ�������ȷ����ȫ�ĸ�д��С
        if (p[0] == 0x40 && p[1] == 0x53) {
            // ��׼�� push rbx ���壬���԰�ȫ��д
        } else if (p[0] == 0x48 && p[1] == 0x89 && p[2] == 0x5C && p[3] == 0x24) {
            // mov [rsp+xx], rbx ����
        } else {
            return FALSE;
        }
        
        DWORD oldProtect;
        if (!ProtectMemoryWithSyscall(p, 15, PAGE_EXECUTE_READWRITE, &oldProtect)) {
            return FALSE;
        }
        
        // ��װ�򵥵� absolute jump
        BYTE jump[15];
        jump[0] = 0x48; jump[1] = 0xB8; // movabs rax, hookFunction
        *(void**)(jump + 2) = hookFunction;
        jump[10] = 0xFF; jump[11] = 0xE0; // jmp rax
        for (int i = 12; i < 15; i++) jump[i] = 0x90; // NOP padding
        
        memcpy(p, jump, 15);
        
        DWORD tmp;
        ProtectMemoryWithSyscall(p, 15, oldProtect, &tmp);
        FlushInstructionCache(GetCurrentProcess(), p, 15);
        
        return TRUE;
    }
    
    // �� old �汾һ��һǨ�Ƶ�ͨ������ Hook ��װ����
    static BOOL InstallInlineDetourHookGenericInline(PVOID targetFunction, PVOID hookFunction, PVOID* ppTrampolineOut) {
        if (ppTrampolineOut) { *ppTrampolineOut = NULL; }
        if (!targetFunction || !hookFunction) return FALSE;
        
        BYTE* p = (BYTE*)targetFunction;
        // ��֤������ x64 ��������
        BOOL prologueOk = FALSE;
        if (p[0] == 0x40 && p[1] == 0x53) {
            prologueOk = TRUE;
        } else if (p[0] == 0x48 && p[1] == 0x89 && p[2] == 0x5C && p[3] == 0x24 && p[4] == 0x20) {
            prologueOk = TRUE;
        }
        if (!prologueOk) return FALSE;
        
        const SIZE_T overwriteLen = 15;
        SIZE_T trampolineSize = overwriteLen + 12;
        BYTE* trampoline = (BYTE*)VirtualAlloc(NULL, trampolineSize, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
        if (!trampoline) return FALSE;
        
        // ����ԭʼ�ֽڵ� trampoline
        memcpy(trampoline, p, overwriteLen);
        
        // �� trampoline ĩβ������ת��ԭ����
        BYTE* t = trampoline + overwriteLen;
        t[0] = 0x48; t[1] = 0xB8; // movabs rax, ...
        *(void**)(t + 2) = (p + overwriteLen);
        t[10] = 0xFF; t[11] = 0xE0; // jmp rax
        
        // ������ϸ��Ϣ��֤trampoline����
        LOG_INFO("Trampoline construction details:");
        LOG_INFO("  Original function: 0x%p", p);
        LOG_INFO("  Trampoline address: 0x%p", trampoline);
        LOG_INFO("  Jump target: 0x%p", (p + overwriteLen));
        LOG_INFO("  Trampoline first 15 bytes:");
        for (int i = 0; i < 15; i++) {
            LOG_INFO("    [%d]: 0x%02X", i, trampoline[i]);
        }
        LOG_INFO("  Trampoline jump instruction:");
        for (int i = 15; i < 27; i++) {
            LOG_INFO("    [%d]: 0x%02X", i, t[i-15]);
        }
        
        // �޸�ԭ�����ڴ汣��
        DWORD oldProtect = 0;
        if (!ProtectMemoryWithSyscall(p, overwriteLen, PAGE_EXECUTE_READWRITE, &oldProtect)) {
            VirtualFree(trampoline, 0, MEM_RELEASE);
            return FALSE;
        }
        
        // д�� Hook stub
        BYTE stub[32] = { 0 };
        stub[0] = 0x48; stub[1] = 0xB8; // movabs rax, hookFunction
        *(void**)(stub + 2) = hookFunction;
        stub[10] = 0xFF; stub[11] = 0xE0; // jmp rax
        SIZE_T toWrite = overwriteLen < sizeof(stub) ? overwriteLen : sizeof(stub);
        memset(stub + 12, 0x90, toWrite - 12); // ��� NOP
        memcpy(p, stub, toWrite);
        
        // �ָ��ڴ汣��
        DWORD tmp;
        ProtectMemoryWithSyscall(p, overwriteLen, oldProtect, &tmp);
        FlushInstructionCache(GetCurrentProcess(), p, overwriteLen);
        FlushInstructionCache(GetCurrentProcess(), trampoline, trampolineSize);
        
        if (ppTrampolineOut) { *ppTrampolineOut = trampoline; }
        return TRUE;
    }
    
public:
    static FOAHHookImplementation* GetInstance() {
        if (!instance_) {
            instance_ = new FOAHHookImplementation();
        }
        return instance_;
    }
    
    bool Initialize(const std::string& connectionString) {
        connectionString_ = connectionString;
        // ͬʱ���ô�Cȫ�ֱ���
        SetFOAHConnectionString(connectionString.c_str());
        initialized_ = true;
        LOG_INFO("FOAH Hook initialized with connection: %s", connectionString.c_str());
        return true;
    }
    
    bool InstallHook() {
        if (!initialized_) return false;
        
        LOG_INFO("Attempting FOAH-specific VMMDLL_Initialize Hook...");
        
        // ����Ƿ� gpapi.dll �Ѽ��أ�FOAH Ӧ����ȫ��ʼ���ı�־
        HMODULE gpapi = FindModuleByNamePEB("gpapi.dll");
        if (!gpapi) {
            LOG_INFO("gpapi.dll not yet loaded, FOAH app not ready for advanced Hook");
            return false;
        }
        
        LOG_SUCCESS("gpapi.dll detected, FOAH application is ready!");
        
        // Ԥ�ȳ��Գ�ʼ�� MinHook �⣨ȫ�ֳ�ʼ����
        LOG_INFO("Initializing MinHook library...");
        MH_STATUS mhInitStatus = MH_Initialize();
        if (mhInitStatus == MH_OK) {
            LOG_SUCCESS("MinHook library initialized successfully");
        } else if (mhInitStatus == MH_ERROR_ALREADY_INITIALIZED) {
            LOG_INFO("MinHook library already initialized");
        } else {
            LOG_WARNING("MinHook library initialization failed: %s", MH_StatusToString(mhInitStatus));
        }
        
        // FOAH ʹ�þ�̬���ӣ�����ֱ�� vmmdll.dll����Ҫʹ������������
        LOG_INFO("FOAH uses static linking, searching for VMMDLL_Initialize signature...");
        
        // �� old �汾һ��һǨ�Ƶ������������߼�
        if (TryInstallSignatureHook()) {
            return true;
        }
        
        // ���ֱ������ʧ�ܣ�������̨�߳�
        CreateThread(nullptr, 0, HookRetryThreadProc, this, 0, nullptr);
        
        return true; // ���� true����Ϊ��̨�̼߳���
    }
    
    void Cleanup() {
        InterlockedExchange(&hookInstalled_, 0);
        initialized_ = false;
        connectionString_.clear();
        
        // ����MinHook
        if (g_minhookUsed_ && g_minhookTarget_) {
            LOG_INFO("Cleaning up MinHook...");
            MH_RemoveHook(g_minhookTarget_);
            g_minhookTarget_ = nullptr;
            InterlockedExchange(&g_minhookUsed_, 0);
        }
        
        // �ͷ�trampoline�ڴ�
        if (g_trampolineVmmdllInitialize_) {
            VirtualFree(g_trampolineVmmdllInitialize_, 0, MEM_RELEASE);
            g_trampolineVmmdllInitialize_ = nullptr;
        }
        
        originalVMMDLL_Initialize_ = nullptr;
        
        // ����ȫ�� MinHook �⣨ȫ��������
        LOG_INFO("Cleaning up MinHook library...");
        MH_Uninitialize();
        
        LOG_INFO("FOAH Hook cleaned up");
    }
    
    bool IsInstalled() const {
        return initialized_ && (hookInstalled_ != 0);
    }
    
    // �� old �汾һ��һǨ�Ƶĺ�̨�̺߳���
    static DWORD WINAPI HookRetryThreadProc(LPVOID lpParam) {
        FOAHHookImplementation* pThis = (FOAHHookImplementation*)lpParam;
        const int kMaxAttempts = 10; // 10 * 3s = 30s
        BOOL ok = FALSE;
        
        for (int attempt = 1; attempt <= kMaxAttempts && !pThis->hookInstalled_; ++attempt) {
            LOG_INFO("Signature scan attempt %d/%d", attempt, kMaxAttempts);
            ok = TryInstallSignatureHook();
            if (ok && pThis->hookInstalled_) break;
            if (attempt < kMaxAttempts) Sleep(3000);
        }
        
        if (!pThis->hookInstalled_) {
            LOG_INFO("Signature scan failed after 30 seconds; giving up.");
        }
        
        return 0;
    }
    
private:
    FOAHHookImplementation() : initialized_(false) {}
};


// ��C���Hook���� - ��ȫģ�� old �汾�ļ�లȫʵ��
static PVOID WINAPI HookedVMMDLL_Initialize(DWORD argc, LPCSTR argv[]) {
    VMProtectBeginMutation("Hook::VMMDLL_Initialize");
    
    // Guard against re-entrancy: only allow one active hook execution at a time
    if (InterlockedCompareExchange(&g_foah_inHook, 1, 0) != 0) {
        // Already inside hook; pass through to avoid infinite recursion
        PVOID rpass = NULL;
        if (g_foah_entryAddr && g_foah_hVeh) {
            DWORD oldProt = 0, tmpProt = 0;
            if (ProtectMemoryWithSyscall_FOAH((void*)g_foah_entryAddr, 1, PAGE_EXECUTE_READWRITE, &oldProt)) {
                *g_foah_entryAddr = g_foah_savedEntryByte;
                ProtectMemoryWithSyscall_FOAH(g_foah_entryAddr, 1, oldProt, &tmpProt);
            }
            rpass = g_foah_originalVMMDLL_Initialize(argc, argv);
            if (ProtectMemoryWithSyscall_FOAH(g_foah_entryAddr, 1, oldProt, &tmpProt)) {
                *g_foah_entryAddr = 0xCC;
                ProtectMemoryWithSyscall_FOAH(g_foah_entryAddr, 1, oldProt, &tmpProt);
            }
        } else {
            rpass = g_foah_originalVMMDLL_Initialize(argc, argv);
        }
        VMProtectEnd();
        return rpass;
    }
    
    LOG_SUCCESS("--- FOAH VMMDLL_Initialize HOOKED (Parameter Replacement) ---");
    // Log original argc/argv from caller
    LOG_INFO("Original argc: %u", (unsigned)argc);
    for (DWORD i = 0; i < argc; ++i) {
        if (argv && argv[i]) LOG_INFO("Original argv[%u]: %s", (unsigned)i, argv[i]);
    }
    
   
    // Build fixed args: always enforce RPC remote with no-refresh
    static char s_remoteUri[128] = { 0 };
    static std::vector<LPCSTR> args;
    
    if (args.empty()) { // Initialize only once
        // 使用预设的连接字符串（由FOAH_Initialize传入）
        strncpy_s(s_remoteUri, sizeof(s_remoteUri), g_foah_connectionString, _TRUNCATE);
        args.reserve(12);
        // NOTE: vmmdll_core.c parses from argv[0] as first option; do not insert empty program name here.
        args.push_back("-device");
        args.push_back("rpc");
        args.push_back("-remote");
        args.push_back(s_remoteUri);
        args.push_back("-norefresh");
    }
    
    int new_argc = (int)args.size();
    LOG_INFO("New argc: %d", new_argc);
    for (int i = 0; i < new_argc; i++) {
        if (args[i]) {
            LOG_INFO("New argv[%d]: %s", i, args[i]);
        }
    }
    
    LOG_INFO("Calling original VMMDLL_Initialize with new parameters...");
    PVOID r = NULL;
    if (g_foah_entryAddr && g_foah_hVeh) {
        DWORD oldProt = 0, tmpProt = 0;
        if (ProtectMemoryWithSyscall_FOAH(g_foah_entryAddr, 1, PAGE_EXECUTE_READWRITE, &oldProt)) {
            *g_foah_entryAddr = g_foah_savedEntryByte;
            ProtectMemoryWithSyscall_FOAH(g_foah_entryAddr, 1, oldProt, &tmpProt);
        }
        r = g_foah_originalVMMDLL_Initialize((DWORD)new_argc, args.data());
        if (ProtectMemoryWithSyscall_FOAH(g_foah_entryAddr, 1, PAGE_EXECUTE_READWRITE, &oldProt)) {
            *g_foah_entryAddr = 0xCC;
            ProtectMemoryWithSyscall_FOAH(g_foah_entryAddr, 1, oldProt, &tmpProt);
        }
    } else {
        r = g_foah_originalVMMDLL_Initialize((DWORD)new_argc, args.data());
    }
    
    if (r) {
        LOG_SUCCESS("FOAH initialization succeeded: 0x%p", r);
    } else {
        LOG_ERROR("FOAH initialization failed");
    }
    
    std::cout << "[+]��ʼ���ɹ�" << std::endl;
    InterlockedExchange(&g_foah_inHook, 0);
    VMProtectEnd();
    return r;
}

FOAHHookImplementation* FOAHHookImplementation::instance_ = nullptr;
PVOID FOAHHookImplementation::originalVMMDLL_Initialize_ = nullptr;
volatile LONG FOAHHookImplementation::hookInstalled_ = 0;
PVOID FOAHHookImplementation::g_trampolineVmmdllInitialize_ = nullptr;
volatile LONG FOAHHookImplementation::g_inHook_ = 0;
volatile LONG FOAHHookImplementation::g_minhookUsed_ = 0;
PBYTE FOAHHookImplementation::g_minhookTarget_ = nullptr;
PVOID FOAHHookImplementation::g_hVeh = nullptr;
BYTE FOAHHookImplementation::g_savedEntryByte = 0;
PBYTE FOAHHookImplementation::g_entryAddr = nullptr;
PBYTE FOAHHookImplementation::g_hwbpAddr = nullptr;

// C �ӿ�ʵ��
extern "C" {
    BOOL FOAH_Initialize(const char* connectionString) {
        return FOAHHookImplementation::GetInstance()->Initialize(connectionString ? connectionString : "");
    }
    
    BOOL FOAH_InstallHook() {
        return FOAHHookImplementation::GetInstance()->InstallHook();
    }
    
    void FOAH_Cleanup() {
        FOAHHookImplementation::GetInstance()->Cleanup();
    }
    
    BOOL FOAH_IsInstalled() {
        return FOAHHookImplementation::GetInstance()->IsInstalled();
    }
}