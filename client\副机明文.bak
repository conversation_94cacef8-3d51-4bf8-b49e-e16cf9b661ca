﻿
# Load Windows Forms assembly
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# 定义错误处理函数
function Exit-WithError {
    param([string]$message)
    $Host.UI.RawUI.ForegroundColor = "Red"
    [Console]::WriteLine($message)
    [System.Windows.Forms.MessageBox]::Show($message, "错误", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
    [System.Environment]::Exit(1)
}

function Write-Chinese {
    param([string]$message)
    $Host.UI.RawUI.ForegroundColor = "Green"
    [Console]::WriteLine($message)
}

$currentDir = Get-Location | Select-Object -ExpandProperty Path
$sys32Path = Join-Path -Path $env:windir -ChildPath "System32"
if ($currentDir -eq $sys32Path) {
    Exit-WithError "请不要使用管理员模式运行, 普通运行即可!"
} 

# Create main form
$form = New-Object System.Windows.Forms.Form
$form.Text = "更新程序"
$form.Size = New-Object System.Drawing.Size(350, 190)
$form.StartPosition = "CenterScreen"
$form.FormBorderStyle = [System.Windows.Forms.FormBorderStyle]::FixedDialog
$form.MaximizeBox = $false
$form.MinimizeBox = $false

# Create key input label
$labelKey = New-Object System.Windows.Forms.Label
$labelKey.Location = New-Object System.Drawing.Point(20, 20)
$labelKey.Size = New-Object System.Drawing.Size(300, 20)
$labelKey.Text = "卡密:"
$form.Controls.Add($labelKey)

# Create key input textbox
$textBoxKey = New-Object System.Windows.Forms.TextBox
$textBoxKey.Location = New-Object System.Drawing.Point(20, 40)
$textBoxKey.Size = New-Object System.Drawing.Size(300, 20)
$form.Controls.Add($textBoxKey)

# Create install button
$buttonInstall = New-Object System.Windows.Forms.Button
$buttonInstall.Location = New-Object System.Drawing.Point(20, 80)
$buttonInstall.Size = New-Object System.Drawing.Size(300, 50)
$buttonInstall.Text = "获取最新版本(请不用在游戏机器使用)"
$buttonInstall.Add_Click({
    $key = $textBoxKey.Text.Trim()
    
    # Validate key
    if ($key.Length -le 30 -or $key.Length -gt 36) {
        [System.Windows.Forms.MessageBox]::Show("卡密错误,请重新输入(注意卡密有没有空格)", "错误", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Warning)
        return
    }
    
    # Run installation
    $form.Hide()
    Install-Software -key $key
    $form.Close()
})
$form.Controls.Add($buttonInstall)

# Create status label
$labelStatus = New-Object System.Windows.Forms.Label
$labelStatus.Location = New-Object System.Drawing.Point(20, 180)
$labelStatus.Size = New-Object System.Drawing.Size(360, 60)
$labelStatus.Text = ""
$form.Controls.Add($labelStatus)

function Install-Software {
    param(
        [string]$key
    )
    
    # Define download URLs
    $DOWNLOAD_URL_1 = "http://radar.0xdead.top/sjz/guest/leechcore.bin"
    $DOWNLOAD_URL_2 = "http://radar.0xdead.top/sjz/guest/winusb.bin"

    $TARGET_1_FILE = Join-Path -Path (Get-Location) -ChildPath "leechcore.dll"
	$TARGET_2_FILE = Join-Path -Path (Get-Location) -ChildPath "winusb.dll"

    Write-Chinese("")
    Write-Chinese("===更新时请勿关闭窗口===")
    Write-Chinese("")
    Write-Chinese("正在获取更新中...")
    Write-Chinese("")
    
    Start-Sleep -Milliseconds 100

    # Function to get remote file size
    function Get-RemoteFileSize {
        param([string]$url)
        try {
            $request = [System.Net.HttpWebRequest]::Create($url)
            $request.Method = "HEAD"
            $response = $request.GetResponse()
            $size = $response.ContentLength
            $response.Close()
            return $size
        } catch {
            return $null
        }
    }

    # Process second file
    $remoteSize2 = Get-RemoteFileSize -url $DOWNLOAD_URL_2
    if ($remoteSize2 -eq $null) {
        Exit-WithError "无法获取核心(2)的远程文件大小"
    }

    if (Test-Path $TARGET_2_FILE) {
        $localSize2 = (Get-Item $TARGET_2_FILE).Length
        if ($localSize2 -eq $remoteSize2) {
            Write-Chinese("核心(2)已是最新版本.")
            Write-Chinese("")
        } else {
            Write-Chinese("")
            Write-Chinese("正在下载核心(2)中...")   
            try {
                Invoke-WebRequest -Uri $DOWNLOAD_URL_2 -OutFile $TARGET_2_FILE -ErrorAction Stop
                if (Test-Path $TARGET_2_FILE) {
                    $newSize = (Get-Item $TARGET_2_FILE).Length
                    if ($newSize -ne $remoteSize2) {
                        Remove-Item $TARGET_2_FILE -Force
                        Exit-WithError "核心(2)下载失败: 文件大小不符"
                    }
                    Write-Chinese("核心(2)下载成功.")
                    Write-Chinese("")
                } else {
                    Exit-WithError "核心(2)下载失败: 文件未找到"
                }
            } catch {
                Exit-WithError "核心(2)下载失败: $_"
            }
        }
    } else {
        Write-Chinese("")
        Write-Chinese("正在下载核心(2)中...")   
        try {
            Invoke-WebRequest -Uri $DOWNLOAD_URL_2 -OutFile $TARGET_2_FILE -ErrorAction Stop
            if (Test-Path $TARGET_2_FILE) {
                $newSize = (Get-Item $TARGET_2_FILE).Length
                if ($newSize -ne $remoteSize2) {
                    Remove-Item $TARGET_2_FILE -Force
                    Exit-WithError "核心(2)下载失败: 文件大小不符"
                }
                Write-Chinese("核心(2)下载成功.")
                Write-Chinese("")
            } else {
                Exit-WithError "核心(2)下载失败: 文件未找到"
            }
        } catch {
            Exit-WithError "核心(2)下载失败: $_"
        }
    }

    # Process first file
    $remoteSize1 = Get-RemoteFileSize -url $DOWNLOAD_URL_1
    if ($remoteSize1 -eq $null) {
        Exit-WithError "无法获取核心(1)的远程文件大小"
    }

    if (Test-Path $TARGET_1_FILE) {
        $localSize1 = (Get-Item $TARGET_1_FILE).Length
        if ($localSize1 -eq $remoteSize1) {
            Write-Chinese("核心(1)已是最新版本.")
            Write-Chinese("")
        } else {
            Write-Chinese("")
            Write-Chinese("正在下载核心(1)中...")   
            try {
                Invoke-WebRequest -Uri $DOWNLOAD_URL_1 -OutFile $TARGET_1_FILE -ErrorAction Stop
                if (Test-Path $TARGET_1_FILE) {
                    $newSize = (Get-Item $TARGET_1_FILE).Length
                    if ($newSize -ne $remoteSize1) {
                        Remove-Item $TARGET_1_FILE -Force
                        Exit-WithError "核心(1)下载失败: 文件大小不符"
                    }
                    Write-Chinese("核心(1)下载成功.")
                    Write-Chinese("")
                } else {
                    Exit-WithError "核心(1)下载失败: 文件未找到"
                }
            } catch {
                Exit-WithError "核心(1)下载失败: $_"
            }
        }
    } else {
        Write-Chinese("")
        Write-Chinese("正在下载核心(1)中...")   
        try {
            Invoke-WebRequest -Uri $DOWNLOAD_URL_1 -OutFile $TARGET_1_FILE -ErrorAction Stop
            if (Test-Path $TARGET_1_FILE) {
                $newSize = (Get-Item $TARGET_1_FILE).Length
                if ($newSize -ne $remoteSize1) {
                    Remove-Item $TARGET_1_FILE -Force
                    Exit-WithError "核心(1)下载失败: 文件大小不符"
                }
                Write-Chinese("核心(1)下载成功.")
                Write-Chinese("")
            } else {
                Exit-WithError "核心(1)下载失败: 文件未找到"
            }
        } catch {
            Exit-WithError "核心(1)下载失败: $_"
        }
    }

    Start-Sleep -Milliseconds 100

    Write-Chinese("")
    Write-Chinese("获取完成!")
	Write-Chinese("把本目录下的所有文件复制到你要开的DMA辅助目录里即可!")
	[System.Windows.Forms.MessageBox]::Show("更新成功！`n`n把本目录下的所有文件复制到你要开的DMA辅助目录里即可!", 
                                      "完成", 
                                      [System.Windows.Forms.MessageBoxButtons]::OK, 
                                      [System.Windows.Forms.MessageBoxIcon]::Information)
}

# Show the form
[void]$form.ShowDialog()