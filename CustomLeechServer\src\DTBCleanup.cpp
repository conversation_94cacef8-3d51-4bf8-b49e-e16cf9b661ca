// DTBCleanup.cpp : DTB cleanup and override functionality
// This file implements DTB value cleaning and command-line override support

#include <Windows.h>
#include <stdio.h>
#include <string.h>
#include "../include/DriverInterface.h"
#include "../include/DTBDetector.h"
#include "../include/VMP.h"

// Global DTB override value (0 means no override)
static QWORD g_qwDTBOverride = 0;

// DTB comparison log
static struct {
    QWORD qwDriverDTB;      // Raw DTB from driver
    QWORD qwMemProcFSDTB;   // DTB expected by MemProcFS  
    QWORD qwCleanedDTB;     // DTB after cleanup
    BOOL  fOverrideUsed;    // Whether override was used
} g_DTBLog = {0};

//-----------------------------------------------------------------------------
// DTB CLEANUP FUNCTIONS
//-----------------------------------------------------------------------------

// Clean DTB value by removing KVA shadow and PCID bits
// x64 CR3 register layout:
// Bit 63: KVA shadow bit (may be set on some systems)
// Bits 51-12: Page Frame Number (PFN) - physical address of PML4
// Bits 11-0: PCID and control bits (should be masked for DTB)
extern "C" QWORD DTBCleanup_CleanDTB(QWORD qwRawDTB)
{
    VMProtectBegin("DTBCleanup::CleanDTB");
    QWORD qwCleanDTB = qwRawDTB;
    
    // Clear KVA shadow bit (bit 63)
    qwCleanDTB &= ~(1ULL << 63);
    
    // Clear PCID bits and page flags (low 12 bits)
    // Keep only the page frame number (bits 51-12)
    qwCleanDTB &= 0xFFFFFFFFFFFFF000ULL;
    
    printf("[DTB Cleanup] Raw DTB: 0x%llX -> Cleaned DTB: 0x%llX\n", qwRawDTB, qwCleanDTB);
    
    // Additional validation: DTB should be 4KB aligned
    if (qwCleanDTB & 0xFFF) {
        printf("[DTB Cleanup] WARNING: DTB not 4KB aligned after cleanup\n");
    }
    
    // Additional validation: DTB should be within reasonable physical memory range
    if (qwCleanDTB > 0x100000000000ULL) { // > 16TB
        printf("[DTB Cleanup] WARNING: DTB seems too large (>16TB physical)\n");
    }
    
    VMProtectEnd();
    return qwCleanDTB;
}

// Alternative cleanup: Clear only PCID bits (preserve KVA shadow)
extern "C" QWORD DTBCleanup_CleanDTBMinimal(QWORD qwRawDTB)
{
    VMProtectBegin("DTBCleanup::CleanDTBMinimal");
    // Only clear the low 12 bits (PCID and page flags)
    QWORD qwCleanDTB = qwRawDTB & 0xFFFFFFFFFFFFF000ULL;
    
    printf("[DTB Cleanup] Minimal cleanup - Raw DTB: 0x%llX -> Cleaned DTB: 0x%llX\n", 
           qwRawDTB, qwCleanDTB);
    
    VMProtectEnd();
    return qwCleanDTB;
}

//-----------------------------------------------------------------------------
// DTB OVERRIDE FUNCTIONS
//-----------------------------------------------------------------------------

// Parse command line for --dtb parameter
extern "C" BOOL DTBCleanup_ParseCommandLine(int argc, char* argv[])
{
    VMProtectBegin("DTBCleanup::ParseCmd");
    for (int i = 1; i < argc - 1; i++) {
        if (_stricmp(argv[i], "--dtb") == 0) {
            // Parse DTB value (supports hex with 0x prefix)
            char* endptr = NULL;
            g_qwDTBOverride = strtoull(argv[i + 1], &endptr, 0);
            
            if (endptr == argv[i + 1] || *endptr != '\0') {
                printf("[DTB Override] ERROR: Invalid DTB value: %s\n", argv[i + 1]);
                return FALSE;
            }
            
            printf("[DTB Override] Command-line DTB override set to: 0x%llX\n", g_qwDTBOverride);
            return TRUE;
        }
    }
    VMProtectEnd();
    return FALSE;
}

// Get DTB override value if set
extern "C" QWORD DTBCleanup_GetOverrideDTB()
{
    VMProtectBegin("DTBCleanup::GetOverride");
    QWORD val = g_qwDTBOverride;
    VMProtectEnd();
    return val;
}

// Check if DTB override is active
extern "C" BOOL DTBCleanup_HasOverride()
{
    VMProtectBegin("DTBCleanup::HasOverride");
    BOOL r = g_qwDTBOverride != 0;
    VMProtectEnd();
    return r;
}

//-----------------------------------------------------------------------------
// DTB COMPARISON AND LOGGING
//-----------------------------------------------------------------------------

// Log DTB values for comparison
extern "C" VOID DTBCleanup_LogDTBComparison(QWORD qwDriverDTB, QWORD qwMemProcFSDTB)
{
    VMProtectBeginVirtualization("DTBCleanup::LogComparison");
    g_DTBLog.qwDriverDTB = qwDriverDTB;
    g_DTBLog.qwMemProcFSDTB = qwMemProcFSDTB;
    g_DTBLog.qwCleanedDTB = DTBCleanup_CleanDTB(qwDriverDTB);
    
    printf("\n[DTB Comparison Report]\n");
    printf("  Driver returned DTB:     0x%llX\n", qwDriverDTB);
    printf("  MemProcFS expected DTB:  0x%llX\n", qwMemProcFSDTB);
    printf("  Cleaned DTB:            0x%llX\n", g_DTBLog.qwCleanedDTB);
    
    // Check if cleaned DTB matches MemProcFS expectation
    if (g_DTBLog.qwCleanedDTB == qwMemProcFSDTB) {
        printf("  Status: MATCH after cleanup\n");
    } else {
        printf("  Status: MISMATCH\n");
        
        // Analyze the difference
        QWORD qwDiff = g_DTBLog.qwCleanedDTB ^ qwMemProcFSDTB;
        printf("  Difference bits: 0x%llX\n", qwDiff);
        
        // Check if only low bits differ
        if ((qwDiff & 0xFFFFFFFFFFFFF000ULL) == 0) {
            printf("  -> Difference only in low 12 bits (PCID/flags)\n");
        }
        
        // Check if high bit differs
        if (qwDiff & (1ULL << 63)) {
            printf("  -> KVA shadow bit differs\n");
        }
    }
    
    if (g_qwDTBOverride) {
        printf("  Override DTB:           0x%llX (ACTIVE)\n", g_qwDTBOverride);
    }
    
    printf("\n");
    VMProtectEnd();
}

// Get the final DTB to use (with override and cleanup)
extern "C" QWORD DTBCleanup_GetFinalDTB(DriverInterface* pDriver)
{
    VMProtectBeginMutation("DTBCleanup::GetFinalDTB");
    // Check for command-line override first
    if (g_qwDTBOverride != 0) {
        printf("[DTB] Using command-line override DTB: 0x%llX\n", g_qwDTBOverride);
        g_DTBLog.fOverrideUsed = TRUE;
        QWORD tmp = g_qwDTBOverride;
        VMProtectEnd();
        return tmp;
    }
    
    // Get DTB from driver or scanner
    QWORD qwDTB = DTBDetector_GetSystemDTB(pDriver, FALSE);
    
    if (qwDTB == 0) {
        printf("[DTB] WARNING: No DTB available\n");
        VMProtectEnd();
        return 0;
    }
    
    // Apply cleanup
    QWORD qwCleanDTB = DTBCleanup_CleanDTB(qwDTB);
    
    // Store for logging
    g_DTBLog.qwDriverDTB = qwDTB;
    g_DTBLog.qwCleanedDTB = qwCleanDTB;
    g_DTBLog.fOverrideUsed = FALSE;
    
    VMProtectEnd();
    return qwCleanDTB;
}

//-----------------------------------------------------------------------------
// DIAGNOSTIC FUNCTIONS
//-----------------------------------------------------------------------------

// Print DTB diagnostic information
extern "C" VOID DTBCleanup_PrintDiagnostics()
{
    VMProtectBegin("DTBCleanup::Diagnostics");
    printf("\n[DTB Diagnostics]\n");
    printf("==========================================\n");
    
    if (g_DTBLog.qwDriverDTB) {
        printf("Driver DTB Analysis:\n");
        printf("  Raw value:        0x%016llX\n", g_DTBLog.qwDriverDTB);
        printf("  Binary:           ");
        for (int i = 63; i >= 0; i--) {
            if (i == 63 || i == 51 || i == 11) printf(" ");
            printf("%d", (g_DTBLog.qwDriverDTB >> i) & 1);
        }
        printf("\n");
        printf("  KVA shadow bit:   %s\n", (g_DTBLog.qwDriverDTB & (1ULL << 63)) ? "SET" : "CLEAR");
        printf("  Page frame:       0x%llX\n", (g_DTBLog.qwDriverDTB & 0xFFFFFFFFFFFFF000ULL) >> 12);
        printf("  PCID/flags:       0x%03llX\n", g_DTBLog.qwDriverDTB & 0xFFF);
    }
    
    if (g_DTBLog.qwMemProcFSDTB) {
        printf("\nMemProcFS Expected DTB:\n");
        printf("  Value:            0x%016llX\n", g_DTBLog.qwMemProcFSDTB);
    }
    
    if (g_DTBLog.qwCleanedDTB) {
        printf("\nCleaned DTB:\n");
        printf("  Value:            0x%016llX\n", g_DTBLog.qwCleanedDTB);
        printf("  Match status:     %s\n", 
               (g_DTBLog.qwCleanedDTB == g_DTBLog.qwMemProcFSDTB) ? "MATCH" : "MISMATCH");
    }
    
    if (g_qwDTBOverride) {
        printf("\nOverride DTB:\n");
        printf("  Value:            0x%016llX\n", g_qwDTBOverride);
        printf("  Status:           %s\n", g_DTBLog.fOverrideUsed ? "USED" : "AVAILABLE");
    }
    
    printf("==========================================\n\n");
    VMProtectEnd();
}

// Test DTB cleanup with various values
extern "C" VOID DTBCleanup_RunTests()
{
    VMProtectBegin("DTBCleanup::RunTests");
    printf("\n[DTB Cleanup Tests]\n");
    printf("==========================================\n");
    
    struct {
        QWORD qwInput;
        const char* szDescription;
    } testCases[] = {
        { 0x00000001AA000867, "Typical DTB with flags" },
        { 0x80000001AA000867, "DTB with KVA shadow bit" },
        { 0x00000001AA000000, "Clean DTB (no flags)" },
        { 0x00000001AA000FFF, "DTB with all PCID bits set" },
        { 0xFFFFFFFFFFFFFFFF, "All bits set" },
        { 0x00000000001AA000, "Low memory DTB" },
        { 0x000000DEAD000867, "High memory DTB" }
    };
    
    for (int i = 0; i < sizeof(testCases) / sizeof(testCases[0]); i++) {
        printf("\nTest %d: %s\n", i + 1, testCases[i].szDescription);
        printf("  Input:    0x%016llX\n", testCases[i].qwInput);
        
        QWORD qwClean = DTBCleanup_CleanDTB(testCases[i].qwInput);
        printf("  Output:   0x%016llX\n", qwClean);
        
        QWORD qwMinimal = DTBCleanup_CleanDTBMinimal(testCases[i].qwInput);
        printf("  Minimal:  0x%016llX\n", qwMinimal);
    }
    
    printf("==========================================\n\n");
    VMProtectEnd();
}
