// leechrpc.h : definitions related to the leech rpc service.
// Adapted for CustomLeechServer
//
// (c) Ulf Frisk, 2018-2025
// Author: Ulf Frisk, <EMAIL>
//
#ifndef __LEECHRPC_H__
#define __LEECHRPC_H__

#include "Common.h"

// Note: Using MS-RPC only, no gRPC dependencies needed
// All gRPC-related types and functions have been removed

#ifdef _WIN32
#include <windows.h>

// Define NTSTATUS if not already defined
#ifndef NTSTATUS
typedef LONG NTSTATUS;
#endif

#define CLSID_BINDING_INTERFACE_LEECHRPC "906B0DC2-1337-0666-0001-0000657A63DD"

#define LEECHRPC_COMPRESS_MAXTHREADS    8

typedef NTSTATUS WINAPI PFN_RtlCompressBuffer(
    _In_ USHORT CompressionFormatAndEngine,
    _In_  PUCHAR UncompressedBuffer,
    _In_  ULONG  UncompressedBufferSize,
    _Out_ PUCHAR CompressedBuffer,
    _In_  ULONG  CompressedBufferSize,
    _In_  ULONG  UncompressedChunkSize,
    _Out_ PULONG FinalCompressedSize,
    _In_  PVOID  WorkSpace
);

typedef NTSTATUS WINAPI PFN_RtlDecompressBuffer(
    _In_  USHORT CompressionFormat,
    _Out_ PUCHAR UncompressedBuffer,
    _In_  ULONG  UncompressedBufferSize,
    _In_  PUCHAR CompressedBuffer,
    _In_  ULONG  CompressedBufferSize,
    _Out_ PULONG FinalUncompressedSize
);

typedef struct tdLEECHRPC_COMPRESS {
    BOOL fValid;
    HANDLE hDll;
    DWORD iCompress;
    struct {
        CRITICAL_SECTION Lock;
        PVOID pbWorkspace;
    } Compress[LEECHRPC_COMPRESS_MAXTHREADS];
    struct {
        PFN_RtlCompressBuffer* pfnRtlCompressBuffer;
        PFN_RtlDecompressBuffer* pfnRtlDecompressBuffer;
    } fn;
} LEECHRPC_COMPRESS, *PLEECHRPC_COMPRESS;

#endif /* _WIN32 */

#define LEECHRPC_MSGMAGIC                   0xd05a2667
#define LEECHRPC_FLAG_NOCOMPRESS                0x0010
#define LEECHRPC_FLAG_FNEXIST_ReadScatterMEM    0x0100
#define LEECHRPC_FLAG_FNEXIST_WriteScatterMEM   0x0200
#define LEECHRPC_FLAG_FNEXIST_Close             0x0800
#define LEECHRPC_FLAG_FNEXIST_GetOption         0x1000
#define LEECHRPC_FLAG_FNEXIST_SetOption         0x2000
#define LEECHRPC_FLAG_FNEXIST_Command           0x4000

// gRPC server context for CustomLeechServer
typedef struct LEECHRPC_GRPC_SERVER {
    HMODULE hDll;
    HANDLE hGRPC;
    CHAR szListenAddress[MAX_PATH];
    DWORD dwPort;
    BOOL fInsecure;
    CHAR szTlsServerP12Path[MAX_PATH];
    CHAR szTlsServerP12Password[MAX_PATH];
    CHAR szTlsClientCaCertPath[MAX_PATH];
} LEECHRPC_GRPC_SERVER, *PLEECHRPC_GRPC_SERVER;

typedef enum {
    LEECHRPC_MSGTYPE_NA =                0,
    LEECHRPC_MSGTYPE_PING_REQ =          1,
    LEECHRPC_MSGTYPE_PING_RSP =          2,
    LEECHRPC_MSGTYPE_OPEN_REQ =          3,
    LEECHRPC_MSGTYPE_OPEN_RSP =          4,
    LEECHRPC_MSGTYPE_CLOSE_REQ =         5,
    LEECHRPC_MSGTYPE_CLOSE_RSP =         6,
    LEECHRPC_MSGTYPE_READSCATTER_REQ =   7,
    LEECHRPC_MSGTYPE_READSCATTER_RSP =   8,
    LEECHRPC_MSGTYPE_WRITESCATTER_REQ =  9,
    LEECHRPC_MSGTYPE_WRITESCATTER_RSP = 10,
    LEECHRPC_MSGTYPE_GETOPTION_REQ =    11,
    LEECHRPC_MSGTYPE_GETOPTION_RSP =    12,
    LEECHRPC_MSGTYPE_SETOPTION_REQ =    13,
    LEECHRPC_MSGTYPE_SETOPTION_RSP =    14,
    LEECHRPC_MSGTYPE_COMMAND_REQ =      15,
    LEECHRPC_MSGTYPE_COMMAND_RSP =      16,
    LEECHRPC_MSGTYPE_KEEPALIVE_REQ =    17,
    LEECHRPC_MSGTYPE_KEEPALIVE_RSP =    18,
    LEECHRPC_MSGTYPE_MAX =              18,
} LEECHRPC_MSGTYPE;

typedef struct tdLEECHRPC_MSG_HDR {
    DWORD dwMagic;
    DWORD cbMsg;
    LEECHRPC_MSGTYPE tpMsg;
    BOOL fMsgResult;
    DWORD dwRpcClientID;
    DWORD flags;
} LEECHRPC_MSG_HDR, *PLEECHRPC_MSG_HDR, **PPLEECHRPC_MSG_HDR;

typedef struct tdLEECHRPC_MSG_OPEN {
    // HDR
    DWORD dwMagic;
    DWORD cbMsg;
    LEECHRPC_MSGTYPE tpMsg;
    BOOL fMsgResult;
    DWORD dwRpcClientID;
    DWORD flags;
    // MSG
    BOOL fValidOpen;
    LC_CONFIG cfg;
    LC_CONFIG_ERRORINFO errorinfo;
} LEECHRPC_MSG_OPEN, *PLEECHRPC_MSG_OPEN;

typedef struct tdLEECHRPC_MSG_BIN {
    // HDR
    DWORD dwMagic;
    DWORD cbMsg;
    LEECHRPC_MSGTYPE tpMsg;
    BOOL fMsgResult;
    DWORD dwRpcClientID;
    DWORD flags;
    // MSG
    QWORD qwData[2];
    DWORD cbDecompress; // cb uncompressed data, 0 = no compression
    DWORD cb;
    BYTE pb[];
} LEECHRPC_MSG_BIN, *PLEECHRPC_MSG_BIN;

typedef struct tdLEECHRPC_MSG_DATA {
    // HDR
    DWORD dwMagic;
    DWORD cbMsg;
    LEECHRPC_MSGTYPE tpMsg;
    BOOL fMsgResult;
    DWORD dwRpcClientID;
    DWORD flags;
    // MSG
    QWORD qwData[2];
} LEECHRPC_MSG_DATA, *PLEECHRPC_MSG_DATA;

// Function declarations
BOOL LeechRPC_CompressInitialize(_Inout_ PLEECHRPC_COMPRESS ctxCompress);
VOID LeechRPC_CompressClose(_Inout_ PLEECHRPC_COMPRESS ctxCompress);
VOID LeechRPC_Compress(_In_ PLEECHRPC_COMPRESS ctxCompress, _Inout_ PLEECHRPC_MSG_BIN pMsg, _In_ BOOL fCompressDisable);
_Success_(return)
BOOL LeechRPC_Decompress(_In_ PLEECHRPC_COMPRESS ctxCompress, _In_ PLEECHRPC_MSG_BIN pMsgIn, _Out_ PLEECHRPC_MSG_BIN *ppMsgOut);
VOID LeechSvc_GetTimeStamp(_Out_writes_(32) LPSTR szTime);

// gRPC command handler function pointer type
typedef VOID(*PFN_GRPC_COMMAND_HANDLER)(PVOID ctx, PBYTE pbIn, SIZE_T cbIn, PBYTE* ppbOut, SIZE_T* pcbOut);

// C++ compatibility
#ifdef __cplusplus
extern "C" {
#endif

// Core RPC functions
VOID LeechRpcOnLoadInitialize();
VOID LeechRpcOnUnloadClose();
VOID LeechGRPC_ReservedSubmitCommand(_In_opt_ PVOID ctx, _In_ PBYTE pbIn, _In_ SIZE_T cbIn, _Out_ PBYTE *ppbOut, _Out_ SIZE_T *pcbOut);

// Forward declarations for C++ classes
#ifdef __cplusplus
class DriverInterface;
class SessionManager;

// C++ to C interface functions
VOID LeechRpc_SetDriverInterface(DriverInterface* pDriverInterface);
VOID LeechRpc_SetSessionManager(SessionManager* pSessionManager);

// C-compatible wrapper functions
BOOL DriverInterface_ReadScatter_C(DWORD cMEMs, PPMEM_SCATTER ppMEMs);
BOOL DriverInterface_WriteScatter_C(DWORD cMEMs, PPMEM_SCATTER ppMEMs);
BOOL SessionManager_GetSession_C(DWORD dwClientID);
BOOL SessionManager_AddSession_C(DWORD dwClientID);
VOID SessionManager_RemoveSession_C(DWORD dwClientID);
VOID SessionManager_IncrementActiveRequests_C(DWORD dwClientID);
VOID SessionManager_DecrementActiveRequests_C(DWORD dwClientID);
#else
// C-only declarations for bridge functions
BOOL DriverInterface_ReadScatter_C(DWORD cMEMs, PPMEM_SCATTER ppMEMs);
BOOL DriverInterface_WriteScatter_C(DWORD cMEMs, PPMEM_SCATTER ppMEMs);
BOOL SessionManager_GetSession_C(DWORD dwClientID);
BOOL SessionManager_AddSession_C(DWORD dwClientID);
VOID SessionManager_RemoveSession_C(DWORD dwClientID);
VOID SessionManager_IncrementActiveRequests_C(DWORD dwClientID);
VOID SessionManager_DecrementActiveRequests_C(DWORD dwClientID);
#endif

#ifdef __cplusplus
}
#endif

#endif /* __LEECHRPC_H__ */