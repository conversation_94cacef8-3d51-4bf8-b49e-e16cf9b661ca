#include "KliGlobal.h"
#include <ntifs.h>

// 验证函数指针是否有效
BOOLEAN IsValidKernelPointer(PVOID ptr)
{
    if (!ptr || ptr == (PVOID)-1 || (ULONG_PTR)ptr < 0xFFFF800000000000ULL)
        return FALSE;
    
    // 简单的内核地址范围检查
    if ((ULONG_PTR)ptr >= 0xFFFF800000000000ULL && (ULONG_PTR)ptr < 0xFFFFFFFFF0000000ULL)
        return TRUE;
    
    return FALSE;
}

// 安全的KLI函数设置宏
#define KLI_SAFE_SET(name) do { \
    PVOID funcPtr = (PVOID)(kli::crypto::RC4Func(kli::find_kernel_export(KLI_HASH_STR(#name)))); \
    if (IsValidKernelPointer(funcPtr)) { \
        KLI##name = (decltype(&name))funcPtr; \
    } else { \
        KLI##name = nullptr; \
    } \
} while(0)

// 统一的KLI缓存初始化函数
void InitializeAllKliCache()
{
    // 初始化entry.cpp使用的函数
    KLI_SAFE_SET(KeGetCurrentIrql);
    KLI_SAFE_SET(DbgPrintEx);
    KLI_SAFE_SET(ExAllocatePool2);
    KLI_SAFE_SET(ExAllocatePoolWithTag);
    KLI_SAFE_SET(ExFreePool);
    KLI_SAFE_SET(ExFreePoolWithTag);
    KLI_SAFE_SET(IoCreateDevice);
    KLI_SAFE_SET(IoCreateSymbolicLink);
    KLI_SAFE_SET(IoDeleteDevice);
    KLI_SAFE_SET(IoDeleteSymbolicLink);
    KLI_SAFE_SET(IofCompleteRequest);
    KLI_SAFE_SET(RtlInitUnicodeString);
    // KLI_SAFE_SET(RtlStringCchPrintfW); // 移除 - 此函数在ntoskrnl中未导出
    KLI_SAFE_SET(KeQueryTimeIncrement);

    // 初始化字符串处理函数
    KLI_SAFE_SET(swprintf_s);
    KLI_SAFE_SET(wcscpy_s);
    KLI_SAFE_SET(wcscat_s);

    // 初始化Anti4heatExpert使用的函数
    KLI_SAFE_SET(MmGetPhysicalMemoryRanges);
    KLI_SAFE_SET(MmGetPhysicalAddress);
    KLI_SAFE_SET(MmGetVirtualForPhysical);
    KLI_SAFE_SET(MmAllocateMappingAddress);
    KLI_SAFE_SET(MmFreeMappingAddress);
}