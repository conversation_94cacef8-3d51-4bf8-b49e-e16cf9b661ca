// connection_manager.h - 全局连接管理器
#pragma once
#include <windows.h>

class ConnectionManager {
private:
    static ConnectionManager* instance;
    static CRITICAL_SECTION cs;
    static bool csInitialized;
    
    char serverIP[16];
    char remoteUri[128];
    bool initialized;
    bool userInteractionCompleted;
    
    ConnectionManager();
    
public:
    static ConnectionManager* GetInstance();
    static void Cleanup();
    
    // 获取服务器连接信息：仅在第一次调用时交互提示用户
    bool GetServerConnection(char* outIP, size_t ipSize, char* outUri, size_t uriSize);
    
    // 判断是否已经初始化
    bool IsInitialized() const { return initialized; }
    
    // 重置状态（清除缓存并允许再次交互）
    void Reset();
};