// VMP.h - Lightweight wrapper for VMProtect SDK macros.
// When ENABLE_VMPROTECT is defined in preprocessor macros this header REQUIRES
// the real VMProtectSDK.h to be available and included – ensuring markers are
// emitted so VMProtect can recognize regions. Otherwise, it falls back to
// harmless no-op macros for developer/debug builds.

#pragma once

#ifdef ENABLE_VMPROTECT
  // REQUIRE the official SDK header so markers are emitted into the binary
  // for VMProtect to detect. If the header is missing, compilation should fail
  // to avoid producing an unmarked build.
  #ifndef __cplusplus
    // Some C compilers (MSVC/clang-cl in MSVC mode) don't provide 'bool' by default
    // Ensure 'bool/true/false' exist before including the C VMProtect header.
    #if defined(__has_include)
      #if __has_include(<stdbool.h>)
        #include <stdbool.h>
      #endif
    #endif
    #ifndef __bool_true_false_are_defined
      typedef unsigned char bool;
      #ifndef true
        #define true 1
      #endif
      #ifndef false
        #define false 0
      #endif
    #endif
  #endif
  #include "VMProtectSDK.h"
#else
  // Developer/no-SDK fallback: define no-op macros.
  #ifndef VMProtectBegin
  #  define VMProtectBegin(name)
  #endif
  #ifndef VMProtectBeginVirtualization
  #  define VMProtectBeginVirtualization(name)
  #endif
  #ifndef VMProtectBeginMutation
  #  define VMProtectBeginMutation(name)
  #endif
  #ifndef VMProtectBeginUltra
  #  define VMProtectBeginUltra(name)
  #endif
  #ifndef VMProtectEnd
  #  define VMProtectEnd()
  #endif
#endif


