// leechrpc_c_interface.c : C interface functions for RPC server to access C++ objects
//
// This file provides C-compatible interface functions that bridge between
// the C-based RPC server code and the C++ driver/session manager objects.
//

#include "../include/Common.h"
#include "../include/leechrpc.h"
#include <stdio.h>

// Forward declarations for C++ wrapper functions
// These are implemented in C++ files and expose C-compatible interfaces
#ifdef __cplusplus
extern "C" {
#endif

extern BOOL DriverInterface_ReadScatter_Impl(DWORD cMEMs, PPMEM_SCATTER ppMEMs);
extern BOOL DriverInterface_WriteScatter_Impl(DWORD cMEMs, PPMEM_SCATTER ppMEMs);
extern QWORD DriverInterface_GetDTB_Impl();
extern QWORD DriverInterface_GetKernelBase_Impl();

extern BOOL SessionManager_GetSession_Impl(DWORD dwSessionId);
extern BOOL SessionManager_AddSession_Impl(DWORD dwSessionId);
extern void SessionManager_RemoveSession_Impl(DWORD dwSessionId);
extern void SessionManager_IncrementActiveRequests_Impl(DWORD dwSessionId);
extern void SessionManager_DecrementActiveRequests_Impl(DWORD dwSessionId);

#ifdef __cplusplus
}
#endif

// These functions are no longer needed here as they're defined in leechrpc.h
// The implementation will be provided by C++ wrapper functions

// DriverInterface C wrappers - forward to C++ implementation
BOOL DriverInterface_ReadScatter_C(DWORD cMEMs, PPMEM_SCATTER ppMEMs)
{
    return DriverInterface_ReadScatter_Impl(cMEMs, ppMEMs);
}

BOOL DriverInterface_WriteScatter_C(DWORD cMEMs, PPMEM_SCATTER ppMEMs)
{
    return DriverInterface_WriteScatter_Impl(cMEMs, ppMEMs);
}

// SessionManager C wrappers - forward to C++ implementation
BOOL SessionManager_GetSession_C(DWORD dwSessionId)
{
    return SessionManager_GetSession_Impl(dwSessionId);
}

BOOL SessionManager_AddSession_C(DWORD dwSessionId)
{
    return SessionManager_AddSession_Impl(dwSessionId);
}

void SessionManager_RemoveSession_C(DWORD dwSessionId)
{
    SessionManager_RemoveSession_Impl(dwSessionId);
}

void SessionManager_IncrementActiveRequests_C(DWORD dwSessionId)
{
    SessionManager_IncrementActiveRequests_Impl(dwSessionId);
}

void SessionManager_DecrementActiveRequests_C(DWORD dwSessionId)
{
    SessionManager_DecrementActiveRequests_Impl(dwSessionId);
}

// Enhanced GETOPTION handler with DTB support
#ifdef __cplusplus
extern "C" {
#endif

error_status_t LeechRpc_CommandGetOption_DTB(
    _In_ PLEECHRPC_MSG_DATA pReqData,
    _Out_ long *pcbOut,
    _Out_ byte **ppbOut)
{
    PLEECHRPC_MSG_DATA pRspData = NULL;
    QWORD qwValue = 0;
    DWORD cbRsp;
    
    // Allocate response
    cbRsp = sizeof(LEECHRPC_MSG_DATA);
    if (!(pRspData = (PLEECHRPC_MSG_DATA)LocalAlloc(LMEM_ZEROINIT, cbRsp))) {
        *pcbOut = 0;
        *ppbOut = NULL;
        return (error_status_t)-1;
    }
    
    // Setup response header
    pRspData->cbMsg = cbRsp;
    pRspData->dwMagic = LEECHRPC_MSGMAGIC;
    pRspData->fMsgResult = FALSE;
    pRspData->tpMsg = LEECHRPC_MSGTYPE_GETOPTION_RSP;
    
    // Handle specific options
    if (pReqData->qwData[0] == LC_OPT_MEMORYINFO_OS_DTB) {
        // Get DTB from driver if available
        qwValue = DriverInterface_GetDTB_Impl();
        if (qwValue) {
            pRspData->qwData[0] = qwValue;
            pRspData->fMsgResult = TRUE;
            printf("[DTB] Returning DTB value from driver: 0x%llX\n", qwValue);
        } else {
            // If driver doesn't have DTB, use a default value
            // This is a placeholder - in real implementation, you would
            // need to detect or configure the actual DTB
            pRspData->qwData[0] = 0x1AA000;  // Common Windows 10/11 kernel DTB
            pRspData->fMsgResult = TRUE;
            printf("[DTB] Driver DTB not available, using default: 0x%llX\n", pRspData->qwData[0]);
        }
    } else if (pReqData->qwData[0] == LC_OPT_MEMORYINFO_OS_PFN) {
        // Handle PFN database info if needed
        pRspData->qwData[0] = 0;  // Not implemented yet
        pRspData->fMsgResult = FALSE;
    } else if (pReqData->qwData[0] == LC_OPT_MEMORYINFO_OS_KERNELBASE) {
        // Handle kernel base if needed
        qwValue = DriverInterface_GetKernelBase_Impl();
        if (qwValue) {
            pRspData->qwData[0] = qwValue;
            pRspData->fMsgResult = TRUE;
            printf("[KERNEL] Returning kernel base from driver: 0x%llX\n", qwValue);
        }
    } else {
        // Unknown option
        pRspData->qwData[0] = 0;
        pRspData->fMsgResult = FALSE;
    }
    
    *pcbOut = pRspData->cbMsg;
    *ppbOut = (PBYTE)pRspData;
    return 0;
}

#ifdef __cplusplus
}
#endif
