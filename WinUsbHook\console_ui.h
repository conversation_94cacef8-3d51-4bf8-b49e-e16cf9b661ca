﻿#pragma once

#include <windows.h>
#include <stdio.h>
#include <stdarg.h>
#include <string>
#include <iostream>
#include <fstream>
#include <iomanip> // Required for std::setfill and std::setw


#define DEBUGLOG 1
// Color constants
#define FOREGROUND_CYAN (FOREGROUND_GREEN | FOREGROUND_BLUE)
#define FOREGROUND_YELLOW (FOREGROUND_RED | FOREGROUND_GREEN)
#define FOREGROUND_MAGENTA (FOREGROUND_RED | FOREGROUND_BLUE)

// Console color definitions
enum class LogLevel {
    INFO = 0,
    SUCCESS = 1,
    ERRORS = 2,
    WARNINGS = 3
};

class ConsoleUI {
private:
    static HANDLE hConsole;
    static bool initialized;
    static WORD originalAttributes;
    
    // File logging members
    static std::ofstream logFile;
    static std::string logFilePath;
    static bool fileLoggingEnabled;

    // Color definitions
    static const WORD INFO_COLOR = BACKGROUND_BLUE | FOREGROUND_RED | FOREGROUND_GREEN | FOREGROUND_BLUE | FOREGROUND_INTENSITY;
    static const WORD SUCCESS_COLOR = BACKGROUND_GREEN | FOREGROUND_RED | FOREGROUND_GREEN | FOREGROUND_BLUE | FOREGROUND_INTENSITY;
    static const WORD ERROR_COLOR = BACKGROUND_RED | FOREGROUND_RED | FOREGROUND_GREEN | FOREGROUND_BLUE | FOREGROUND_INTENSITY;
    static const WORD WARNING_COLOR = BACKGROUND_RED | BACKGROUND_GREEN | FOREGROUND_RED | FOREGROUND_GREEN | FOREGROUND_BLUE | FOREGROUND_INTENSITY;

public:
    // Initialize console UI
    static bool Initialize() {
        if (initialized) return true;

        // Set console code page to UTF-8 and Chinese support
        SetConsoleCP(936);          // Chinese GBK input
        SetConsoleOutputCP(936);    // Chinese GBK output

        hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
        if (hConsole == INVALID_HANDLE_VALUE) {
            return false;
        }

        // Get original console attributes
        CONSOLE_SCREEN_BUFFER_INFO consoleInfo;
        if (GetConsoleScreenBufferInfo(hConsole, &consoleInfo)) {
            originalAttributes = consoleInfo.wAttributes;
        }

        // Set console title
        SetConsoleTitleA("CONSOLE");

        initialized = true;
        return true;
    }

    // Initialize file logging
    static bool InitializeFileLogging(const std::string& filePath = "") {
        if (fileLoggingEnabled) return true;
        
        if (filePath.empty()) {
            // Generate default log file name with timestamp
            SYSTEMTIME st;
            GetLocalTime(&st);
            char timeStr[32];
            sprintf_s(timeStr, "%04d%02d%02d_%02d%02d%02d", 
                     st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
            logFilePath = "Hook_" + std::string(timeStr) + ".log";
        } else {
            logFilePath = filePath;
        }
        
        try {
            logFile.open(logFilePath, std::ios::app);
            if (logFile.is_open()) {
                fileLoggingEnabled = true;
                // Write log header
                logFile << "=== Hook Log Started ===" << std::endl;
                logFile.flush();
                return true;
            }
        } catch (...) {
            // File logging failed, continue without it
        }
        
        fileLoggingEnabled = false;
        return false;
    }

    // Print banner
    static void PrintBanner() {
        SetConsoleTextAttribute(hConsole, FOREGROUND_CYAN | FOREGROUND_INTENSITY);
#if 0
        PrintAsciiLogo();
#endif
        SetConsoleTextAttribute(hConsole, originalAttributes);
        printf("\n");
    }

    // Generic log function with level
    static void Log(LogLevel level, const char* format, ...) {
        if (!initialized) Initialize();

        char buffer[1024];
        va_list args;
        va_start(args, format);
        vsnprintf_s(buffer, sizeof(buffer), _TRUNCATE, format, args);
        va_end(args);

        // Get current time
        SYSTEMTIME st;
        GetLocalTime(&st);

        WORD color;
        const char* levelStr;
        const char* prefix;

        switch (level) {
            case LogLevel::INFO:
                color = INFO_COLOR;
                levelStr = "INFO";
                prefix = "[INFO]";
                break;
            case LogLevel::SUCCESS:
                color = SUCCESS_COLOR;
                levelStr = "SUCCESS";
                prefix = "[SUCCESS]";
                break;
            case LogLevel::ERRORS:
                color = ERROR_COLOR;
                levelStr = "ERROR";
                prefix = "[ERROR]";
                break;
            case LogLevel::WARNINGS:
                color = WARNING_COLOR;
                levelStr = "WARNING";
                prefix = "[WARNING]";
                break;
            default:
                color = originalAttributes;
                levelStr = "UNKNOWN";
                prefix = "[UNKNOWN]";
                break;
        }

        // Print timestamp
        SetConsoleTextAttribute(hConsole, FOREGROUND_INTENSITY);
        printf("[%02d:%02d:%02d] ", st.wHour, st.wMinute, st.wSecond);

        // Print level with background color
        SetConsoleTextAttribute(hConsole, color);
        printf(" %-7s ", levelStr);

        // Reset to normal and print message
        SetConsoleTextAttribute(hConsole, originalAttributes);
        printf(" %s\n", buffer);

        fflush(stdout);
    }

    // Specific log level functions with format support
    static void LogInfo(const char* format, ...) {
        if (!initialized) Initialize();
        va_list args;
        va_start(args, format);
        LogVA(LogLevel::INFO, format, args);
        va_end(args);
    }

    static void LogSuccess(const char* format, ...) {
        if (!initialized) Initialize();
        va_list args;
        va_start(args, format);
        LogVA(LogLevel::SUCCESS, format, args);
        va_end(args);
    }

    static void LogError(const char* format, ...) {
        if (!initialized) Initialize();
        va_list args;
        va_start(args, format);
        LogVA(LogLevel::ERRORS, format, args);
        va_end(args);
    }

    static void LogWarning(const char* format, ...) {
        if (!initialized) Initialize();
        va_list args;
        va_start(args, format);
        LogVA(LogLevel::WARNINGS, format, args);
        va_end(args);
    }

private:
    // Internal function for va_list handling
    static void LogVA(LogLevel level, const char* format, va_list args) {
        char buffer[1024];
        vsnprintf_s(buffer, sizeof(buffer), _TRUNCATE, format, args);

        // Get current time
        SYSTEMTIME st;
        GetLocalTime(&st);

        WORD color;
        const char* levelStr;

        switch (level) {
            case LogLevel::INFO:
                color = INFO_COLOR;
                levelStr = "INFO";
                break;
            case LogLevel::SUCCESS:
                color = SUCCESS_COLOR;
                levelStr = "SUCCESS";
                break;
            case LogLevel::ERRORS:
                color = ERROR_COLOR;
                levelStr = "ERROR";
                break;
            case LogLevel::WARNINGS:
                color = WARNING_COLOR;
                levelStr = "WARNING";
                break;
            default:
                color = originalAttributes;
                levelStr = "UNKNOWN";
                break;
        }

        // Print timestamp
        SetConsoleTextAttribute(hConsole, FOREGROUND_INTENSITY);
        printf("[%02d:%02d:%02d] ", st.wHour, st.wMinute, st.wSecond);

        // Print level with background color
        SetConsoleTextAttribute(hConsole, color);
        printf(" %-7s ", levelStr);

        // Reset to normal and print message
        SetConsoleTextAttribute(hConsole, originalAttributes);
        printf(" %s\n", buffer);

        fflush(stdout);
        
        // Write to log file if enabled
        if (fileLoggingEnabled && logFile.is_open()) {
            try {
                logFile << "[" << std::setfill('0') << std::setw(2) << st.wHour << ":"
                       << std::setfill('0') << std::setw(2) << st.wMinute << ":"
                       << std::setfill('0') << std::setw(2) << st.wSecond << "] "
                       << levelStr << " " << buffer << std::endl;
                logFile.flush();
            } catch (...) {
                // File write failed, disable file logging
                fileLoggingEnabled = false;
            }
        }
    }

public:

#if 0
    // Print ASCII logo
    static void PrintAsciiLogo() {
        if (!initialized) Initialize();
        std::cout << R"(
░▒▓███████▓▒░      ░▒▓███████▓▒░        ░▒▓██████▓▒░
░▒▓█▓▒░             ░▒▓█▓▒░░▒▓█▓▒░      ░▒▓█▓▒░░▒▓█▓▒░
░▒▓█▓▒░             ░▒▓█▓▒░░▒▓█▓▒░      ░▒▓█▓▒░
░▒▓██████▓▒░       ░▒▓███████▓▒░       ░▒▓█▓▒▒▓███▓▒░
      ░▒▓█▓▒░      ░▒▓█▓▒░             ░▒▓█▓▒░░▒▓█▓▒░
      ░▒▓█▓▒░      ░▒▓█▓▒░             ░▒▓█▓▒░░▒▓█▓▒░
░▒▓███████▓▒░      ░▒▓█▓▒░              ░▒▓██████▓▒░
)" << std::endl;
    }
#endif

    // Print separator
    static void PrintSeparator() {
        SetConsoleTextAttribute(hConsole, FOREGROUND_INTENSITY);
        printf("--------------------------------------------------------------------------------\n");
        SetConsoleTextAttribute(hConsole, originalAttributes);
    }

    // Print section header
    static void PrintSection(const char* title) {
        SetConsoleTextAttribute(hConsole, FOREGROUND_CYAN | FOREGROUND_INTENSITY);
        printf("\n=== %s ===============================================================\n", title);
        SetConsoleTextAttribute(hConsole, originalAttributes);
    }

    // Clean up
    static void Cleanup() {
        if (initialized && hConsole != INVALID_HANDLE_VALUE) {
            SetConsoleTextAttribute(hConsole, originalAttributes);
        }
        
        // Close log file
        if (fileLoggingEnabled && logFile.is_open()) {
            try {
                logFile << "=== WinUsbHook Log Ended ===" << std::endl;
                logFile.close();
            } catch (...) {
                // Ignore cleanup errors
            }
            fileLoggingEnabled = false;
        }
    }
    
    // Get current log file path
    static std::string GetLogFilePath() {
        return logFilePath;
    }
    
    // Check if file logging is enabled
    static bool IsFileLoggingEnabled() {
        return fileLoggingEnabled;
    }
};


// Convenience macros
#define LOG_INFO(fmt, ...) ConsoleUI::LogInfo(fmt, ##__VA_ARGS__)
#define LOG_SUCCESS(fmt, ...) ConsoleUI::LogSuccess(fmt, ##__VA_ARGS__)
#define LOG_ERROR(fmt, ...) ConsoleUI::LogError(fmt, ##__VA_ARGS__)
#define LOG_WARNING(fmt, ...) ConsoleUI::LogWarning(fmt, ##__VA_ARGS__)

