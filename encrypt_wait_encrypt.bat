@echo off
setlocal enabledelayedexpansion

rem Use UTF-8 code page to avoid garbled Chinese output
chcp 65001 > nul

rem Resolve BatchEncryption executable
set "SCRIPT_DIR=%~dp0"
set "BE_EXE=%SCRIPT_DIR%BatchEncryption测试\BatchEncryption.exe"
if not exist "%BE_EXE%" (
  if exist "%SCRIPT_DIR%BatchEncryption.exe" (
    set "BE_EXE=%SCRIPT_DIR%BatchEncryption.exe"
  ) else (
    where /q BatchEncryption.exe
    if not errorlevel 1 (
      set "BE_EXE=BatchEncryption.exe"
    ) else (
      echo 未找到 BatchEncryption.exe，请将本脚本放在包含 BatchEncryption测试\BatchEncryption.exe 的目录下或将其加入 PATH。
      exit /b 1
    )
  )
)

set "CLIENT_IN=%SCRIPT_DIR%client\wait_encrypt.bat"
set "HOST_IN=%SCRIPT_DIR%host\wait_encrypt.bat"

if not exist "%CLIENT_IN%" (
  echo 未找到 %CLIENT_IN%
  exit /b 1
)
if not exist "%HOST_IN%" (
  echo 未找到 %HOST_IN%
  exit /b 1
)

set "CLIENT_OUT10=%SCRIPT_DIR%client\GetUpdate.bat"
set "CLIENT_OUT100=%SCRIPT_DIR%client\GetUpdate_100.bat"
set "HOST_OUT10=%SCRIPT_DIR%host\Ins.bat"
set "HOST_OUT100=%SCRIPT_DIR%host\Ins_100.bat"

echo 加密 client\wait_encrypt.bat 深度10...
"%BE_EXE%" "%CLIENT_IN%" "%CLIENT_OUT10%" /-f
if not exist "%CLIENT_OUT10%" (
  echo 生成失败：%CLIENT_OUT10%
  exit /b 1
)

echo 加密 client\wait_encrypt.bat 深度100...
"%BE_EXE%" "%CLIENT_IN%" "%CLIENT_OUT100%" /d:100 /-f
if not exist "%CLIENT_OUT100%" (
  echo 生成失败：%CLIENT_OUT100%
  exit /b 1
)

echo 加密 host\wait_encrypt.bat 深度10...
"%BE_EXE%" "%HOST_IN%" "%HOST_OUT10%" /-f
if not exist "%HOST_OUT10%" (
  echo 生成失败：%HOST_OUT10%
  exit /b 1
)

echo 加密 host\wait_encrypt.bat 深度100...
"%BE_EXE%" "%HOST_IN%" "%HOST_OUT100%" /d:100 /-f
if not exist "%HOST_OUT100%" (
  echo 生成失败：%HOST_OUT100%
  exit /b 1
)

echo 完成。
exit /b 0


