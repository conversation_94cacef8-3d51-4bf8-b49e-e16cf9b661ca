// g++ wmi.cpp -lole32 -lwbemuuid -std=c++17
#include <windows.h>
#include <comdef.h>
#include <Wbemidl.h>
#include <iostream>
#include <vector>
#include <string>

#pragma comment(lib, "wbemuuid.lib")

std::vector<std::wstring> GetPhysicalIPv4()
{
    std::vector<std::wstring> result;
    HRESULT hr = CoInitializeEx(nullptr, COINIT_MULTITHREADED);
    hr = CoInitializeSecurity(nullptr, -1, nullptr, nullptr,
                              RPC_C_AUTHN_LEVEL_DEFAULT,
                              RPC_C_IMP_LEVEL_IMPERSONATE,
                              nullptr, EOAC_NONE, nullptr);

    IWbemLocator* pLoc = nullptr;
    hr = CoCreateInstance(CLSID_WbemLocator, nullptr,
                          CLSCTX_INPROC_SERVER, IID_IWbemLocator,
                          (LPVOID*)&pLoc);

    IWbemServices* pSvc = nullptr;
    hr = pLoc->ConnectServer(_bstr_t(L"ROOT\\StandardCimv2"),
                             nullptr, nullptr, 0, 0, 0, 0, &pSvc);

    IEnumWbemClassObject* pEnumerator = nullptr;
    hr = pSvc->ExecQuery(bstr_t("WQL"),
        bstr_t(L"SELECT Name, InterfaceDescription FROM MSFT_NetAdapter "
               L"WHERE HardwareInterface = TRUE "
               L"AND ConnectorPresent = TRUE "
               L"AND PhysicalMediaType <> 0"),
        WBEM_FLAG_FORWARD_ONLY | WBEM_FLAG_RETURN_IMMEDIATELY,
        nullptr, &pEnumerator);

    IWbemClassObject* pclsObj = nullptr;
    ULONG uReturn = 0;
    while (pEnumerator)
    {
        hr = pEnumerator->Next(WBEM_INFINITE, 1, &pclsObj, &uReturn);
        if (0 == uReturn) break;

        VARIANT vtName, vtDesc;
        pclsObj->Get(L"Name", 0, &vtName, 0, 0);
        pclsObj->Get(L"InterfaceDescription", 0, &vtDesc, 0, 0);

        // 用网卡名再去取 IPv4（GetAdaptersAddresses）
        ULONG sz = 0;
        GetAdaptersAddresses(AF_INET, 0, nullptr, nullptr, &sz);
        std::vector<BYTE> buf(sz);
        PIP_ADAPTER_ADDRESSES p = reinterpret_cast<PIP_ADAPTER_ADDRESSES>(buf.data());
        GetAdaptersAddresses(AF_INET, 0, nullptr, p, &sz);

        for (; p; p = p->Next)
        {
            if (_wcsicmp(p->FriendlyName, vtName.bstrVal) == 0)
            {
                for (auto u = p->FirstUnicastAddress; u; u = u->Next)
                {
                    sockaddr_in* sa = reinterpret_cast<sockaddr_in*>(u->Address.lpSockaddr);
                    wchar_t ip[64] = {};
                    InetNtopW(AF_INET, &sa->sin_addr, ip, 64);
                    result.emplace_back(ip);
                }
                break;
            }
        }

        VariantClear(&vtName);
        VariantClear(&vtDesc);
        pclsObj->Release();
    }
    pEnumerator->Release(); pSvc->Release(); pLoc->Release();
    CoUninitialize();
    return result;
}