// winusb_hook_vmm.cpp - VMMDLL_Initialize parameter replacer
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#include "Minhook/MinHook.h"
#endif
#include <windows.h>
#include <cstdio>
#include <winusb.h> 
#include <cstring>
#include <Ws2tcpip.h>
#include <Iphlpapi.h>
#include <TlHelp32.h>
#include <string.h>
#include <vector>
#include <string>
#include "console_ui.h"
#include "connection_manager.h"
#include "../CustomLeechServer/include/VMP.h"
// LeechCore config types for LcCreate/LcCreateEx hook
#include "../CustomLeechServer/include/leechcore.h"
#include "minhook/MinHook.h"
#pragma comment(lib, "user32.lib")
#pragma comment(lib, "Ws2_32.lib")
#pragma comment(lib, "Iphlpapi.lib")
#include <Shlwapi.h>
#include <Psapi.h>
#include <iostream>

#include "sw_mem.h"

#pragma comment(lib, "Shlwapi.lib")
#pragma comment(lib, "Psapi.lib")
#pragma comment(linker, "/CETCOMPAT:NO")  // 绂佺敤CET鍏煎鎬�
#pragma comment(linker, "/EXPORT:WinUsb_AbortPipe=AheadLibEx_WinUsb_AbortPipe,@1")
#pragma comment(linker, "/EXPORT:WinUsb_AbortPipeAsync=AheadLibEx_WinUsb_AbortPipeAsync,@2")
#pragma comment(linker, "/EXPORT:WinUsb_ControlTransfer=AheadLibEx_WinUsb_ControlTransfer,@3")
#pragma comment(linker, "/EXPORT:WinUsb_FlushPipe=AheadLibEx_WinUsb_FlushPipe,@4")
#pragma comment(linker, "/EXPORT:WinUsb_Free=AheadLibEx_WinUsb_Free,@5")
#pragma comment(linker, "/EXPORT:WinUsb_GetAdjustedFrameNumber=AheadLibEx_WinUsb_GetAdjustedFrameNumber,@6")
#pragma comment(linker, "/EXPORT:WinUsb_GetAssociatedInterface=AheadLibEx_WinUsb_GetAssociatedInterface,@7")
#pragma comment(linker, "/EXPORT:WinUsb_GetCurrentAlternateSetting=AheadLibEx_WinUsb_GetCurrentAlternateSetting,@8")
#pragma comment(linker, "/EXPORT:WinUsb_GetCurrentFrameNumber=AheadLibEx_WinUsb_GetCurrentFrameNumber,@9")
#pragma comment(linker, "/EXPORT:WinUsb_GetCurrentFrameNumberAndQpc=AheadLibEx_WinUsb_GetCurrentFrameNumberAndQpc,@10")
#pragma comment(linker, "/EXPORT:WinUsb_GetDescriptor=AheadLibEx_WinUsb_GetDescriptor,@11")
#pragma comment(linker, "/EXPORT:WinUsb_GetOverlappedResult=AheadLibEx_WinUsb_GetOverlappedResult,@12")
#pragma comment(linker, "/EXPORT:WinUsb_GetPipePolicy=AheadLibEx_WinUsb_GetPipePolicy,@13")
#pragma comment(linker, "/EXPORT:WinUsb_GetPowerPolicy=AheadLibEx_WinUsb_GetPowerPolicy,@14")
#pragma comment(linker, "/EXPORT:WinUsb_Initialize=AheadLibEx_WinUsb_Initialize,@15")
#pragma comment(linker, "/EXPORT:WinUsb_ParseConfigurationDescriptor=AheadLibEx_WinUsb_ParseConfigurationDescriptor,@16")
#pragma comment(linker, "/EXPORT:WinUsb_ParseDescriptors=AheadLibEx_WinUsb_ParseDescriptors,@17")
#pragma comment(linker, "/EXPORT:WinUsb_QueryDeviceInformation=AheadLibEx_WinUsb_QueryDeviceInformation,@18")
#pragma comment(linker, "/EXPORT:WinUsb_QueryInterfaceSettings=AheadLibEx_WinUsb_QueryInterfaceSettings,@19")
#pragma comment(linker, "/EXPORT:WinUsb_QueryPipe=AheadLibEx_WinUsb_QueryPipe,@20")
#pragma comment(linker, "/EXPORT:WinUsb_QueryPipeEx=AheadLibEx_WinUsb_QueryPipeEx,@21")
#pragma comment(linker, "/EXPORT:WinUsb_ReadIsochPipe=AheadLibEx_WinUsb_ReadIsochPipe,@22")
#pragma comment(linker, "/EXPORT:WinUsb_ReadIsochPipeAsap=AheadLibEx_WinUsb_ReadIsochPipeAsap,@23")
#pragma comment(linker, "/EXPORT:WinUsb_ReadPipe=AheadLibEx_WinUsb_ReadPipe,@24")
#pragma comment(linker, "/EXPORT:WinUsb_RegisterIsochBuffer=AheadLibEx_WinUsb_RegisterIsochBuffer,@25")
#pragma comment(linker, "/EXPORT:WinUsb_ResetPipe=AheadLibEx_WinUsb_ResetPipe,@26")
#pragma comment(linker, "/EXPORT:WinUsb_ResetPipeAsync=AheadLibEx_WinUsb_ResetPipeAsync,@27")
#pragma comment(linker, "/EXPORT:WinUsb_SetCurrentAlternateSetting=AheadLibEx_WinUsb_SetCurrentAlternateSetting,@28")
#pragma comment(linker, "/EXPORT:WinUsb_SetCurrentAlternateSettingAsync=AheadLibEx_WinUsb_SetCurrentAlternateSettingAsync,@29")
#pragma comment(linker, "/EXPORT:WinUsb_SetPipePolicy=AheadLibEx_WinUsb_SetPipePolicy,@30")
#pragma comment(linker, "/EXPORT:WinUsb_SetPowerPolicy=AheadLibEx_WinUsb_SetPowerPolicy,@31")
#pragma comment(linker, "/EXPORT:WinUsb_StartTrackingForTimeSync=AheadLibEx_WinUsb_StartTrackingForTimeSync,@32")
#pragma comment(linker, "/EXPORT:WinUsb_StopTrackingForTimeSync=AheadLibEx_WinUsb_StopTrackingForTimeSync,@33")
#pragma comment(linker, "/EXPORT:WinUsb_UnregisterIsochBuffer=AheadLibEx_WinUsb_UnregisterIsochBuffer,@34")
#pragma comment(linker, "/EXPORT:WinUsb_WriteIsochPipe=AheadLibEx_WinUsb_WriteIsochPipe,@35")
#pragma comment(linker, "/EXPORT:WinUsb_WriteIsochPipeAsap=AheadLibEx_WinUsb_WriteIsochPipeAsap,@36")
#pragma comment(linker, "/EXPORT:WinUsb_WritePipe=AheadLibEx_WinUsb_WritePipe,@37")
// Original function pointers

//40 53 55 56 41 54 41 56 48 81 EC 90 03
char g_serverIP[16] = "127.0.0.1";
HMODULE g_hOriginalDll = NULL;

// ========== 使用外部ConnectionManager模块 ==========
FARPROC WINAPI GetAddress(PCSTR pszProcName)
{
	FARPROC fpAddress;
	CHAR szProcName[64];
	TCHAR tzTemp[MAX_PATH];

	fpAddress = GetProcAddress(g_hOriginalDll, pszProcName);
	if (fpAddress == NULL)
	{
		if (HIWORD(pszProcName) == 0)
		{
			wsprintfA(szProcName, "#%d", pszProcName);
			pszProcName = szProcName;
		}

		wsprintf(tzTemp, TEXT("无法找到函数 %hs,程序无法正常运行"), pszProcName);
		MessageBox(NULL, tzTemp, TEXT("Core"), MB_ICONSTOP);
		ExitProcess(-2);
	}
	return fpAddress;
}
extern "C"
{
	PVOID pfnAheadLibEx_WinUsb_AbortPipe;
	PVOID pfnAheadLibEx_WinUsb_AbortPipeAsync;
	PVOID pfnAheadLibEx_WinUsb_ControlTransfer;
	PVOID pfnAheadLibEx_WinUsb_FlushPipe;
	PVOID pfnAheadLibEx_WinUsb_Free;
	PVOID pfnAheadLibEx_WinUsb_GetAdjustedFrameNumber;
	PVOID pfnAheadLibEx_WinUsb_GetAssociatedInterface;
	PVOID pfnAheadLibEx_WinUsb_GetCurrentAlternateSetting;
	PVOID pfnAheadLibEx_WinUsb_GetCurrentFrameNumber;
	PVOID pfnAheadLibEx_WinUsb_GetCurrentFrameNumberAndQpc;
	PVOID pfnAheadLibEx_WinUsb_GetDescriptor;
	PVOID pfnAheadLibEx_WinUsb_GetOverlappedResult;
	PVOID pfnAheadLibEx_WinUsb_GetPipePolicy;
	PVOID pfnAheadLibEx_WinUsb_GetPowerPolicy;
	PVOID pfnAheadLibEx_WinUsb_Initialize;
	PVOID pfnAheadLibEx_WinUsb_ParseConfigurationDescriptor;
	PVOID pfnAheadLibEx_WinUsb_ParseDescriptors;
	PVOID pfnAheadLibEx_WinUsb_QueryDeviceInformation;
	PVOID pfnAheadLibEx_WinUsb_QueryInterfaceSettings;
	PVOID pfnAheadLibEx_WinUsb_QueryPipe;
	PVOID pfnAheadLibEx_WinUsb_QueryPipeEx;
	PVOID pfnAheadLibEx_WinUsb_ReadIsochPipe;
	PVOID pfnAheadLibEx_WinUsb_ReadIsochPipeAsap;
	PVOID pfnAheadLibEx_WinUsb_ReadPipe;
	PVOID pfnAheadLibEx_WinUsb_RegisterIsochBuffer;
	PVOID pfnAheadLibEx_WinUsb_ResetPipe;
	PVOID pfnAheadLibEx_WinUsb_ResetPipeAsync;
	PVOID pfnAheadLibEx_WinUsb_SetCurrentAlternateSetting;
	PVOID pfnAheadLibEx_WinUsb_SetCurrentAlternateSettingAsync;
	PVOID pfnAheadLibEx_WinUsb_SetPipePolicy;
	PVOID pfnAheadLibEx_WinUsb_SetPowerPolicy;
	PVOID pfnAheadLibEx_WinUsb_StartTrackingForTimeSync;
	PVOID pfnAheadLibEx_WinUsb_StopTrackingForTimeSync;
	PVOID pfnAheadLibEx_WinUsb_UnregisterIsochBuffer;
	PVOID pfnAheadLibEx_WinUsb_WriteIsochPipe;
	PVOID pfnAheadLibEx_WinUsb_WriteIsochPipeAsap;
	PVOID pfnAheadLibEx_WinUsb_WritePipe;
}
static BOOL ProtectMemoryWithSyscall(PVOID address, SIZE_T size, ULONG newProtect, PULONG oldProtect)
{
	PVOID baseAddress = address;
	SIZE_T regionSize = size;
	NTSTATUS status = Sw3NtProtectVirtualMemory(GetCurrentProcess(), &baseAddress, &regionSize, newProtect, oldProtect);
	return status >= 0;
}

BOOL WINAPI Init()
{
	pfnAheadLibEx_WinUsb_AbortPipe = GetAddress("WinUsb_AbortPipe");
	pfnAheadLibEx_WinUsb_AbortPipeAsync = GetAddress("WinUsb_AbortPipeAsync");
	pfnAheadLibEx_WinUsb_ControlTransfer = GetAddress("WinUsb_ControlTransfer");
	pfnAheadLibEx_WinUsb_FlushPipe = GetAddress("WinUsb_FlushPipe");
	pfnAheadLibEx_WinUsb_Free = GetAddress("WinUsb_Free");
	pfnAheadLibEx_WinUsb_GetAdjustedFrameNumber = GetAddress("WinUsb_GetAdjustedFrameNumber");
	pfnAheadLibEx_WinUsb_GetAssociatedInterface = GetAddress("WinUsb_GetAssociatedInterface");
	pfnAheadLibEx_WinUsb_GetCurrentAlternateSetting = GetAddress("WinUsb_GetCurrentAlternateSetting");
	pfnAheadLibEx_WinUsb_GetCurrentFrameNumber = GetAddress("WinUsb_GetCurrentFrameNumber");
	pfnAheadLibEx_WinUsb_GetCurrentFrameNumberAndQpc = GetAddress("WinUsb_GetCurrentFrameNumberAndQpc");
	pfnAheadLibEx_WinUsb_GetDescriptor = GetAddress("WinUsb_GetDescriptor");
	pfnAheadLibEx_WinUsb_GetOverlappedResult = GetAddress("WinUsb_GetOverlappedResult");
	pfnAheadLibEx_WinUsb_GetPipePolicy = GetAddress("WinUsb_GetPipePolicy");
	pfnAheadLibEx_WinUsb_GetPowerPolicy = GetAddress("WinUsb_GetPowerPolicy");
	pfnAheadLibEx_WinUsb_Initialize = GetAddress("WinUsb_Initialize");
	pfnAheadLibEx_WinUsb_ParseConfigurationDescriptor = GetAddress("WinUsb_ParseConfigurationDescriptor");
	pfnAheadLibEx_WinUsb_ParseDescriptors = GetAddress("WinUsb_ParseDescriptors");
	pfnAheadLibEx_WinUsb_QueryDeviceInformation = GetAddress("WinUsb_QueryDeviceInformation");
	pfnAheadLibEx_WinUsb_QueryInterfaceSettings = GetAddress("WinUsb_QueryInterfaceSettings");
	pfnAheadLibEx_WinUsb_QueryPipe = GetAddress("WinUsb_QueryPipe");
	pfnAheadLibEx_WinUsb_QueryPipeEx = GetAddress("WinUsb_QueryPipeEx");
	pfnAheadLibEx_WinUsb_ReadIsochPipe = GetAddress("WinUsb_ReadIsochPipe");
	pfnAheadLibEx_WinUsb_ReadIsochPipeAsap = GetAddress("WinUsb_ReadIsochPipeAsap");
	pfnAheadLibEx_WinUsb_ReadPipe = GetAddress("WinUsb_ReadPipe");
	pfnAheadLibEx_WinUsb_RegisterIsochBuffer = GetAddress("WinUsb_RegisterIsochBuffer");
	pfnAheadLibEx_WinUsb_ResetPipe = GetAddress("WinUsb_ResetPipe");
	pfnAheadLibEx_WinUsb_ResetPipeAsync = GetAddress("WinUsb_ResetPipeAsync");
	pfnAheadLibEx_WinUsb_SetCurrentAlternateSetting = GetAddress("WinUsb_SetCurrentAlternateSetting");
	pfnAheadLibEx_WinUsb_SetCurrentAlternateSettingAsync = GetAddress("WinUsb_SetCurrentAlternateSettingAsync");
	pfnAheadLibEx_WinUsb_SetPipePolicy = GetAddress("WinUsb_SetPipePolicy");
	pfnAheadLibEx_WinUsb_SetPowerPolicy = GetAddress("WinUsb_SetPowerPolicy");
	pfnAheadLibEx_WinUsb_StartTrackingForTimeSync = GetAddress("WinUsb_StartTrackingForTimeSync");
	pfnAheadLibEx_WinUsb_StopTrackingForTimeSync = GetAddress("WinUsb_StopTrackingForTimeSync");
	pfnAheadLibEx_WinUsb_UnregisterIsochBuffer = GetAddress("WinUsb_UnregisterIsochBuffer");
	pfnAheadLibEx_WinUsb_WriteIsochPipe = GetAddress("WinUsb_WriteIsochPipe");
	pfnAheadLibEx_WinUsb_WriteIsochPipeAsap = GetAddress("WinUsb_WriteIsochPipeAsap");
	pfnAheadLibEx_WinUsb_WritePipe = GetAddress("WinUsb_WritePipe");
	return TRUE;
}

typedef PVOID(*pfnVMMDLL_Initialize)(DWORD argc, LPCSTR argv[]);
pfnVMMDLL_Initialize Original_VMMDLL_Initialize = NULL;
static PVOID g_trampolineVmmdllInitialize = NULL;
static BOOL InstallInlineDetourHook(PVOID targetFunction, PVOID hookFunction);

static volatile LONG g_inCreateExGate = 0;
// -------------------------------------------------------------------------
// New: LeechCore LcCreate / LcCreateEx hook definitions
// -------------------------------------------------------------------------
typedef HANDLE(*pfnLcCreateEx)(PLC_CONFIG pLcCreateConfig, PPLC_CONFIG_ERRORINFO ppLcErrorInfo);
typedef HANDLE(*pfnLcCreate)(PLC_CONFIG pLcCreateConfig);
static pfnLcCreateEx Original_LcCreateEx = NULL;
static pfnLcCreate Original_LcCreate = NULL;
typedef VOID(*pfnLcClose)(HANDLE hLC);
static pfnLcClose Original_LcClose = NULL;
static HANDLE g_remotePrimary = NULL;

// ------------------------------
// Helpers: dump LC_CONFIG and synthetic argv
// ------------------------------
static void DumpLcConfig(const char* tag, const LC_CONFIG* cfg)
{
	if (!cfg) { LOG_INFO("[%s] cfg = NULL", tag ? tag : "Cfg"); return; }
	LOG_INFO("[%s] dwVersion=0x%08X dwVerb=%u paMax=0x%llX pfn_printf=%p", tag ? tag : "Cfg",
		cfg->dwVersion, cfg->dwPrintfVerbosity, (unsigned long long)cfg->paMax, cfg->pfn_printf_opt);
	LOG_INFO("[%s] device='%s' remote='%s'", tag ? tag : "Cfg", cfg->szDevice, cfg->szRemote);
}

static void DumpSyntheticArgv(const char* tag, const LC_CONFIG* cfg)
{
	if (!cfg) return;
	LOG_INFO("[%s] argv: -device %s -remote %s", tag ? tag : "Argv", cfg->szDevice, cfg->szRemote);
}

static void DebugDumpBytes(const char* tag, const void* p, SIZE_T n)
{
#if DBG_LOG
	if (!p || n == 0) return;
	const unsigned char* b = (const unsigned char*)p;
	char line[256]; line[0] = 0;
	SIZE_T m = (n > 32) ? 32 : n; // cap at 32 bytes
	SIZE_T o = 0;
	for (SIZE_T i = 0; i < m; ++i) {
		int wrote = _snprintf_s(line + o, sizeof(line) - o, _TRUNCATE, "%02X ", b[i]);
		if (wrote < 0) break; o += (SIZE_T)wrote;
	}
	LOG_INFO("[%s] %p : %s", tag, p, line);
#endif
}

// Our hook function

// Forward declarations for globals used by VEH restoration inside hook
extern "C" {
	extern PVOID g_hVeh;
	extern BYTE  g_savedEntryByte;
	extern PBYTE g_entryAddr;
}

// ------------------------------
// 网络探测函数已移至 connection_manager.cpp
// ------------------------------

// ========== ConnectionManager 实现已移至 connection_manager.cpp ==========

    if (initialized && userInteractionCompleted) {
        if (outIP && ipSize > 0) {
            strncpy_s(outIP, ipSize, serverIP, _TRUNCATE);
        }
        if (outUri && uriSize > 0) {
            strncpy_s(outUri, uriSize, remoteUri, _TRUNCATE);
        }
        LeaveCriticalSection(&cs);
        return true;
    }
    
    if (!userInteractionCompleted) {
        char discoveredIp[16] = { 0 };
        const USHORT rpcPort = 28473;
        BOOL fFound = FALSE;

        std::cout << "============================================" << std::endl;
        std::cout << "连接选项" << std::endl;
        std::cout << "============================================" << std::endl;
        std::cout << "1. 自动扫描局域网主机" << std::endl;
        std::cout << "2. 手动输入主机IP地址" << std::endl;
        std::cout << "请选择 (1-2): ";
        
        int choice = 1;
        char input[16] = {0};
        if (fgets(input, sizeof(input), stdin)) {
            choice = atoi(input);
        }

        if (choice == 2) {
            // 鎵嬪姩杈撳叆IP
            LOG_INFO("请输入主机IP地址:");
            char ipInput[32] = {0};
            if (fgets(ipInput, sizeof(ipInput), stdin)) {
                ipInput[strcspn(ipInput, "\r\n")] = 0;
                LOG_INFO("正在连接主机:%s", ipInput);
                
                if (IsTcpPortOpenFast(ipInput, rpcPort, 1000)) {
                    strcpy_s(discoveredIp, sizeof(discoveredIp), ipInput);
                    fFound = TRUE;
                    LOG_SUCCESS("连接成功:%s", ipInput);
                } else {
                    LOG_ERROR("连接失败:%s", ipInput);
                }
            }
        } else {
            // 鑷姩鎵弿
            LOG_INFO("正在自动扫描局域网主机...");
            fFound = DiscoverServerIpInLAN(discoveredIp, sizeof(discoveredIp), rpcPort);
            if (fFound) {
                LOG_SUCCESS("自动扫描成功:%s", discoveredIp);
            }
        }

        if (!fFound || discoveredIp[0] == '\0') {
            LOG_ERROR("无法找到主机");
            MessageBoxA(NULL, "无法找到主机", "连接失败", MB_ICONERROR | MB_OK);
            LeaveCriticalSection(&cs);
            return false;
        }

        // 保存结果
        strcpy_s(serverIP, sizeof(serverIP), discoveredIp);
        sprintf_s(remoteUri, sizeof(remoteUri), "rpc://insecure:%s:nocompress", discoveredIp);
        
        userInteractionCompleted = true;
        initialized = true;
        
        LOG_SUCCESS("连接成功: %s", serverIP);
    }
    
    // 返回结果
    if (outIP && ipSize > 0) {
        strncpy_s(outIP, ipSize, serverIP, _TRUNCATE);
    }
    if (outUri && uriSize > 0) {
        strncpy_s(outUri, uriSize, remoteUri, _TRUNCATE);
    }
    
    LeaveCriticalSection(&cs);
    return true;
}

static volatile LONG g_inHook = 0;
PVOID WINAPI Hooked_VMMDLL_Initialize(DWORD argc, LPCSTR argv[])
{
	VMProtectBeginMutation("Hook::VMMDLL_Initialize");
	// Guard against re-entrancy: only allow one active hook execution at a time
	if (InterlockedCompareExchange(&g_inHook, 1, 0) != 0) {
		// Already inside hook; pass through to avoid infinite recursion
		// If INT3 VEH is active restore original byte during the pass-through
		PVOID rpass = NULL;
		if (g_entryAddr && g_hVeh) {
			DWORD oldProt = 0, tmpProt = 0;
			if (ProtectMemoryWithSyscall((void*)g_entryAddr, 1, PAGE_EXECUTE_READWRITE, &oldProt)) {
				*g_entryAddr = g_savedEntryByte;
				ProtectMemoryWithSyscall(g_entryAddr, 1, oldProt, &tmpProt);
			}
			rpass = Original_VMMDLL_Initialize(argc, argv);
			if (ProtectMemoryWithSyscall(g_entryAddr, 1, oldProt, &tmpProt)) {
				*g_entryAddr = 0xCC;
				ProtectMemoryWithSyscall(g_entryAddr, 1, oldProt, &tmpProt);
			}
		}
		else {
			rpass = Original_VMMDLL_Initialize(argc, argv);
		}
		return rpass;
	}
#if DBG_LOG
	LOG_INFO("--- VMMDLL_Initialize HOOKED (Parameter Replacement) ---");
	// Log original argc/argv from caller
	LOG_INFO("Original argc: %u", (unsigned)argc);
	for (DWORD i = 0; i < argc; ++i) {
		if (argv && argv[i]) LOG_INFO("Original argv[%u]: %s", (unsigned)i, argv[i]);
	}
#endif

	// 浣跨敤鍏ㄥ眬杩炴帴绠＄悊鍣ㄨ幏鍙栨湇鍔″櫒淇℃伅
	ConnectionManager* connMgr = ConnectionManager::GetInstance();
	char serverIP[16] = {0};
	static char s_remoteUri[128] = { 0 };
	
	if (!connMgr->GetServerConnection(serverIP, sizeof(serverIP), s_remoteUri, sizeof(s_remoteUri))) {
		LOG_ERROR("获取服务器连接失败");
		InterlockedExchange(&g_inHook, 0);
		VMProtectEnd();
		ExitProcess(1001);
	}





	std::cout << "1. 自动扫描局域网主机" << std::endl;
	std::cout << "2. 手动输入主机IP地址" << std::endl;
	std::cout << "请选择 (1-2): ";
	
	int choice = 1; // 姒涙ǹ顓婚柅澶嬪閼奉亜濮╅幍顐ｅ伎
	char input[16] = {0};
	if (fgets(input, sizeof(input), stdin)) {
		choice = atoi(input);
	}

	if (choice == 2) {
		// Manual IP input
		LOG_INFO("请输入主机IP地址:");
		char ipInput[32] = {0};
		if (fgets(ipInput, sizeof(ipInput), stdin)) {
			// Remove trailing newline
			ipInput[strcspn(ipInput, "\r\n")] = 0;
			
			LOG_INFO("正在连接主机:%s" , ipInput );
			
			// Verify the specified server
			if (IsTcpPortOpenFast(ipInput, rpcPort, 1000)) {
				strcpy_s(discoveredIp, sizeof(discoveredIp), ipInput);
				fFound = TRUE;
				LOG_SUCCESS("连接成功:%s" , ipInput );
			} else {
				LOG_ERROR("连接失败:%s",ipInput);
			}
		}
	} else {
		// Auto scan (default option)
		LOG_INFO("正在自动扫描局域网主机...");
		fFound = DiscoverServerIpInLAN(discoveredIp, sizeof(discoveredIp), rpcPort);
		if (fFound) {
			LOG_SUCCESS("自动扫描成功:%s",discoveredIp);
		}
	}

	if (!fFound || discoveredIp[0] == '\0') {
#if DBG_LOG
		LOG_INFO("Server connection failed: no RPC server found.");
#endif
		LOG_ERROR("无法找到主机");
		MessageBoxA(NULL, "无法找到主机", "连接失败", MB_ICONERROR | MB_OK);
		VMProtectEnd();
		ExitProcess(1001);
	}
	sprintf_s(s_remoteUri, sizeof(s_remoteUri), "rpc://insecure:%s:nocompress", discoveredIp);

	// Build fixed args: always enforce RPC remote with no-refresh
	static std::vector<LPCSTR> args;
	if (args.empty()) { // Initialize only once
		args.reserve(12);
		// NOTE: vmmdll_core.c parses from argv[0] as first option; do not insert empty program name here.
		args.push_back("-device");
		args.push_back("rpc");
		args.push_back("-remote");
		args.push_back(s_remoteUri);
		args.push_back("-norefresh");
	}

	int new_argc = (int)args.size();
#if DBG_LOG
	LOG_INFO("New argc: %d", new_argc);
	for (int i = 0; i < new_argc; i++) {
		if (args[i]) {
			LOG_INFO("New argv[%d]: %s", i, args[i]);
		}
	}

	LOG_INFO("Calling original VMMDLL_Initialize with new parameters...");
#endif
	PVOID r = NULL;
	if (g_entryAddr && g_hVeh) {
		DWORD oldProt = 0, tmpProt = 0;
		if (ProtectMemoryWithSyscall(g_entryAddr, 1, PAGE_EXECUTE_READWRITE, &oldProt)) {
			*g_entryAddr = g_savedEntryByte;
			ProtectMemoryWithSyscall(g_entryAddr, 1, oldProt, &tmpProt);
		}
		r = Original_VMMDLL_Initialize((DWORD)new_argc, args.data());
		if (ProtectMemoryWithSyscall(g_entryAddr, 1, PAGE_EXECUTE_READWRITE, &oldProt)) {
			*g_entryAddr = 0xCC;
			ProtectMemoryWithSyscall(g_entryAddr, 1, oldProt, &tmpProt);
		}
	}
	else {
		r = Original_VMMDLL_Initialize((DWORD)new_argc, args.data());
	}
	LOG_SUCCESS("连接成功!");
	InterlockedExchange(&g_inHook, 0);
	VMProtectEnd();
	return r;
}

static BOOL ShouldRewriteToRpc_VMM(PLC_CONFIG pCfg)
{
	if (!pCfg) return FALSE;
	if (pCfg->szRemote[0]) {
		if (!_strnicmp(pCfg->szRemote, "rpc://", 6)) return FALSE; // already rpc
		if (!_strnicmp(pCfg->szRemote, "grpc://", 7)) return FALSE;
		if (!_strnicmp(pCfg->szRemote, "smb://", 6)) return FALSE;
	}
	return TRUE;
}
static void RewriteConfigToRpc_VMM(PLC_CONFIG pCfg)
{
	if (!pCfg) return;

	// Cache for repeated calls with same parameters
	static char s_cachedDevice[64] = { 0 };
	static char s_cachedRemote[260] = { 0 };
	static char s_cachedResultDevice[64] = { 0 };
	static char s_cachedResultRemote[260] = { 0 };
	static BOOL s_cacheValid = FALSE;

	// Check if this is the same configuration as last time
	if (s_cacheValid && 
		strcmp(pCfg->szDevice, s_cachedDevice) == 0 && 
		strcmp(pCfg->szRemote, s_cachedRemote) == 0) {
		// Use cached result
#if DBG_LOG
		LOG_INFO("[RPC] Using cached rewrite result: device=%s, remote=%s", s_cachedResultDevice, s_cachedResultRemote);
#endif
		strcpy_s(pCfg->szDevice, sizeof(pCfg->szDevice), s_cachedResultDevice);
		strcpy_s(pCfg->szRemote, sizeof(pCfg->szRemote), s_cachedResultRemote);
		return;
	}


	strcpy_s(s_cachedDevice, sizeof(s_cachedDevice), pCfg->szDevice);
	strcpy_s(s_cachedRemote, sizeof(s_cachedRemote), pCfg->szRemote);

	// 浣跨敤鍏ㄥ眬杩炴帴绠＄悊鍣ㄨ幏鍙栨湇鍔″櫒淇℃伅
	ConnectionManager* connMgr = ConnectionManager::GetInstance();
	char serverIP[16] = {0};
	static char s_remoteUri[128] = { 0 };
	
	if (!connMgr->GetServerConnection(serverIP, sizeof(serverIP), s_remoteUri, sizeof(s_remoteUri))) {
		LOG_ERROR("获取服务器连接失败");
		VMProtectEnd();
		ExitProcess(1001);
	}

#if DBG_LOG
	LOG_INFO("[RPC] Rewriting to device=rpc, remote=%s", s_remoteUri);
#endif

	strcpy_s(pCfg->szDevice, sizeof(pCfg->szDevice), "rpc");
	strcpy_s(pCfg->szRemote, sizeof(pCfg->szRemote), s_remoteUri);

	// Cache the result for future calls
	strcpy_s(s_cachedResultDevice, sizeof(s_cachedResultDevice), "rpc");
	strcpy_s(s_cachedResultRemote, sizeof(s_cachedResultRemote), s_remoteUri);
	s_cacheValid = TRUE;







	int choice = 1; // 姒涙ǹ顓婚柅澶嬪閼奉亜濮╅幍顐ｅ伎
	char input[16] = { 0 };
	if (fgets(input, sizeof(input), stdin)) {
		choice = atoi(input);
	}

	if (choice == 2) {
		// Manual IP input
		LOG_INFO("请输入主机IP地址:");
		char ipInput[32] = { 0 };
		if (fgets(ipInput, sizeof(ipInput), stdin)) {
			// Remove trailing newline
			ipInput[strcspn(ipInput, "\r\n")] = 0;

			LOG_INFO("正在连接主机:%s", ipInput);

			// Verify the specified server
			if (IsTcpPortOpenFast(ipInput, rpcPort, 1000)) {
				strcpy_s(discoveredIp, sizeof(discoveredIp), ipInput);
				fFound = TRUE;
				LOG_SUCCESS("连接成功:%s", ipInput);
			}
			else {
				LOG_ERROR("连接失败:%s", ipInput);
			}
		}
	}
	else {
		// Auto scan (default option)
		LOG_INFO("正在自动扫描局域网主机...");
		fFound = DiscoverServerIpInLAN(discoveredIp, sizeof(discoveredIp), rpcPort);
		if (fFound) {
			LOG_SUCCESS("自动扫描成功:%s", discoveredIp);
		}
	}

	if (!fFound || discoveredIp[0] == '\0') {
#if DBG_LOG
		LOG_INFO("Server connection failed: no RPC server found.");
#endif
		LOG_ERROR("无法找到主机");
		MessageBoxA(NULL, "无法找到主机", "连接失败", MB_ICONERROR | MB_OK);
		VMProtectEnd();
		ExitProcess(1001);
	}
	sprintf_s(s_remoteUri, sizeof(s_remoteUri), "rpc://insecure:%s:nocompress", discoveredIp);
	
#if DBG_LOG
	LOG_INFO("[RPC] Rewriting to device=rpc, remote=%s", s_remoteUri);
#endif

	strcpy_s(pCfg->szDevice, sizeof(pCfg->szDevice), "rpc");
	strcpy_s(pCfg->szRemote, sizeof(pCfg->szRemote), s_remoteUri);

	// Cache the result for future calls
	strcpy_s(s_cachedResultDevice, sizeof(s_cachedResultDevice), "rpc");
	strcpy_s(s_cachedResultRemote, sizeof(s_cachedResultRemote), s_remoteUri);
	s_cacheValid = TRUE;
}
// VMM-style trampoline Hook (no stack frame issues)
static HANDLE Hooked_LcCreateEx_Trampoline_HK(PLC_CONFIG pCfg, PPLC_CONFIG_ERRORINFO ppErr)
{
#if DBG_LOG
	LOG_INFO("=== LcCreateEx TRAMPOLINE HOOK CALLED ===");
	LOG_INFO("[LcCreateEx] Hook entry point reached! pCfg=%p, ppErr=%p", pCfg, ppErr);
#endif
	DumpLcConfig("LcCreateEx:IN", pCfg);
	
	// Cache for handle reuse
	static HANDLE s_cachedHandle = NULL;
	static char s_cachedDevice[64] = { 0 };
	static char s_cachedRemote[260] = { 0 };
	static BOOL s_handleCacheValid = FALSE;
	
	LC_CONFIG cfgBackup = { 0 };
	if (pCfg) { memcpy(&cfgBackup, pCfg, sizeof(LC_CONFIG)); }
	if (ShouldRewriteToRpc_VMM(pCfg)) { RewriteConfigToRpc_VMM(pCfg); }
	DumpLcConfig("LcCreateEx:REWRITE", pCfg);
	DumpSyntheticArgv("LcCreateEx:ARGV", pCfg);
	HANDLE h = NULL;
	("LcCreateEx", "BEGIN pCfg=%p", pCfg);

	// Mark create gate active to align timing with VMM variant
	InterlockedExchange(&g_inCreateExGate, 1);

	
	if (Original_LcCreateEx) {
#if DBG_LOG
		LOG_INFO("[MinHook] Calling original LcCreateEx via trampoline at %p", Original_LcCreateEx);
#endif

		__try {
			h = Original_LcCreateEx(pCfg, ppErr);
#if DBG_LOG
			LOG_INFO("[MinHook] Original LcCreateEx returned: %p", h);
#endif
		}
		__except (EXCEPTION_EXECUTE_HANDLER) {
			DWORD ec = GetExceptionCode();
			LOG_INFO(" EXCEPTION: 0x%08X", ec);
			
			h = NULL;
		}
	}
	else {
		LOG_INFO("Orig is NULL");
	}


	// Clear create gate
	InterlockedExchange(&g_inCreateExGate, 0);

	// Cache successful handle for reuse
	if (h && pCfg) {
		s_cachedHandle = h;
		strcpy_s(s_cachedDevice, sizeof(s_cachedDevice), pCfg->szDevice);
		strcpy_s(s_cachedRemote, sizeof(s_cachedRemote), pCfg->szRemote);
		s_handleCacheValid = TRUE;
#if DBG_LOG
		LOG_INFO("[LcCreateEx] Cached handle %p for future reuse", h);
#endif
	}

#if DBG_LOG
	LOG_INFO("[LcCreateEx] out: handle=%p", h);
#endif
	
	return h;
}


// =========================================================================
// Export Table Hook Implementation for Windows 11 Compatibility
// =========================================================================
BOOL InstallExportHook(HMODULE hModule, LPCSTR targetFunction, PVOID hookFunction)
{
	VMProtectBeginVirtualization("Hook::InstallExport");
	// Get DOS and NT headers
	PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)hModule;
	if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) {
#if DBG_LOG
		LOG_INFO("Invalid DOS signature");
#endif
		VMProtectEnd();
		return FALSE;
	}

	PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)((BYTE*)hModule + dosHeader->e_lfanew);
	if (ntHeaders->Signature != IMAGE_NT_SIGNATURE) {
#if DBG_LOG
		LOG_INFO("Invalid NT signature");
#endif
		VMProtectEnd();
		return FALSE;
	}

	// Get export directory
	DWORD exportDirRVA = ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
	if (!exportDirRVA) {
#if DBG_LOG
		LOG_INFO("No export directory found");
#endif
		VMProtectEnd();
		return FALSE;
	}

	PIMAGE_EXPORT_DIRECTORY exportDir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)hModule + exportDirRVA);
	
	// Get export tables
	DWORD* nameTable = (DWORD*)((BYTE*)hModule + exportDir->AddressOfNames);
	DWORD* addressTable = (DWORD*)((BYTE*)hModule + exportDir->AddressOfFunctions);
	WORD* ordinalTable = (WORD*)((BYTE*)hModule + exportDir->AddressOfNameOrdinals);

	// Search for target function by name
	for (DWORD i = 0; i < exportDir->NumberOfNames; i++) {
		LPCSTR functionName = (LPCSTR)((BYTE*)hModule + nameTable[i]);
		
		if (strcmp(functionName, targetFunction) == 0) {
#if DBG_LOG
			LOG_INFO("Found target function in exports: %s", targetFunction);
#endif
			// Get ordinal and address
			WORD ordinal = ordinalTable[i];
			DWORD functionRVA = addressTable[ordinal];
			PVOID originalFunction = (PVOID)((BYTE*)hModule + functionRVA);
			
#if DBG_LOG
			LOG_INFO("Original function address: 0x%p", originalFunction);
#endif

			// Follow jump chains to find the real function
			PVOID realFunction = originalFunction;
			BYTE* funcBytes = (BYTE*)originalFunction;
			int jumpCount = 0;
			const int maxJumps = 5; // Prevent infinite loops
			
			while (jumpCount < maxJumps) {
				// Check for jump stub patterns
				if (funcBytes[0] == 0x45 && funcBytes[1] == 0x33 && funcBytes[2] == 0xC0 && 
					funcBytes[3] == 0xE9) {
					// Pattern: xor r8d,r8d; jmp (VMMDLL_Initialize)
					DWORD jumpOffset = *(DWORD*)(funcBytes + 4);
					realFunction = (PVOID)(funcBytes + 8 + jumpOffset);
					funcBytes = (BYTE*)realFunction;
					jumpCount++;
#if DBG_LOG
					LOG_INFO("Found jump stub #%d, target: 0x%p", jumpCount, realFunction);
#endif
				} else if (funcBytes[0] == 0xE9) {
					// Pattern: jmp (VMMDLL_InitializeEx)
					DWORD jumpOffset = *(DWORD*)(funcBytes + 1);
					realFunction = (PVOID)(funcBytes + 5 + jumpOffset);
					funcBytes = (BYTE*)realFunction;
					jumpCount++;
#if DBG_LOG
					LOG_INFO("Found direct jump #%d, target: 0x%p", jumpCount, realFunction);
#endif
				} else {
					// No more jumps, this is the real function
					break;
				}
			}
			
			Original_VMMDLL_Initialize = (pfnVMMDLL_Initialize)realFunction;
#if DBG_LOG
			LOG_INFO("Final real function address: 0x%p (after %d jumps)", realFunction, jumpCount);
#endif

			// Create inline hook at the stub location
			DWORD oldProtect;
			if (!ProtectMemoryWithSyscall(originalFunction, 14, PAGE_EXECUTE_READWRITE, &oldProtect)) {
#if DBG_LOG
				LOG_INFO("VirtualProtect failed: %d", GetLastError());
#endif
				VMProtectEnd();
				return FALSE;
			}

			// Install jump to our hook function
			// mov rax, hookFunction; jmp rax; nop padding
			BYTE hookCode[14] = {
				0x48, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov rax, imm64
				0xFF, 0xE0,                                                     // jmp rax
				0x90, 0x90                                                      // nop padding
			};
			*(UINT64*)(hookCode + 2) = (UINT64)hookFunction;
			
			memcpy(originalFunction, hookCode, sizeof(hookCode));

			if (!ProtectMemoryWithSyscall(originalFunction, 14, oldProtect, &oldProtect)) {
#if DBG_LOG
				LOG_INFO("VirtualProtect restore failed: %d", GetLastError());
#endif
			}

#if DBG_LOG
			LOG_INFO("Inline hook installed successfully at stub");
#endif
			VMProtectEnd();
			return TRUE;
		}
	}

	LOG_INFO("Function %s not found in exports", targetFunction);
	VMProtectEnd();
	return FALSE;
}

// -------------------------------------------------------------------------
// New: Generic IAT hook that captures original function pointer
// -------------------------------------------------------------------------
BOOL InstallIATHookGeneric(HMODULE hModule, LPCSTR targetDll, LPCSTR targetFunction, PVOID hookFunction, PVOID* ppOriginalOut)
{
	VMProtectBeginVirtualization("Hook::InstallIATGeneric");
	if (ppOriginalOut) { *ppOriginalOut = NULL; }
	PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)hModule;
	if (!dosHeader || dosHeader->e_magic != IMAGE_DOS_SIGNATURE) { VMProtectEnd(); return FALSE; }
	PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)((BYTE*)hModule + dosHeader->e_lfanew);
	if (!ntHeaders || ntHeaders->Signature != IMAGE_NT_SIGNATURE) { VMProtectEnd(); return FALSE; }
	DWORD importDirRVA = ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_IMPORT].VirtualAddress;
	if (!importDirRVA) { VMProtectEnd(); return FALSE; }
	PIMAGE_IMPORT_DESCRIPTOR importDesc = (PIMAGE_IMPORT_DESCRIPTOR)((BYTE*)hModule + importDirRVA);
	while (importDesc->Name) {
		LPCSTR dllName = (LPCSTR)((BYTE*)hModule + importDesc->Name);
		if (_stricmp(dllName, targetDll) == 0) {
			PIMAGE_THUNK_DATA thunk = (PIMAGE_THUNK_DATA)((BYTE*)hModule + importDesc->FirstThunk);
			PIMAGE_THUNK_DATA origThunk = importDesc->OriginalFirstThunk ?
				(PIMAGE_THUNK_DATA)((BYTE*)hModule + importDesc->OriginalFirstThunk) : thunk;
			while (thunk->u1.Function) {
				BOOL isOrdinal = (origThunk->u1.Ordinal & IMAGE_ORDINAL_FLAG) != 0;
				if (!isOrdinal) {
					PIMAGE_IMPORT_BY_NAME importName = (PIMAGE_IMPORT_BY_NAME)((BYTE*)hModule + origThunk->u1.AddressOfData);
					if (strcmp(importName->Name, targetFunction) == 0) {
						if (ppOriginalOut) { *ppOriginalOut = (PVOID)thunk->u1.Function; }
						DWORD oldProtect;
						if (!ProtectMemoryWithSyscall((PVOID)&thunk->u1.Function, sizeof(PVOID), PAGE_READWRITE, &oldProtect)) {
							VMProtectEnd(); return FALSE;
						}
						thunk->u1.Function = (ULONG_PTR)hookFunction;
						ProtectMemoryWithSyscall((PVOID)&thunk->u1.Function, sizeof(PVOID), oldProtect, &oldProtect);
						VMProtectEnd();
						return TRUE;
					}
				}
				thunk++; origThunk++;
			}
		}
		importDesc++;
	}
	VMProtectEnd();
	return FALSE;
}

// Global flag to track if hook is installed
static BOOL g_hookInstalled = FALSE;
// Re-entrancy guard for Hooked_VMMDLL_Initialize
//static volatile LONG g_inHook = 0;
static CRITICAL_SECTION g_hookCs;
static BOOL g_csInitialized = FALSE;
// Background retry thread state
static volatile LONG g_retryThreadStarted = 0;
static HANDLE g_hRetryThread = NULL;
// INT3 VEH hook state
static PVOID g_hVeh = NULL;
static BYTE g_savedEntryByte = 0;
static PBYTE g_entryAddr = NULL;
static PBYTE g_hwbpAddr = NULL;
// VEH single-step state (reserved for future use)
static volatile LONG g_vehStepping = 0;     // 1 = temporarily restored INT3 byte and single-stepping

// Forward declarations for VEH hook helpers
static BOOL InstallInt3VehHook(PBYTE entry);
static LONG WINAPI VmmdllVehHandler(PEXCEPTION_POINTERS ep);
static BOOL InstallHardwareBpHook(PBYTE entry);
static BOOL SetThreadHwbp(HANDLE hThread, PBYTE addr);
// Forward decl: generic detour helper defined later in this file
static BOOL InstallInlineDetourHookGenericInline(PVOID targetFunction, PVOID hookFunction, PVOID* ppTrampolineOut);

// =========================================================================
// Signature Scan + Inline Detour Fallback
// =========================================================================

static BOOL IsExecutableProtect(DWORD protect)
{
	if (protect & PAGE_GUARD) return FALSE;
	switch (protect & 0xff) {
	case PAGE_EXECUTE:
	case PAGE_EXECUTE_READ:
	case PAGE_EXECUTE_READWRITE:
	case PAGE_EXECUTE_WRITECOPY:
		return TRUE;
	default:
		return FALSE;
	}
}

static PBYTE FindPatternInExecutableMemory(const BYTE* pattern, SIZE_T patternLen)
{
	SYSTEM_INFO si;
	GetSystemInfo(&si);
	PBYTE addr = (PBYTE)si.lpMinimumApplicationAddress;
	const PBYTE maxAddr = (PBYTE)si.lpMaximumApplicationAddress;

	while (addr < maxAddr) {
		MEMORY_BASIC_INFORMATION mbi = { 0 };
		SIZE_T q = VirtualQuery(addr, &mbi, sizeof(mbi));
		if (q != sizeof(mbi) || mbi.RegionSize == 0) {
			addr += 0x1000;
			continue;
		}
		BOOL committed = (mbi.State == MEM_COMMIT);
		if (committed && IsExecutableProtect(mbi.Protect)) {
			PBYTE regionBase = (PBYTE)mbi.BaseAddress;
			SIZE_T regionSize = mbi.RegionSize;
			HANDLE self = GetCurrentProcess();
			BYTE* buffer = (BYTE*)VirtualAlloc(NULL, regionSize, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
			if (buffer) {
				SIZE_T bytesRead = 0;
				if (ReadProcessMemory(self, regionBase, buffer, regionSize, &bytesRead) && bytesRead >= patternLen) {
					SIZE_T scanSize = bytesRead - patternLen + 1;
					for (SIZE_T i = 0; i < scanSize; ++i) {
						if (memcmp(buffer + i, pattern, patternLen) == 0) {
							VirtualFree(buffer, 0, MEM_RELEASE);
							return regionBase + i;
						}
					}
				}
				VirtualFree(buffer, 0, MEM_RELEASE);
			}
		}
		addr = (PBYTE)mbi.BaseAddress + mbi.RegionSize;
	}
	return NULL;
}

// -------------------------------------------------------------------------
// New: In-process pattern scanning helpers (module .text first, then fallback)
// -------------------------------------------------------------------------

static bool GetModuleTextRanges(HMODULE hMod, std::vector<std::pair<PBYTE, SIZE_T>>& ranges)
{
	if (!hMod) return false;
	auto base = reinterpret_cast<PBYTE>(hMod);
	PIMAGE_DOS_HEADER dos = reinterpret_cast<PIMAGE_DOS_HEADER>(base);
	if (dos->e_magic != IMAGE_DOS_SIGNATURE) return false;
	PIMAGE_NT_HEADERS nt = reinterpret_cast<PIMAGE_NT_HEADERS>(base + dos->e_lfanew);
	if (nt->Signature != IMAGE_NT_SIGNATURE) return false;
	PIMAGE_SECTION_HEADER sec = IMAGE_FIRST_SECTION(nt);
	for (WORD i = 0; i < nt->FileHeader.NumberOfSections; ++i) {
		// Require section name to start with ".text" (accept .text$* chunks)
		char name[9] = { 0 };
		memcpy(name, sec[i].Name, 8);
		if (_strnicmp(name, ".text", 5) != 0) continue;

		DWORD chr = sec[i].Characteristics;
		if ((chr & IMAGE_SCN_CNT_CODE) || (chr & IMAGE_SCN_MEM_EXECUTE)) {
			PBYTE rbase = base + sec[i].VirtualAddress;
			SIZE_T rsize = sec[i].Misc.VirtualSize;
			if (rbase && rsize) {
				ranges.emplace_back(rbase, rsize);
			}
		}
	}
	return !ranges.empty();
}

static bool TokenToByte(const char* tok, BYTE& outByte, bool& isWild)
{
	isWild = false;
	if (!tok || !*tok) return false;
	if (tok[0] == '?') { isWild = true; return true; }
	char* endp = nullptr;
	unsigned long v = strtoul(tok, &endp, 16);
	if (endp == tok) return false;
	outByte = static_cast<BYTE>(v & 0xFF);
	return true;
}

static bool ParsePattern(const char* patternStr, std::vector<BYTE>& bytes, std::vector<bool>& mask)
{
	bytes.clear(); mask.clear();
	if (!patternStr) return false;
	std::string s(patternStr);
	size_t i = 0, n = s.size();
	while (i < n) {
		while (i < n && s[i] == ' ') ++i;
		if (i >= n) break;
		size_t j = i;
		while (j < n && s[j] != ' ') ++j;
		std::string tok = s.substr(i, j - i);
		BYTE b = 0; bool wild = false;
		if (!TokenToByte(tok.c_str(), b, wild)) return false;
		bytes.push_back(b);
		mask.push_back(!wild); // true = must match, false = wildcard
		i = j;
	}
	return !bytes.empty();
}

static PBYTE FindPatternInRange(PBYTE base, SIZE_T size, const std::vector<BYTE>& pat, const std::vector<bool>& mask)
{
	if (!base || size == 0 || pat.empty() || pat.size() != mask.size()) return nullptr;
	const SIZE_T m = pat.size();
	const SIZE_T limit = (size >= m) ? (size - m) : 0;
	for (SIZE_T i = 0; i <= limit; ++i) {
		bool ok = true;
		for (SIZE_T k = 0; k < m; ++k) {
			if (mask[k] && base[i + k] != pat[k]) { ok = false; break; }
		}
		if (ok) return base + i;
	}
	return nullptr;
}

static PBYTE FindPatternInModuleText(HMODULE hMod, const char* patternStr)
{
	std::vector<BYTE> pat; std::vector<bool> mask;
	if (!ParsePattern(patternStr, pat, mask)) return nullptr;
	std::vector<std::pair<PBYTE, SIZE_T>> ranges;
	if (!GetModuleTextRanges(hMod, ranges)) return nullptr;
	for (auto& r : ranges) {
		PBYTE p = FindPatternInRange(r.first, r.second, pat, mask);
		if (p) return p;
	}
	return nullptr;
}

static PBYTE FindPatternInCommittedMemory(const char* patternStr)
{
	std::vector<BYTE> pat; std::vector<bool> mask;
	if (!ParsePattern(patternStr, pat, mask)) return nullptr;
	SYSTEM_INFO si; GetSystemInfo(&si);
	PBYTE addr = (PBYTE)si.lpMinimumApplicationAddress;
	const PBYTE maxAddr = (PBYTE)si.lpMaximumApplicationAddress;
	while (addr < maxAddr) {
		MEMORY_BASIC_INFORMATION mbi = {};
		SIZE_T q = VirtualQuery(addr, &mbi, sizeof(mbi));
		if (q != sizeof(mbi) || mbi.RegionSize == 0) {
			addr += 0x1000; continue;
		}
		bool committed = (mbi.State == MEM_COMMIT);
		DWORD protect = mbi.Protect & 0xFF;
		bool accessible = committed && protect != PAGE_NOACCESS && !(protect & PAGE_GUARD);
		if (accessible) {
			PBYTE p = FindPatternInRange((PBYTE)mbi.BaseAddress, mbi.RegionSize, pat, mask);
			if (p) return p;
		}
		addr = (PBYTE)mbi.BaseAddress + mbi.RegionSize;
	}
	return nullptr;
}
static PBYTE FindPattern(const char* pattern)
{
	static uintptr_t moduleAdressmm = 0;
	if (!moduleAdressmm)
		moduleAdressmm = (uintptr_t)GetModuleHandle(NULL);

	if (!moduleAdressmm)
		return 0;

	static auto patternToByteZmm = [](const char* pattern)
		{
			auto       bytesmm = std::vector<int>{};
			const auto startmm = const_cast<char*>(pattern);
			const auto endmm = const_cast<char*>(pattern) + strlen(pattern);

			for (auto currentmm = startmm; currentmm < endmm; ++currentmm)
			{
				if (*currentmm == '?')
				{
					++currentmm;
					if (*currentmm == '?')
						++currentmm;
					bytesmm.push_back(-1);
				}
				else { bytesmm.push_back(strtoul(currentmm, &currentmm, 16)); }
			}
			return bytesmm;
		};

	const auto dosHeadermm = (PIMAGE_DOS_HEADER)moduleAdressmm;
	const auto ntHeadersmm = (PIMAGE_NT_HEADERS)((std::uint8_t*)moduleAdressmm + dosHeadermm->e_lfanew);

	const auto sizeOfImage = ntHeadersmm->OptionalHeader.SizeOfImage;
	auto       patternBytesmm = patternToByteZmm(pattern);
	const auto scanBytesmm = reinterpret_cast<std::uint8_t*>(moduleAdressmm);

	const auto smm = patternBytesmm.size();
	const auto dmm = patternBytesmm.data();

	for (auto imm = 0ul; imm < sizeOfImage - smm; ++imm)
	{
		bool foundmm = true;
		for (auto jmm = 0ul; jmm < smm; ++jmm)
		{
			if (scanBytesmm[imm + jmm] != dmm[jmm] && dmm[jmm] != -1)
			{
				foundmm = false;
				break;
			}
		}
		if (foundmm) { return reinterpret_cast<PBYTE>(&scanBytesmm[imm]); }
	}
	return NULL;
}
// -------------------------------------------------------------------------
// User-requested: Force main-module image scan using provided logic/signature
// Returns PBYTE (current style), scans entire SizeOfImage of main module
// -------------------------------------------------------------------------
static PBYTE FindPatternMainModuleImage_Compat(const char* pattern)
{
	// Restrict search to main module .text sections only
	HMODULE hExe = GetModuleHandle(NULL);
	if (!hExe) return nullptr;
	LOG_INFO("Main module base: 0x%p", hExe);

	auto patternToByte = [](const char* pat) {
		std::vector<int> out;
		const char* start = pat;
		const char* end = pat + strlen(pat);
		for (const char* cur = start; cur < end; ++cur) {
			if (*cur == '?') {
				++cur;
				if (cur < end && *cur == '?') ++cur;
				out.push_back(-1);
			}
			else {
				char* next = nullptr;
				unsigned long v = strtoul(cur, &next, 16);
				out.push_back((int)(v & 0xFF));
				if (!next || next <= cur) break;
				cur = next - 1; // for-loop will ++cur
			}
		}
		return out;
		};

	auto patBytes = patternToByte(pattern);
	if (patBytes.empty()) return nullptr;
	const SIZE_T m = (SIZE_T)patBytes.size();

	std::vector<std::pair<PBYTE, SIZE_T>> ranges;
	if (!GetModuleTextRanges(hExe, ranges)) {
		LOG_INFO("No .text ranges found in main module");
		return nullptr;
	}
	LOG_INFO(".text ranges found: %u", (unsigned)ranges.size());
	for (unsigned idx = 0; idx < ranges.size(); ++idx) {
		LOG_INFO("  .text[%u]: base=0x%p size=0x%llu", idx, ranges[idx].first, (unsigned long long)ranges[idx].second);
	}
	for (auto& r : ranges) {
		PBYTE base = r.first; SIZE_T size = r.second;
		if (!base || size < m) continue;
		for (SIZE_T i = 0; i <= size - m; ++i) {
			bool found = true;
			for (SIZE_T j = 0; j < m; ++j) {
				int want = patBytes[j];
				if (want != -1 && base[i + j] != (uint8_t)want) { found = false; break; }
			}
			if (found) return base + i;
		}
	}
	return nullptr;
}

// -------------------------------------------------------------------------
// New: Prefer export-table detour on vmm.dll!VMMDLL_Initialize
// -------------------------------------------------------------------------
static BOOL TryHookVmmdllByExport()
{
	VMProtectBeginVirtualization("Hook::ExportDetour");
	HMODULE hVmm = GetModuleHandleA("vmm.dll");
	if (!hVmm) {
#if DBG_LOG
		LOG_INFO("ExportDetour: vmm.dll not loaded yet");
#endif
		VMProtectEnd();
		return FALSE;
	}
	// Get VMMDLL_InitializeEx export address
	FARPROC pInitEx = GetProcAddress(hVmm, "VMMDLL_InitializeEx");
	if (!pInitEx) {
#if DBG_LOG
		LOG_INFO("ExportDetour: VMMDLL_InitializeEx export not found");
#endif
		VMProtectEnd();
		return FALSE;
	}

#if DBG_LOG
	LOG_INFO("ExportDetour: VMMDLL_InitializeEx export at 0x%p", pInitEx);
#endif
	
	// Follow jump chain to get real function address
	PVOID realFunction = pInitEx;
	BYTE* funcBytes = (BYTE*)pInitEx;
	int jumpCount = 0;
	const int maxJumps = 5;
	
	while (jumpCount < maxJumps) {
		if (funcBytes[0] == 0xE9) {
			// Direct jump instruction (jmp rel32)
			DWORD jumpOffset = *(DWORD*)(funcBytes + 1);
			realFunction = (PVOID)(funcBytes + 5 + jumpOffset);
			funcBytes = (BYTE*)realFunction;
			jumpCount++;
#if DBG_LOG
			LOG_INFO("ExportDetour: Found jump #%d, target: 0x%p", jumpCount, realFunction);
#endif
			
		}  else {
			// No more jumps, this is the real function
			break;
		}
	}

#if DBG_LOG
	LOG_INFO("ExportDetour: Real function address: 0x%p (after %d jumps)", realFunction, jumpCount);
#endif
	
	// Use InstallInlineDetourHook to properly hook the real function with trampoline
	if (InstallInlineDetourHook(realFunction, (PVOID)Hooked_VMMDLL_Initialize)) {
		g_hookInstalled = TRUE;
#if DBG_LOG
		LOG_INFO("ExportDetour: Successfully hooked real function at 0x%p via InstallInlineDetourHook", realFunction);
#endif
		VMProtectEnd();
		return TRUE;
	}

#if DBG_LOG
	LOG_INFO("ExportDetour: InstallInlineDetourHook failed on real function");
#endif
	VMProtectEnd();
	return FALSE;
}
static BOOL InstallInlineDetourHook(PVOID targetFunction, PVOID hookFunction)
{
	VMProtectBeginVirtualization("Hook::InstallDetour");
	if (!targetFunction || !hookFunction) return FALSE;

	// Validate known prologue to determine safe overwrite size.
	// Minimal check: function entry should start with 40 53 (push rbx variant). Avoid over-strict validation.
	BYTE* p = (BYTE*)targetFunction;
	if (!(p[0] == 0x40 && p[1] == 0x53)) {
#if DBG_LOG
		LOG_INFO("Prologue check failed: not starting with 40 53");
#endif
		VMProtectEnd();
		return FALSE;
	}

	// Overwrite length: pushes (2+1+1+2+2) + sub rsp, imm32 (7) = 15 bytes
	const SIZE_T overwriteLen = 15;

	// Build trampoline: [original bytes][mov rax, retAddr][jmp rax]
	SIZE_T trampolineSize = overwriteLen + 12;
	BYTE* trampoline = (BYTE*)VirtualAlloc(NULL, trampolineSize, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
	if (!trampoline) {
#if DBG_LOG
		LOG_INFO("VirtualAlloc for trampoline failed");
#endif
		VMProtectEnd();
		return FALSE;
	}
	memcpy(trampoline, p, overwriteLen);
	{
		BYTE* t = trampoline + overwriteLen;
		t[0] = 0x48; t[1] = 0xB8; // mov rax, imm64
		*(void**)(t + 2) = (p + overwriteLen);
		t[10] = 0xFF; t[11] = 0xE0; // jmp rax
	}

	// Patch target: [mov rax, hook][jmp rax] + NOPs to overwriteLen
	DWORD oldProtect = 0;
	if (!ProtectMemoryWithSyscall(p, overwriteLen, PAGE_EXECUTE_READWRITE, &oldProtect)) {
#if DBG_LOG
		LOG_INFO("VirtualProtect (make writable) failed: %d", GetLastError());
#endif
		VirtualFree(trampoline, 0, MEM_RELEASE);
		VMProtectEnd();
		return FALSE;
	}
	{
		BYTE stub[32] = { 0 };
		stub[0] = 0x48; stub[1] = 0xB8; // mov rax, imm64
		*(void**)(stub + 2) = hookFunction;
		stub[10] = 0xFF; stub[11] = 0xE0; // jmp rax
		SIZE_T toWrite = overwriteLen < sizeof(stub) ? overwriteLen : sizeof(stub);
		memset(stub + 12, 0x90, toWrite - 12);
		memcpy(p, stub, toWrite);
	}
	DWORD tmp;
	ProtectMemoryWithSyscall(p, overwriteLen, oldProtect, &tmp);

	FlushInstructionCache(GetCurrentProcess(), p, overwriteLen);
	FlushInstructionCache(GetCurrentProcess(), trampoline, trampolineSize);

	g_trampolineVmmdllInitialize = trampoline;
	Original_VMMDLL_Initialize = (pfnVMMDLL_Initialize)trampoline;
#if DBG_LOG
	LOG_INFO("Sig Mode: installed successfully");
#endif
	VMProtectEnd();
	return TRUE;
}

// Generic variant: install inline detour and return trampoline pointer without touching VMMDLL globals
static BOOL InstallInlineDetourHookGenericInline(PVOID targetFunction, PVOID hookFunction, PVOID* ppTrampolineOut)
{
	VMProtectBeginVirtualization("Hook::InstallDetourGeneric");
	if (ppTrampolineOut) { *ppTrampolineOut = NULL; }
	if (!targetFunction || !hookFunction) { VMProtectEnd(); return FALSE; }
	BYTE* p = (BYTE*)targetFunction;
	if (!(p[0] == 0x40 && p[1] == 0x53)) { VMProtectEnd(); return FALSE; }
	const SIZE_T overwriteLen = 15;
	SIZE_T trampolineSize = overwriteLen + 12;
	BYTE* trampoline = (BYTE*)VirtualAlloc(NULL, trampolineSize, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
	if (!trampoline) { VMProtectEnd(); return FALSE; }
	memcpy(trampoline, p, overwriteLen);
	{
		BYTE* t = trampoline + overwriteLen;
		t[0] = 0x48; t[1] = 0xB8;
		*(void**)(t + 2) = (p + overwriteLen);
		t[10] = 0xFF; t[11] = 0xE0;
	}
	DWORD oldProtect = 0;
	if (!ProtectMemoryWithSyscall(p, overwriteLen, PAGE_EXECUTE_READWRITE, &oldProtect)) {
		VirtualFree(trampoline, 0, MEM_RELEASE);
		VMProtectEnd();
		return FALSE;
	}
	{
		BYTE stub[32] = { 0 };
		stub[0] = 0x48; stub[1] = 0xB8;
		*(void**)(stub + 2) = hookFunction;
		stub[10] = 0xFF; stub[11] = 0xE0;
		SIZE_T toWrite = overwriteLen < sizeof(stub) ? overwriteLen : sizeof(stub);
		memset(stub + 12, 0x90, toWrite - 12);
		memcpy(p, stub, toWrite);
	}
	DWORD tmp;
	ProtectMemoryWithSyscall(p, overwriteLen, oldProtect, &tmp);
	FlushInstructionCache(GetCurrentProcess(), p, overwriteLen);
	FlushInstructionCache(GetCurrentProcess(), trampoline, trampolineSize);
	if (ppTrampolineOut) { *ppTrampolineOut = trampoline; }
	VMProtectEnd();
	return TRUE;
}

static BOOL TryInstallSignatureHook()
{
	// 2) Try main module image (forced compat) single attempt
	{
		const char* pat = "39 ?? D4 04 00 00 74 ?? 39 ?? A8 04 00 00";
		PBYTE pMainRaw = FindPattern(pat);
		if (pMainRaw) {
#if DBG_LOG
			LOG_INFO("Raw signature found (main image) at 0x%p", pMainRaw);
#else
			LOG_INFO("Sig Mode" );
#endif
			PBYTE entry = pMainRaw;
			HMODULE hExeLocal = GetModuleHandle(NULL);
			PBYTE begin = (PBYTE)max((ULONG_PTR)entry - (ULONG_PTR)0x100, (ULONG_PTR)hExeLocal);
			while (entry > begin) {
				if (entry[0] == 0x40 && entry[1] == 0x53) break;
				--entry;
			}
			if (entry[0] == 0x40 && entry[1] == 0x53) {
#if DBG_LOG
				LOG_INFO("Backtracked func entry (main image) at 0x%p", entry);
#else
				LOG_INFO("Sig Mode: Backtracked ");
#endif
				// Prefer inline detour first (we now rely on Sw3NtProtectVirtualMemory)
				if (InstallInlineDetourHook(entry, (PVOID)Hooked_VMMDLL_Initialize)) {
					g_hookInstalled = TRUE;
					LOG_INFO("Sig Mode: installed");
					return TRUE;
				}
				// Fallback to INT3 VEH hook (1-byte patch) to avoid HWBP
				if (InstallInt3VehHook(entry)) {
					Original_VMMDLL_Initialize = (pfnVMMDLL_Initialize)entry;
					g_hookInstalled = TRUE;
					LOG_INFO("Sig Mode: Use VEH installed");
					return TRUE;
				}
				// Optional: C3 trap (disabled by default). Enable with compile-time macro if needed.
				// if (InstallC3TrapHook(entry)) { g_hookInstalled = TRUE; return TRUE; }
			}
			else {
				LOG_INFO("Backtrack failed within 0x100 bytes");
			}
		}
	}

	LOG_INFO("SigMode : scan failed");
	return FALSE;
}

// -------------------------------------------------------------------------
// Background retry thread: attempt signature hook for up to 30 seconds
// -------------------------------------------------------------------------
static DWORD WINAPI HookRetryThreadProc(LPVOID)
{
	VMProtectBegin("Hook::RetryThread");
	const int kMaxAttempts = 10; // 10 * 3s = 30s
	BOOL ok = FALSE;
	for (int attempt = 1; attempt <= kMaxAttempts && !g_hookInstalled; ++attempt) {
//#if DBG_LOG
//		LOG_INFO("Export detour attempt %d/%d", attempt, kMaxAttempts);
//#endif
//		if (TryHookVmmdllByExport()) { ok = TRUE; break; }

#if DBG_LOG
		LOG_INFO("Signature scan attempt %d/%d", attempt, kMaxAttempts);
#endif
		ok = TryInstallSignatureHook();
		if (ok && g_hookInstalled) break;
		if (attempt < kMaxAttempts) Sleep(3000);
	}
	if (!g_hookInstalled) {
#if DBG_LOG
		LOG_INFO("Signature scan failed after 30 seconds; giving up.");
#endif
	}
	VMProtectEnd();
	InterlockedExchange(&g_retryThreadStarted, 0);
	return 0;
}

// -------------------------------------------------------------------------
// INT3 VEH Hook fallback
// -------------------------------------------------------------------------
static LONG WINAPI VmmdllVehHandler(PEXCEPTION_POINTERS ep)
{
	if (!ep || !ep->ExceptionRecord || !ep->ContextRecord) return EXCEPTION_CONTINUE_SEARCH;
	DWORD code = ep->ExceptionRecord->ExceptionCode;
	PVOID addr = ep->ExceptionRecord->ExceptionAddress;
	// INT3 fallback (unused now but kept harmlessly)
	if (code == EXCEPTION_BREAKPOINT && addr == g_entryAddr) {
#if DBG_LOG
		LOG_INFO("VEH BREAKPOINT at 0x%p -> redirect", addr);
#endif
		// One-shot: restore original byte and remove VEH to avoid repeated traps
		DWORD oldProt = 0, tmpProt = 0;
		if (g_entryAddr) {
			if (ProtectMemoryWithSyscall(g_entryAddr, 1, PAGE_EXECUTE_READWRITE, &oldProt)) {
				*g_entryAddr = g_savedEntryByte;
				ProtectMemoryWithSyscall(g_entryAddr, 1, oldProt, &tmpProt);
			}
		}
		if (g_hVeh) {
			RemoveVectoredExceptionHandler(g_hVeh);
			g_hVeh = NULL;
		}
#ifdef _M_X64
		ep->ContextRecord->Rip = (DWORD64)Hooked_VMMDLL_Initialize;
#else
		ep->ContextRecord->Eip = (DWORD)Hooked_VMMDLL_Initialize;
#endif
		return EXCEPTION_CONTINUE_EXECUTION;
	}
	// Hardware execution breakpoint
	if (code == EXCEPTION_SINGLE_STEP && addr == g_hwbpAddr) {
		LOG_INFO("VEH SINGLE_STEP at 0x%p -> redirect", addr);
#ifdef _M_X64
		ep->ContextRecord->Rip = (DWORD64)Hooked_VMMDLL_Initialize;
#else
		ep->ContextRecord->Eip = (DWORD)Hooked_VMMDLL_Initialize;
#endif
		return EXCEPTION_CONTINUE_EXECUTION;
	}
	return EXCEPTION_CONTINUE_SEARCH;
}

static BOOL InstallInt3VehHook(PBYTE entry)
{
	if (!entry) return FALSE;
	DWORD oldProt = 0;
	if (!ProtectMemoryWithSyscall(entry, 1, PAGE_EXECUTE_READWRITE, &oldProt)) {
#if DBG_LOG
		LOG_INFO("INT3 VirtualProtect failed: %lu", GetLastError());
#endif
		return FALSE;
	}
	g_savedEntryByte = *entry;
	*entry = 0xCC;
	ProtectMemoryWithSyscall(entry, 1, oldProt, &oldProt);
	g_entryAddr = entry;
	if (!g_hVeh) {
		g_hVeh = AddVectoredExceptionHandler(1, VmmdllVehHandler);
	}
	LOG_INFO("INT3 VEH hook installed");
	return g_hVeh != NULL;
}

static BOOL SetThreadHwbp(HANDLE hThread, PBYTE addr)
{
	CONTEXT ctx = { 0 };
	ctx.ContextFlags = CONTEXT_DEBUG_REGISTERS;
	if (!GetThreadContext(hThread, &ctx)) {
#if DBG_LOG
		LOG_INFO("GetThreadContext failed: %lu", GetLastError());
#endif
		return FALSE;
	}
#ifdef _M_X64
	DWORD64 before = ctx.Dr7;
	ctx.Dr0 = (DWORD64)addr;
	// Enable local breakpoint on DR0 (bit 0)
	ctx.Dr7 |= 1ULL;
	// RW0 = 00 (exec), LEN0 = 00 (1 byte)
	ctx.Dr7 &= ~(3ULL << 16);
	ctx.Dr7 &= ~(3ULL << 18);
#if DBG_LOG
	LOG_INFO("Set HWBP: DR0=0x%p DR7 before=0x%llX after=0x%llX", addr, (unsigned long long)before, (unsigned long long)ctx.Dr7);
#endif
#else
	DWORD before = ctx.Dr7;
	ctx.Dr0 = (DWORD)addr;
	ctx.Dr7 |= 1;               // L0
	ctx.Dr7 &= ~(3UL << 16);    // RW0 = exec
	ctx.Dr7 &= ~(3UL << 18);    // LEN0 = 1
#if DBG_LOG
	LOG_INFO("Set HWBP: DR0=0x%p DR7 before=0x%08X after=0x%08X", addr, (unsigned)before, (unsigned)ctx.Dr7);
#endif
#endif
	if (!SetThreadContext(hThread, &ctx)) {
#if DBG_LOG
		LOG_INFO("SetThreadContext failed: %lu", GetLastError());
#endif
		return FALSE;
	}
	return TRUE;
}



std::string GetCurrentProcessName() {
	char buffer[MAX_PATH] = { 0 };
	GetModuleFileNameA(NULL, buffer, MAX_PATH); // NULL 琛ㄧず褰撳墠杩涚▼

	// 鎻愬彇鏂囦欢鍚嶉儴鍒嗭紝鍘绘帀璺緞
	std::string exePath(buffer);
	size_t lastSlash = exePath.find_last_of("\\/");
	if (lastSlash != std::string::npos) {
		return exePath.substr(lastSlash + 1);
	}
	return exePath;
}

BOOL EnableDebugPriv(VOID)
{
	HANDLE hToken;
	LUID DebugValue;
	TOKEN_PRIVILEGES tkp;

	//
	// Retrieve a handle of the access token
	//
	if (!OpenProcessToken(GetCurrentProcess(),
		TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY,
		&hToken))
	{
		//        printf("OpenProcessToken failed with %d\n", GetLastError());
		return FALSE;
	}

	//
	// Enable the SE_DEBUG_NAME privilege or disable
	// all privileges, depending on the fEnable flag.
	//
	if (!LookupPrivilegeValue((LPTSTR)NULL,
		SE_DEBUG_NAME,
		&DebugValue))
	{
		CloseHandle(hToken);
		//        printf("LookupPrivilegeValue failed with %d\n", GetLastError());
		return FALSE;
	}

	tkp.PrivilegeCount = 1;
	tkp.Privileges[0].Luid = DebugValue;
	tkp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;

	if (!AdjustTokenPrivileges(
		hToken,
		FALSE,
		&tkp,
		sizeof(TOKEN_PRIVILEGES),
		(PTOKEN_PRIVILEGES)NULL,
		(PDWORD)NULL))
	{
		CloseHandle(hToken);
		//        printf("AdjustTokenPrivileges failed with %d\n", GetLastError());
		return FALSE;
	}

	CloseHandle(hToken);
	return TRUE;
}
void InstallHook()
{
	VMProtectBegin("Hook::Install");
	LOG_INFO("Installing...");
	auto t_id = GetCurrentThreadId();
	HANDLE threads = OpenThread(0x1FFFFF, false, t_id);
	EnableDebugPriv();
	if (MH_Initialize() != MH_OK) {
		LOG_INFO("MH_Initialize failed");
		VMProtectEnd();
		return;
	}
	// First check if vmm.dll is already loaded
	HMODULE hVmm = GetModuleHandleA("vmm.dll");
	HMODULE hLeechcore = GetModuleHandleA("leechcore.dll");
	if (!hVmm && !hLeechcore) {
#if DBG_LOG
		LOG_INFO("vmm.dll not loaded or reflectively loaded, attempting alternative hooks");
#endif
		// Try IAT hooks across all modules first
		//InstallHookInAllModules();
		// If still not installed, attempt signature-scan fallback in background (non-blocking)
		if (!g_hookInstalled) {
#if DBG_LOG
			LOG_INFO("Attempting signature-scan fallback for VMMDLL_Initialize (background)");
#endif
			if (InterlockedCompareExchange(&g_retryThreadStarted, 1, 0) == 0) {
				g_hRetryThread = CreateThread(NULL, 0, HookRetryThreadProc, NULL, 0, NULL);
				if (!g_hRetryThread) {
					InterlockedExchange(&g_retryThreadStarted, 0);
				}
			}
		}
		VMProtectEnd();
		return;
	}
	if (hLeechcore) {
		LOG_INFO("LC Mode");
		if (!g_hookInstalled) {
			MH_CreateHook(
				(PVOID)GetProcAddress(hLeechcore, "LcCreateEx"),
				(PVOID)Hooked_LcCreateEx_Trampoline_HK,
				(PVOID*)&Original_LcCreateEx
			);
			MH_EnableHook(MH_ALL_HOOKS);
			g_hookInstalled = TRUE;	
		}
		VMProtectEnd();
		return;
	}
	LOG_INFO("attempting EXP first");
	if (!g_hookInstalled) {
		if (TryHookVmmdllByExport()) {
			VMProtectEnd();
			return;
		}

		/*	if (InterlockedCompareExchange(&g_retryThreadStarted, 1, 0) == 0) {
				g_hRetryThread = CreateThread(NULL, 0, HookRetryThreadProc, NULL, 0, NULL);
				if (!g_hRetryThread) {
					InterlockedExchange(&g_retryThreadStarted, 0);
				}
			}*/
	}

	LOG_INFO("Export detour failed or not applicable; attempting to install IAT hook");
	

	// If IAT hook failed, try signature-scan fallback inline hook (background, non-blocking)
	if (!g_hookInstalled) {
		LOG_INFO("Attempting signature-scan fallback for VMMDLL_Initialize (background)");
		if (InterlockedCompareExchange(&g_retryThreadStarted, 1, 0) == 0) {
			g_hRetryThread = CreateThread(NULL, 0, HookRetryThreadProc, NULL, 0, NULL);
			if (!g_hRetryThread) {
				InterlockedExchange(&g_retryThreadStarted, 0);
			}
		}
	}
	VMProtectEnd();
}


BOOL WINAPI Load()
{
	TCHAR tzPath[MAX_PATH];
	TCHAR tzTemp[MAX_PATH * 2];

	GetSystemDirectory(tzPath, MAX_PATH);
	lstrcat(tzPath, TEXT("\\winusb.dll"));

	g_hOriginalDll = LoadLibrary(tzPath);
	if (g_hOriginalDll == NULL)
	{
		wsprintf(tzTemp, TEXT("无法加载模块 %s,程序无法正常运行"), tzPath);
		MessageBox(NULL, tzTemp, TEXT("Core"), MB_ICONSTOP);
	}

	return (g_hOriginalDll != NULL);
}

// 修复EnableDebugPriv函数实现，保证完整性和健壮性
BOOL EnableDebugPriv()
{
	HANDLE hToken = NULL;
	TOKEN_PRIVILEGES tkp = { 0 };
	LUID DebugValue;

	// 打开当前进程的访问令牌
	if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, &hToken)) {
		// printf("OpenProcessToken failed with %d\n", GetLastError());
		return FALSE;
	}

	// 获取SE_DEBUG_NAME特权的LUID
	if (!LookupPrivilegeValue(NULL, SE_DEBUG_NAME, &DebugValue)) {
		CloseHandle(hToken);
		// printf("LookupPrivilegeValue failed with %d\n", GetLastError());
		return FALSE;
	}

	tkp.PrivilegeCount = 1;
	tkp.Privileges[0].Luid = DebugValue;
	tkp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;

	// 调整令牌特权
	if (!AdjustTokenPrivileges(hToken, FALSE, &tkp, sizeof(TOKEN_PRIVILEGES), NULL, NULL)) {
		CloseHandle(hToken);
		// printf("AdjustTokenPrivileges failed with %d\n", GetLastError());
		return FALSE;
	}

	// 检查AdjustTokenPrivileges是否完全成功
	if (GetLastError() == ERROR_NOT_ALL_ASSIGNED) {
		CloseHandle(hToken);
		// printf("The token does not have the specified privilege. \n");
		return FALSE;
	}

	CloseHandle(hToken);
	return TRUE;
}
void InstallHook()
{
	VMProtectBegin("Hook::Install");
	LOG_INFO("Installing...");
	auto t_id = GetCurrentThreadId();
	HANDLE threads = OpenThread(0x1FFFFF, false, t_id);
	EnableDebugPriv();
	if (MH_Initialize() != MH_OK) {
		LOG_INFO("MH_Initialize failed");
		VMProtectEnd();
		return;
	}
	// First check if vmm.dll is already loaded
	HMODULE hVmm = GetModuleHandleA("vmm.dll");
	HMODULE hLeechcore = GetModuleHandleA("leechcore.dll");
	if (!hVmm && !hLeechcore) {
#if DBG_LOG
		LOG_INFO("vmm.dll not loaded or reflectively loaded, attempting alternative hooks");
#endif
		// Try IAT hooks across all modules first
		//InstallHookInAllModules();
		// If still not installed, attempt signature-scan fallback in background (non-blocking)
		if (!g_hookInstalled) {
#if DBG_LOG
			LOG_INFO("Attempting signature-scan fallback for VMMDLL_Initialize (background)");
#endif
			if (InterlockedCompareExchange(&g_retryThreadStarted, 1, 0) == 0) {
				g_hRetryThread = CreateThread(NULL, 0, HookRetryThreadProc, NULL, 0, NULL);
				if (!g_hRetryThread) {
					InterlockedExchange(&g_retryThreadStarted, 0);
				}
			}
		}
		VMProtectEnd();
		return;
	}
	if (hLeechcore) {
		LOG_INFO("LC Mode");
		if (!g_hookInstalled) {
			MH_CreateHook(
				(PVOID)GetProcAddress(hLeechcore, "LcCreateEx"),
				(PVOID)Hooked_LcCreateEx_Trampoline_HK,
				(PVOID*)&Original_LcCreateEx
			);
			MH_EnableHook(MH_ALL_HOOKS);
			g_hookInstalled = TRUE;	
		}
		VMProtectEnd();
		return;
	}
	LOG_INFO("attempting EXP first");
	if (!g_hookInstalled) {
		if (TryHookVmmdllByExport()) {
			VMProtectEnd();
			return;
		}

		/*	if (InterlockedCompareExchange(&g_retryThreadStarted, 1, 0) == 0) {
				g_hRetryThread = CreateThread(NULL, 0, HookRetryThreadProc, NULL, 0, NULL);
				if (!g_hRetryThread) {
					InterlockedExchange(&g_retryThreadStarted, 0);
				}
			}*/
	}

	LOG_INFO("Export detour failed or not applicable; attempting to install IAT hook");
	

	// If IAT hook failed, try signature-scan fallback inline hook (background, non-blocking)
	if (!g_hookInstalled) {
		LOG_INFO("Attempting signature-scan fallback for VMMDLL_Initialize (background)");
		if (InterlockedCompareExchange(&g_retryThreadStarted, 1, 0) == 0) {
			g_hRetryThread = CreateThread(NULL, 0, HookRetryThreadProc, NULL, 0, NULL);
			if (!g_hRetryThread) {
				InterlockedExchange(&g_retryThreadStarted, 0);
			}
		}
	}
	VMProtectEnd();
}


BOOL WINAPI Load()
{
	TCHAR tzPath[MAX_PATH];
	TCHAR tzTemp[MAX_PATH * 2];

	GetSystemDirectory(tzPath, MAX_PATH);
	lstrcat(tzPath, TEXT("\\winusb.dll"));

	g_hOriginalDll = LoadLibrary(tzPath);
	if (g_hOriginalDll == NULL)
	{
		wsprintf(tzTemp, TEXT("无法加载模块 %s,程序无法正常运行"), tzPath);
		MessageBox(NULL, tzTemp, TEXT("Core"), MB_ICONSTOP);
	}

	return (g_hOriginalDll != NULL);
}


BOOL APIENTRY DllMain(HMODULE hModule, DWORD dwReason, LPVOID lpReserved) {
	if (dwReason == DLL_PROCESS_ATTACH) {
		VMProtectBegin("Hook::DllMainAttach");
		DisableThreadLibraryCalls(hModule);
		if (!GetConsoleWindow()) {
			if (AllocConsole()) {
				FILE* fp = nullptr;
				freopen_s(&fp, "CONOUT$", "w", stdout);
				freopen_s(&fp, "CONOUT$", "w", stderr);
				freopen_s(&fp, "CONIN$", "r", stdin);
				setvbuf(stdout, NULL, _IONBF, 0);
				setvbuf(stderr, NULL, _IONBF, 0);
			}
		}
		if (Load() && Init())
		{
			InstallHook();
		}
		VMProtectEnd();

	}
	else if (dwReason == DLL_PROCESS_DETACH) {
		VMProtectBegin("Hook::DllMainDetach");
		if (g_hOriginalDll) {
			FreeLibrary(g_hOriginalDll);
		}
		if (g_trampolineVmmdllInitialize) {
			VirtualFree(g_trampolineVmmdllInitialize, 0, MEM_RELEASE);
			g_trampolineVmmdllInitialize = NULL;
		}
		VMProtectEnd();
	}
	return TRUE;
}


