#pragma once

// Define WIN32_LEAN_AND_MEAN to prevent inclusion of winsock.h from Windows.h
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif

#include <Windows.h>
#include <stdio.h>
#ifdef __cplusplus
#include <string>
#include <sstream>
#include <iomanip>
#include "fmt/core.h"
#include "fmt/color.h"
#endif
// LeechCore includes
#include "leechcore.h"

// Additional definitions for RPC compatibility
typedef DWORD error_status_t;
typedef unsigned char byte;

// MEM_SCATTER constants from LeechCore
#define MEM_SCATTER_STACK_SIZE              12

// Common definitions
#define MAX_PATH_LENGTH 260
#define DEFAULT_GRPC_PORT 28473

// Forward declarations
typedef struct _CUSTOM_SERVER_CONFIG {
    CHAR szListenAddress[MAX_PATH_LENGTH];
    DWORD dwPort;
    BOOL fInsecure;
    CHAR szDriverName[MAX_PATH_LENGTH];
} CUSTOM_SERVER_CONFIG, *PCUSTOM_SERVER_CONFIG;

// Error codes
#define CUSTOM_ERROR_SUCCESS           0x00000000
#define CUSTOM_ERROR_DRIVER_NOT_FOUND  0x80000001
#define CUSTOM_ERROR_DRIVER_INIT_FAIL  0x80000002
#define CUSTOM_ERROR_MEMORY_READ_FAIL  0x80000003
#define CUSTOM_ERROR_MEMORY_WRITE_FAIL 0x80000004
#define CUSTOM_ERROR_INVALID_ADDRESS   0x80000005

// RPC Error codes (if not defined in system headers)
#ifndef EPT_S_CANT_PERFORM_OP
#define EPT_S_CANT_PERFORM_OP          0x000006D8  // 1752L - The endpoint mapper database entry could not be created
#endif

#ifdef __cplusplus
// -----------------------------------------------------------------------------
// Minimal debug logging facility (C++)
// -----------------------------------------------------------------------------

#ifndef DBG_LOG
#define DBG_LOG 0
#endif

extern "C" void DebugLogA(const char* fmt, ...);

#if DBG_LOG
#define DBG_PRINTF(...) DebugLogA(__VA_ARGS__)
#else
#define DBG_PRINTF(...) do { } while(0)
#endif

inline std::string split_string(const std::string& text, char delimiter) {
    std::string result;
    std::istringstream iss(text);
    std::string token;
    bool firstToken = true;
    while (std::getline(iss, token, delimiter)) {
        if (!firstToken) {
            result += "\n";
        }
        result += token;
        firstToken = false;
    }
    return result;
}

inline std::string WstringToUTF8(const std::wstring& wstr) {
    std::string res;
    int len = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, nullptr, 0, nullptr, nullptr);
    if (len <= 0) { return res; }
    res.resize(len - 1);
    WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, &res[0], len, nullptr, nullptr);
    return res;
}

inline std::wstring StringToWstring(const std::string& str) {
    std::wstring res;
    int len = MultiByteToWideChar(CP_ACP, 0, str.c_str(), -1, nullptr, 0);
    if (len <= 0) { return res; }
    res.resize(len - 1);
    MultiByteToWideChar(CP_ACP, 0, str.c_str(), -1, &res[0], len);
    return res;
}

inline std::string StringToUTF8(const std::string& str) {
    std::wstring wideStr = StringToWstring(str);
    return WstringToUTF8(wideStr);
}

class MachineCodeGenerator {
public:
    std::string GetMachineCode() {
        unsigned long long cpuIdInt = GetCPUId();
        unsigned long long installDateInt = GetSystemInstallDateFromRegistry();
        unsigned long long machineCodeInt = cpuIdInt ^ installDateInt;
        return IntegerToString(machineCodeInt);
    }
private:
    unsigned long long GetCPUId() {
        int cpuInfo[4] = { 0 };
        __cpuid(cpuInfo, 0);
        unsigned long long cpuId = 0;
        cpuId |= (static_cast<unsigned long long>(cpuInfo[1]) << 32);
        cpuId |= cpuInfo[3];
        cpuId |= (static_cast<unsigned long long>(cpuInfo[2]) << 48);
        cpuId |= (static_cast<unsigned long long>(cpuInfo[0]) << 16);
        return cpuId;
    }
    unsigned long long GetSystemInstallDateFromRegistry() {
        HKEY hKey;
        DWORD dwType = REG_DWORD;
        DWORD dwSize = sizeof(DWORD);
        DWORD dwInstallDate = 0;
        if (RegOpenKeyExW(HKEY_LOCAL_MACHINE, L"SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion", 0, KEY_READ, &hKey) == ERROR_SUCCESS) {
            if (RegQueryValueExW(hKey, L"InstallDate", NULL, &dwType, (LPBYTE)&dwInstallDate, &dwSize) == ERROR_SUCCESS) {
                RegCloseKey(hKey);
                return static_cast<unsigned long long>(dwInstallDate);
            }
            RegCloseKey(hKey);
        }
        return 0;
    }
    std::string IntegerToString(unsigned long long num) {
        std::stringstream ss;
        ss << std::hex << std::setw(16) << std::setfill('0') << num;
        return ss.str();
    }
};

namespace Colors {
    inline auto RESET() { return fmt::fg(fmt::color::white); }
    inline auto LYELLOW() { return fmt::fg(fmt::color::light_yellow); }
    inline auto LGREEN() { return fmt::fg(fmt::color::light_green); }
    inline auto RED() { return fmt::fg(fmt::color::red); }
}

template <typename... Args>
inline void Log(FILE* filehandle, const int& type, fmt::string_view format, Args&&... args) {
    switch (type) {
    case 1: fmt::print(Colors::LGREEN(), "[+] "); break;
    case 2: fmt::print(Colors::RED(), "[X] "); break;
    default: fmt::print(Colors::LYELLOW(), "[-] "); break;
    }
    fmt::print(fmt::runtime(format), std::forward<Args>(args)...);
    fmt::print(Colors::RESET(), "\n");
    if (filehandle) {
        std::string prefix = (type == 1 ? "[+] " : type == 2 ? "[X] " : "[-] ");
        fmt::print(filehandle, "{}{}\n", prefix, fmt::format(fmt::runtime(format), std::forward<Args>(args)...));
    }
}

template <typename... Args>
inline void Log(const int& type, fmt::string_view format, Args&&... args) {
    Log(nullptr, type, format, std::forward<Args>(args)...);
}

#define LOG_INFO(...) Log(0, __VA_ARGS__)
#define LOG_SUCCESS(...) Log(1, __VA_ARGS__)
#define LOG_ERROR(...) Log(2, __VA_ARGS__)

#else // __cplusplus

// C environment: provide minimal no-op logging macros and DBG_PRINTF
#ifndef DBG_LOG
#define DBG_LOG 0
#endif

#if DBG_LOG
void DebugLogA(const char* fmt, ...);
#define DBG_PRINTF(...) DebugLogA(__VA_ARGS__)
#else
#define DBG_PRINTF(...) do { } while(0)
#endif

#define LOG_INFO(...) do { } while(0)
#define LOG_SUCCESS(...) do { } while(0)
#define LOG_ERROR(...) do { } while(0)

#endif // __cplusplus
