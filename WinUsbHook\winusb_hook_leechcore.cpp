﻿#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#include <windows.h>
#include <cstdio>
#include <cstring>
#include <Ws2tcpip.h>
#include <Iphlpapi.h>
#include <io.h>
#include <fcntl.h>
#include <Psapi.h>
#include <stdlib.h>
#include <vector>

#include "sw_mem.h"
#include "debug_output.h"
#include "hdhook.h"

// NT_SUCCESS �궨��
#ifndef NT_SUCCESS
#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)
#endif
#include "minhook/MinHook.h"

#pragma comment(lib, "user32.lib")
#pragma comment(lib, "Ws2_32.lib")
#pragma comment(lib, "Iphlpapi.lib")
#pragma comment(lib, "Psapi.lib")
#pragma comment(linker, "/CETCOMPAT:NO")
// Export WinUSB API symbols from this shim DLL via assembly jump stubs
#pragma comment(linker, "/EXPORT:WinUsb_AbortPipe=AheadLibEx_WinUsb_AbortPipe,@1")
#pragma comment(linker, "/EXPORT:WinUsb_AbortPipeAsync=AheadLibEx_WinUsb_AbortPipeAsync,@2")
#pragma comment(linker, "/EXPORT:WinUsb_ControlTransfer=AheadLibEx_WinUsb_ControlTransfer,@3")
#pragma comment(linker, "/EXPORT:WinUsb_FlushPipe=AheadLibEx_WinUsb_FlushPipe,@4")
#pragma comment(linker, "/EXPORT:WinUsb_Free=AheadLibEx_WinUsb_Free,@5")
#pragma comment(linker, "/EXPORT:WinUsb_GetAdjustedFrameNumber=AheadLibEx_WinUsb_GetAdjustedFrameNumber,@6")
#pragma comment(linker, "/EXPORT:WinUsb_GetAssociatedInterface=AheadLibEx_WinUsb_GetAssociatedInterface,@7")
#pragma comment(linker, "/EXPORT:WinUsb_GetCurrentAlternateSetting=AheadLibEx_WinUsb_GetCurrentAlternateSetting,@8")
#pragma comment(linker, "/EXPORT:WinUsb_GetCurrentFrameNumber=AheadLibEx_WinUsb_GetCurrentFrameNumber,@9")
#pragma comment(linker, "/EXPORT:WinUsb_GetCurrentFrameNumberAndQpc=AheadLibEx_WinUsb_GetCurrentFrameNumberAndQpc,@10")
#pragma comment(linker, "/EXPORT:WinUsb_GetDescriptor=AheadLibEx_WinUsb_GetDescriptor,@11")
#pragma comment(linker, "/EXPORT:WinUsb_GetOverlappedResult=AheadLibEx_WinUsb_GetOverlappedResult,@12")
#pragma comment(linker, "/EXPORT:WinUsb_GetPipePolicy=AheadLibEx_WinUsb_GetPipePolicy,@13")
#pragma comment(linker, "/EXPORT:WinUsb_GetPowerPolicy=AheadLibEx_WinUsb_GetPowerPolicy,@14")
#pragma comment(linker, "/EXPORT:WinUsb_Initialize=AheadLibEx_WinUsb_Initialize,@15")
#pragma comment(linker, "/EXPORT:WinUsb_ParseConfigurationDescriptor=AheadLibEx_WinUsb_ParseConfigurationDescriptor,@16")
#pragma comment(linker, "/EXPORT:WinUsb_ParseDescriptors=AheadLibEx_WinUsb_ParseDescriptors,@17")
#pragma comment(linker, "/EXPORT:WinUsb_QueryDeviceInformation=AheadLibEx_WinUsb_QueryDeviceInformation,@18")
#pragma comment(linker, "/EXPORT:WinUsb_QueryInterfaceSettings=AheadLibEx_WinUsb_QueryInterfaceSettings,@19")
#pragma comment(linker, "/EXPORT:WinUsb_QueryPipe=AheadLibEx_WinUsb_QueryPipe,@20")
#pragma comment(linker, "/EXPORT:WinUsb_QueryPipeEx=AheadLibEx_WinUsb_QueryPipeEx,@21")
#pragma comment(linker, "/EXPORT:WinUsb_ReadIsochPipe=AheadLibEx_WinUsb_ReadIsochPipe,@22")
#pragma comment(linker, "/EXPORT:WinUsb_ReadIsochPipeAsap=AheadLibEx_WinUsb_ReadIsochPipeAsap,@23")
#pragma comment(linker, "/EXPORT:WinUsb_ReadPipe=AheadLibEx_WinUsb_ReadPipe,@24")
#pragma comment(linker, "/EXPORT:WinUsb_RegisterIsochBuffer=AheadLibEx_WinUsb_RegisterIsochBuffer,@25")
#pragma comment(linker, "/EXPORT:WinUsb_ResetPipe=AheadLibEx_WinUsb_ResetPipe,@26")
#pragma comment(linker, "/EXPORT:WinUsb_ResetPipeAsync=AheadLibEx_WinUsb_ResetPipeAsync,@27")
#pragma comment(linker, "/EXPORT:WinUsb_SetCurrentAlternateSetting=AheadLibEx_WinUsb_SetCurrentAlternateSetting,@28")
#pragma comment(linker, "/EXPORT:WinUsb_SetCurrentAlternateSettingAsync=AheadLibEx_WinUsb_SetCurrentAlternateSettingAsync,@29")
#pragma comment(linker, "/EXPORT:WinUsb_SetPipePolicy=AheadLibEx_WinUsb_SetPipePolicy,@30")
#pragma comment(linker, "/EXPORT:WinUsb_SetPowerPolicy=AheadLibEx_WinUsb_SetPowerPolicy,@31")
#pragma comment(linker, "/EXPORT:WinUsb_StartTrackingForTimeSync=AheadLibEx_WinUsb_StartTrackingForTimeSync,@32")
#pragma comment(linker, "/EXPORT:WinUsb_StopTrackingForTimeSync=AheadLibEx_WinUsb_StopTrackingForTimeSync,@33")
#pragma comment(linker, "/EXPORT:WinUsb_UnregisterIsochBuffer=AheadLibEx_WinUsb_UnregisterIsochBuffer,@34")
#pragma comment(linker, "/EXPORT:WinUsb_WriteIsochPipe=AheadLibEx_WinUsb_WriteIsochPipe,@35")
#pragma comment(linker, "/EXPORT:WinUsb_WriteIsochPipeAsap=AheadLibEx_WinUsb_WriteIsochPipeAsap,@36")
#pragma comment(linker, "/EXPORT:WinUsb_WritePipe=AheadLibEx_WinUsb_WritePipe,@37")

// Only use header for types/signatures
#include "../LeechCore/includes/leechcore.h"

extern "C" {
	void InstallLeechCoreHooks();
}

// Forward declare PE export resolver used below
static FARPROC GetProcAddressPE_HK(HMODULE hModule, const char* funcName);

// ------------------------------
// Global variables and forward declarations for inline detour hooks
// ------------------------------
static volatile LONG g_inCreateExGate = 0;
static PVOID g_trampolineLcCreateEx = NULL;
static PVOID g_trampolineLcGetOption = NULL;
static volatile LONG g_inlineDetourActive = 0;
static PVOID g_lcCreateExAddr = NULL;
static PVOID g_lcGetOptionAddr = NULL;
static HANDLE g_hookMonitorThread = NULL;

// Forward declarations for hook functions
static HANDLE Hooked_LcCreateEx_Trampoline_HK(PLC_CONFIG pCfg, PPLC_CONFIG_ERRORINFO ppErr);

// Forward declarations for original function pointer
static HANDLE(*g_LcCreateEx_Orig)(PLC_CONFIG, PPLC_CONFIG_ERRORINFO) = NULL;
static BOOL(*g_LcGetOption_Orig)(HANDLE, QWORD, PQWORD) = NULL;
static BOOL Hooked_LcGetOption_HK(HANDLE h, QWORD f, PQWORD pv);

// ------------------------------
// Helpers: dump LC_CONFIG and synthetic argv
// ------------------------------
static void DumpLcConfig(const char* tag, const LC_CONFIG* cfg)
{
	if (!cfg) { DebugOutputF("[%s] cfg = NULL", tag ? tag : "Cfg"); return; }
	DebugOutputF("[%s] dwVersion=0x%08X dwVerb=%u paMax=0x%llX pfn_printf=%p", tag ? tag : "Cfg",
		cfg->dwVersion, cfg->dwPrintfVerbosity, (unsigned long long)cfg->paMax, cfg->pfn_printf_opt);
	DebugOutputF("[%s] device='%s' remote='%s'", tag ? tag : "Cfg", cfg->szDevice, cfg->szRemote);
}

static void DumpSyntheticArgv(const char* tag, const LC_CONFIG* cfg)
{
	if (!cfg) return;
	DebugOutputF("[%s] argv: -device %s -remote %s", tag ? tag : "Argv", cfg->szDevice, cfg->szRemote);
}

// ------------------------------
// LC option/command name mapping (for readable logs)
// ------------------------------
static const char* LcOptName_HK(QWORD f)
{
	QWORD k = (f & 0xffffffff00000000ULL);
	switch (k) {
		// CORE
	case LC_OPT_CORE_PRINTF_ENABLE: return "LC_OPT_CORE_PRINTF_ENABLE";
	case LC_OPT_CORE_VERBOSE: return "LC_OPT_CORE_VERBOSE";
	case LC_OPT_CORE_VERBOSE_EXTRA: return "LC_OPT_CORE_VERBOSE_EXTRA";
	case LC_OPT_CORE_VERBOSE_EXTRA_TLP: return "LC_OPT_CORE_VERBOSE_EXTRA_TLP";
	case LC_OPT_CORE_VERSION_MAJOR: return "LC_OPT_CORE_VERSION_MAJOR";
	case LC_OPT_CORE_VERSION_MINOR: return "LC_OPT_CORE_VERSION_MINOR";
	case LC_OPT_CORE_VERSION_REVISION: return "LC_OPT_CORE_VERSION_REVISION";
	case LC_OPT_CORE_ADDR_MAX: return "LC_OPT_CORE_ADDR_MAX";
	case LC_OPT_CORE_STATISTICS_CALL_COUNT: return "LC_OPT_CORE_STATISTICS_CALL_COUNT";
	case LC_OPT_CORE_STATISTICS_CALL_TIME: return "LC_OPT_CORE_STATISTICS_CALL_TIME";
	case LC_OPT_CORE_VOLATILE: return "LC_OPT_CORE_VOLATILE";
	case LC_OPT_CORE_READONLY: return "LC_OPT_CORE_READONLY";
		// MEMORYINFO
	case LC_OPT_MEMORYINFO_VALID: return "LC_OPT_MEMORYINFO_VALID";
	case LC_OPT_MEMORYINFO_FLAG_32BIT: return "LC_OPT_MEMORYINFO_FLAG_32BIT";
	case LC_OPT_MEMORYINFO_FLAG_PAE: return "LC_OPT_MEMORYINFO_FLAG_PAE";
	case LC_OPT_MEMORYINFO_ARCH: return "LC_OPT_MEMORYINFO_ARCH";
	case LC_OPT_MEMORYINFO_OS_VERSION_MINOR: return "LC_OPT_MEMORYINFO_OS_VERSION_MINOR";
	case LC_OPT_MEMORYINFO_OS_VERSION_MAJOR: return "LC_OPT_MEMORYINFO_OS_VERSION_MAJOR";
	case LC_OPT_MEMORYINFO_OS_DTB: return "LC_OPT_MEMORYINFO_OS_DTB";
	case LC_OPT_MEMORYINFO_OS_PFN: return "LC_OPT_MEMORYINFO_OS_PFN";
	case LC_OPT_MEMORYINFO_OS_PsLoadedModuleList: return "LC_OPT_MEMORYINFO_OS_PsLoadedModuleList";
	case LC_OPT_MEMORYINFO_OS_PsActiveProcessHead: return "LC_OPT_MEMORYINFO_OS_PsActiveProcessHead";
	case LC_OPT_MEMORYINFO_OS_MACHINE_IMAGE_TP: return "LC_OPT_MEMORYINFO_OS_MACHINE_IMAGE_TP";
	case LC_OPT_MEMORYINFO_OS_NUM_PROCESSORS: return "LC_OPT_MEMORYINFO_OS_NUM_PROCESSORS";
	case LC_OPT_MEMORYINFO_OS_SYSTEMTIME: return "LC_OPT_MEMORYINFO_OS_SYSTEMTIME";
	case LC_OPT_MEMORYINFO_OS_UPTIME: return "LC_OPT_MEMORYINFO_OS_UPTIME";
	case LC_OPT_MEMORYINFO_OS_KERNELBASE: return "LC_OPT_MEMORYINFO_OS_KERNELBASE";
	case LC_OPT_MEMORYINFO_OS_KERNELHINT: return "LC_OPT_MEMORYINFO_OS_KERNELHINT";
	case LC_OPT_MEMORYINFO_OS_KdDebuggerDataBlock: return "LC_OPT_MEMORYINFO_OS_KdDebuggerDataBlock";
		// FPGA
	case LC_OPT_FPGA_PROBE_MAXPAGES: return "LC_OPT_FPGA_PROBE_MAXPAGES";
	case LC_OPT_FPGA_MAX_SIZE_RX: return "LC_OPT_FPGA_MAX_SIZE_RX";
	case LC_OPT_FPGA_MAX_SIZE_TX: return "LC_OPT_FPGA_MAX_SIZE_TX";
	case LC_OPT_FPGA_DELAY_PROBE_READ: return "LC_OPT_FPGA_DELAY_PROBE_READ";
	case LC_OPT_FPGA_DELAY_PROBE_WRITE: return "LC_OPT_FPGA_DELAY_PROBE_WRITE";
	case LC_OPT_FPGA_DELAY_WRITE: return "LC_OPT_FPGA_DELAY_WRITE";
	case LC_OPT_FPGA_DELAY_READ: return "LC_OPT_FPGA_DELAY_READ";
	case LC_OPT_FPGA_RETRY_ON_ERROR: return "LC_OPT_FPGA_RETRY_ON_ERROR";
	case LC_OPT_FPGA_DEVICE_ID: return "LC_OPT_FPGA_DEVICE_ID";
	case LC_OPT_FPGA_FPGA_ID: return "LC_OPT_FPGA_FPGA_ID";
	case LC_OPT_FPGA_VERSION_MAJOR: return "LC_OPT_FPGA_VERSION_MAJOR";
	case LC_OPT_FPGA_VERSION_MINOR: return "LC_OPT_FPGA_VERSION_MINOR";
	case LC_OPT_FPGA_ALGO_TINY: return "LC_OPT_FPGA_ALGO_TINY";
	case LC_OPT_FPGA_ALGO_SYNCHRONOUS: return "LC_OPT_FPGA_ALGO_SYNCHRONOUS";
	case LC_OPT_FPGA_CFGSPACE_XILINX: return "LC_OPT_FPGA_CFGSPACE_XILINX";
	case LC_OPT_FPGA_TLP_READ_CB_WITHINFO: return "LC_OPT_FPGA_TLP_READ_CB_WITHINFO";
	case LC_OPT_FPGA_TLP_READ_CB_FILTERCPL: return "LC_OPT_FPGA_TLP_READ_CB_FILTERCPL";
	default: return "LC_OPT_UNKNOWN";
	}
}

static const char* LcCmdName_HK(QWORD f)
{
	QWORD k = (f & 0xffffffff00000000ULL);
	switch (k) {
		// FPGA CMDs
	case LC_CMD_FPGA_PCIECFGSPACE: return "LC_CMD_FPGA_PCIECFGSPACE";
	case LC_CMD_FPGA_CFGREGPCIE: return "LC_CMD_FPGA_CFGREGPCIE";
	case LC_CMD_FPGA_CFGREGCFG: return "LC_CMD_FPGA_CFGREGCFG";
	case LC_CMD_FPGA_CFGREGDRP: return "LC_CMD_FPGA_CFGREGDRP";
	case LC_CMD_FPGA_CFGREGCFG_MARKWR: return "LC_CMD_FPGA_CFGREGCFG_MARKWR";
	case LC_CMD_FPGA_CFGREGPCIE_MARKWR: return "LC_CMD_FPGA_CFGREGPCIE_MARKWR";
	case LC_CMD_FPGA_CFGREG_DEBUGPRINT: return "LC_CMD_FPGA_CFGREG_DEBUGPRINT";
	case LC_CMD_FPGA_PROBE: return "LC_CMD_FPGA_PROBE";
	case LC_CMD_FPGA_CFGSPACE_SHADOW_RD: return "LC_CMD_FPGA_CFGSPACE_SHADOW_RD";
	case LC_CMD_FPGA_CFGSPACE_SHADOW_WR: return "LC_CMD_FPGA_CFGSPACE_SHADOW_WR";
	case LC_CMD_FPGA_TLP_WRITE_SINGLE: return "LC_CMD_FPGA_TLP_WRITE_SINGLE";
	case LC_CMD_FPGA_TLP_WRITE_MULTIPLE: return "LC_CMD_FPGA_TLP_WRITE_MULTIPLE";
	case LC_CMD_FPGA_TLP_TOSTRING: return "LC_CMD_FPGA_TLP_TOSTRING";
	case LC_CMD_FPGA_TLP_CONTEXT: return "LC_CMD_FPGA_TLP_CONTEXT";
	case LC_CMD_FPGA_TLP_CONTEXT_RD: return "LC_CMD_FPGA_TLP_CONTEXT_RD";
	case LC_CMD_FPGA_TLP_FUNCTION_CALLBACK: return "LC_CMD_FPGA_TLP_FUNCTION_CALLBACK";
	case LC_CMD_FPGA_TLP_FUNCTION_CALLBACK_RD: return "LC_CMD_FPGA_TLP_FUNCTION_CALLBACK_RD";
	case LC_CMD_FPGA_BAR_CONTEXT: return "LC_CMD_FPGA_BAR_CONTEXT";
	case LC_CMD_FPGA_BAR_CONTEXT_RD: return "LC_CMD_FPGA_BAR_CONTEXT_RD";
	case LC_CMD_FPGA_BAR_FUNCTION_CALLBACK: return "LC_CMD_FPGA_BAR_FUNCTION_CALLBACK";
	case LC_CMD_FPGA_BAR_FUNCTION_CALLBACK_RD: return "LC_CMD_FPGA_BAR_FUNCTION_CALLBACK_RD";
	case LC_CMD_FPGA_BAR_INFO: return "LC_CMD_FPGA_BAR_INFO";
		// FILE
	case LC_CMD_FILE_DUMPHEADER_GET: return "LC_CMD_FILE_DUMPHEADER_GET";
		// CORE
	case LC_CMD_STATISTICS_GET: return "LC_CMD_STATISTICS_GET";
	case LC_CMD_MEMMAP_GET: return "LC_CMD_MEMMAP_GET";
	case LC_CMD_MEMMAP_SET: return "LC_CMD_MEMMAP_SET";
	case LC_CMD_MEMMAP_GET_STRUCT: return "LC_CMD_MEMMAP_GET_STRUCT";
	case LC_CMD_MEMMAP_SET_STRUCT: return "LC_CMD_MEMMAP_SET_STRUCT";
		// AGENT/VFS
	case LC_CMD_AGENT_EXEC_PYTHON: return "LC_CMD_AGENT_EXEC_PYTHON";
	case LC_CMD_AGENT_EXIT_PROCESS: return "LC_CMD_AGENT_EXIT_PROCESS";
	case LC_CMD_AGENT_VFS_LIST: return "LC_CMD_AGENT_VFS_LIST";
	case LC_CMD_AGENT_VFS_READ: return "LC_CMD_AGENT_VFS_READ";
	case LC_CMD_AGENT_VFS_WRITE: return "LC_CMD_AGENT_VFS_WRITE";
	case LC_CMD_AGENT_VFS_OPT_GET: return "LC_CMD_AGENT_VFS_OPT_GET";
	case LC_CMD_AGENT_VFS_OPT_SET: return "LC_CMD_AGENT_VFS_OPT_SET";
	case LC_CMD_AGENT_VFS_INITIALIZE: return "LC_CMD_AGENT_VFS_INITIALIZE";
	case LC_CMD_AGENT_VFS_CONSOLE: return "LC_CMD_AGENT_VFS_CONSOLE";
	default: return "LC_CMD_UNKNOWN";
	}
}

// ------------------------------
// WinUSB original DLL forwarders (used by ASM jump stubs)
// ------------------------------
static HMODULE g_hOriginalDll = NULL;
static FARPROC GetAddressWinUsb_HK(PCSTR pszProcName)
{
	FARPROC fpAddress = GetProcAddressPE_HK(g_hOriginalDll, pszProcName);
	if (fpAddress == NULL) {
		CHAR szProcName[64];
		if (HIWORD(pszProcName) == 0) {
			wsprintfA(szProcName, "#%d", pszProcName);
			pszProcName = szProcName;
		}
		WCHAR tzTempW[MAX_PATH];
		wsprintfW(tzTempW, L"�޷��ҵ�����%hs,�����޷���������", pszProcName);
		MessageBoxW(NULL, tzTempW, L"", MB_ICONSTOP);
		ExitProcess(-2);
	}
	return fpAddress;
}

extern "C" {
	PVOID pfnAheadLibEx_WinUsb_AbortPipe;
	PVOID pfnAheadLibEx_WinUsb_AbortPipeAsync;
	PVOID pfnAheadLibEx_WinUsb_ControlTransfer;
	PVOID pfnAheadLibEx_WinUsb_FlushPipe;
	PVOID pfnAheadLibEx_WinUsb_Free;
	PVOID pfnAheadLibEx_WinUsb_GetAdjustedFrameNumber;
	PVOID pfnAheadLibEx_WinUsb_GetAssociatedInterface;
	PVOID pfnAheadLibEx_WinUsb_GetCurrentAlternateSetting;
	PVOID pfnAheadLibEx_WinUsb_GetCurrentFrameNumber;
	PVOID pfnAheadLibEx_WinUsb_GetCurrentFrameNumberAndQpc;
	PVOID pfnAheadLibEx_WinUsb_GetDescriptor;
	PVOID pfnAheadLibEx_WinUsb_GetOverlappedResult;
	PVOID pfnAheadLibEx_WinUsb_GetPipePolicy;
	PVOID pfnAheadLibEx_WinUsb_GetPowerPolicy;
	PVOID pfnAheadLibEx_WinUsb_Initialize;
	PVOID pfnAheadLibEx_WinUsb_ParseConfigurationDescriptor;
	PVOID pfnAheadLibEx_WinUsb_ParseDescriptors;
	PVOID pfnAheadLibEx_WinUsb_QueryDeviceInformation;
	PVOID pfnAheadLibEx_WinUsb_QueryInterfaceSettings;
	PVOID pfnAheadLibEx_WinUsb_QueryPipe;
	PVOID pfnAheadLibEx_WinUsb_QueryPipeEx;
	PVOID pfnAheadLibEx_WinUsb_ReadIsochPipe;
	PVOID pfnAheadLibEx_WinUsb_ReadIsochPipeAsap;
	PVOID pfnAheadLibEx_WinUsb_ReadPipe;
	PVOID pfnAheadLibEx_WinUsb_RegisterIsochBuffer;
	PVOID pfnAheadLibEx_WinUsb_ResetPipe;
	PVOID pfnAheadLibEx_WinUsb_ResetPipeAsync;
	PVOID pfnAheadLibEx_WinUsb_SetCurrentAlternateSetting;
	PVOID pfnAheadLibEx_WinUsb_SetCurrentAlternateSettingAsync;
	PVOID pfnAheadLibEx_WinUsb_SetPipePolicy;
	PVOID pfnAheadLibEx_WinUsb_SetPowerPolicy;
	PVOID pfnAheadLibEx_WinUsb_StartTrackingForTimeSync;
	PVOID pfnAheadLibEx_WinUsb_StopTrackingForTimeSync;
	PVOID pfnAheadLibEx_WinUsb_UnregisterIsochBuffer;
	PVOID pfnAheadLibEx_WinUsb_WriteIsochPipe;
	PVOID pfnAheadLibEx_WinUsb_WriteIsochPipeAsap;
	PVOID pfnAheadLibEx_WinUsb_WritePipe;
}

static BOOL InitWinUsbForwarders_HK()
{
	DebugOutput("[WinUSB] Initializing forwarders...");
	pfnAheadLibEx_WinUsb_AbortPipe = GetAddressWinUsb_HK("WinUsb_AbortPipe");
	pfnAheadLibEx_WinUsb_AbortPipeAsync = GetAddressWinUsb_HK("WinUsb_AbortPipeAsync");
	pfnAheadLibEx_WinUsb_ControlTransfer = GetAddressWinUsb_HK("WinUsb_ControlTransfer");
	pfnAheadLibEx_WinUsb_FlushPipe = GetAddressWinUsb_HK("WinUsb_FlushPipe");
	pfnAheadLibEx_WinUsb_Free = GetAddressWinUsb_HK("WinUsb_Free");
	pfnAheadLibEx_WinUsb_GetAdjustedFrameNumber = GetAddressWinUsb_HK("WinUsb_GetAdjustedFrameNumber");
	pfnAheadLibEx_WinUsb_GetAssociatedInterface = GetAddressWinUsb_HK("WinUsb_GetAssociatedInterface");
	pfnAheadLibEx_WinUsb_GetCurrentAlternateSetting = GetAddressWinUsb_HK("WinUsb_GetCurrentAlternateSetting");
	pfnAheadLibEx_WinUsb_GetCurrentFrameNumber = GetAddressWinUsb_HK("WinUsb_GetCurrentFrameNumber");
	pfnAheadLibEx_WinUsb_GetCurrentFrameNumberAndQpc = GetAddressWinUsb_HK("WinUsb_GetCurrentFrameNumberAndQpc");
	pfnAheadLibEx_WinUsb_GetDescriptor = GetAddressWinUsb_HK("WinUsb_GetDescriptor");
	pfnAheadLibEx_WinUsb_GetOverlappedResult = GetAddressWinUsb_HK("WinUsb_GetOverlappedResult");
	pfnAheadLibEx_WinUsb_GetPipePolicy = GetAddressWinUsb_HK("WinUsb_GetPipePolicy");
	pfnAheadLibEx_WinUsb_GetPowerPolicy = GetAddressWinUsb_HK("WinUsb_GetPowerPolicy");
	pfnAheadLibEx_WinUsb_Initialize = GetAddressWinUsb_HK("WinUsb_Initialize");
	pfnAheadLibEx_WinUsb_ParseConfigurationDescriptor = GetAddressWinUsb_HK("WinUsb_ParseConfigurationDescriptor");
	pfnAheadLibEx_WinUsb_ParseDescriptors = GetAddressWinUsb_HK("WinUsb_ParseDescriptors");
	pfnAheadLibEx_WinUsb_QueryDeviceInformation = GetAddressWinUsb_HK("WinUsb_QueryDeviceInformation");
	pfnAheadLibEx_WinUsb_QueryInterfaceSettings = GetAddressWinUsb_HK("WinUsb_QueryInterfaceSettings");
	pfnAheadLibEx_WinUsb_QueryPipe = GetAddressWinUsb_HK("WinUsb_QueryPipe");
	pfnAheadLibEx_WinUsb_QueryPipeEx = GetAddressWinUsb_HK("WinUsb_QueryPipeEx");
	pfnAheadLibEx_WinUsb_ReadIsochPipe = GetAddressWinUsb_HK("WinUsb_ReadIsochPipe");
	pfnAheadLibEx_WinUsb_ReadIsochPipeAsap = GetAddressWinUsb_HK("WinUsb_ReadIsochPipeAsap");
	pfnAheadLibEx_WinUsb_ReadPipe = GetAddressWinUsb_HK("WinUsb_ReadPipe");
	pfnAheadLibEx_WinUsb_RegisterIsochBuffer = GetAddressWinUsb_HK("WinUsb_RegisterIsochBuffer");
	pfnAheadLibEx_WinUsb_ResetPipe = GetAddressWinUsb_HK("WinUsb_ResetPipe");
	pfnAheadLibEx_WinUsb_ResetPipeAsync = GetAddressWinUsb_HK("WinUsb_ResetPipeAsync");
	pfnAheadLibEx_WinUsb_SetCurrentAlternateSetting = GetAddressWinUsb_HK("WinUsb_SetCurrentAlternateSetting");
	pfnAheadLibEx_WinUsb_SetCurrentAlternateSettingAsync = GetAddressWinUsb_HK("WinUsb_SetCurrentAlternateSettingAsync");
	pfnAheadLibEx_WinUsb_SetPipePolicy = GetAddressWinUsb_HK("WinUsb_SetPipePolicy");
	pfnAheadLibEx_WinUsb_SetPowerPolicy = GetAddressWinUsb_HK("WinUsb_SetPowerPolicy");
	pfnAheadLibEx_WinUsb_StartTrackingForTimeSync = GetAddressWinUsb_HK("WinUsb_StartTrackingForTimeSync");
	pfnAheadLibEx_WinUsb_StopTrackingForTimeSync = GetAddressWinUsb_HK("WinUsb_StopTrackingForTimeSync");
	pfnAheadLibEx_WinUsb_UnregisterIsochBuffer = GetAddressWinUsb_HK("WinUsb_UnregisterIsochBuffer");
	pfnAheadLibEx_WinUsb_WriteIsochPipe = GetAddressWinUsb_HK("WinUsb_WriteIsochPipe");
	pfnAheadLibEx_WinUsb_WriteIsochPipeAsap = GetAddressWinUsb_HK("WinUsb_WriteIsochPipeAsap");
	pfnAheadLibEx_WinUsb_WritePipe = GetAddressWinUsb_HK("WinUsb_WritePipe");
	int okCnt = 0;
	okCnt += (pfnAheadLibEx_WinUsb_AbortPipe != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_AbortPipeAsync != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_ControlTransfer != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_FlushPipe != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_Free != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_GetAdjustedFrameNumber != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_GetAssociatedInterface != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_GetCurrentAlternateSetting != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_GetCurrentFrameNumber != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_GetCurrentFrameNumberAndQpc != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_GetDescriptor != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_GetOverlappedResult != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_GetPipePolicy != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_GetPowerPolicy != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_Initialize != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_ParseConfigurationDescriptor != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_ParseDescriptors != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_QueryDeviceInformation != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_QueryInterfaceSettings != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_QueryPipe != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_QueryPipeEx != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_ReadIsochPipe != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_ReadIsochPipeAsap != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_ReadPipe != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_RegisterIsochBuffer != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_ResetPipe != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_ResetPipeAsync != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_SetCurrentAlternateSetting != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_SetCurrentAlternateSettingAsync != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_SetPipePolicy != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_SetPowerPolicy != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_StartTrackingForTimeSync != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_StopTrackingForTimeSync != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_UnregisterIsochBuffer != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_WriteIsochPipe != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_WriteIsochPipeAsap != NULL);
	okCnt += (pfnAheadLibEx_WinUsb_WritePipe != NULL);
	DebugOutputF("[WinUSB] Forwarders ready: %d/37", okCnt);
	return TRUE;
}

static BOOL LoadOriginalWinUsb_HK()
{
	TCHAR tzPath[MAX_PATH];
	GetSystemDirectory(tzPath, MAX_PATH);
	lstrcat(tzPath, TEXT("\\winusb.dll"));
	DebugOutputF("[WinUSB] Loading system DLL: %ls", tzPath);
	g_hOriginalDll = LoadLibrary(tzPath);
	if (g_hOriginalDll == NULL) {
		DebugOutput("[WinUSB] LoadLibrary failed.");
	}
	else { DebugOutputF("[WinUSB] Loaded: %p", g_hOriginalDll); }
	return (g_hOriginalDll != NULL);
}

// ------------------------------
// PEB structures and helpers
// ------------------------------
typedef struct _UNICODE_STRING_HK {
	USHORT Length;
	USHORT MaximumLength;
	PWSTR  Buffer;
} UNICODE_STRING_HK, * PUNICODE_STRING_HK;

typedef struct _LDR_DATA_TABLE_ENTRY_HK {
	LIST_ENTRY InLoadOrderLinks;
	LIST_ENTRY InMemoryOrderLinks;
	LIST_ENTRY InInitializationOrderLinks;
	PVOID DllBase;
	PVOID EntryPoint;
	ULONG SizeOfImage;
	UNICODE_STRING_HK FullDllName;
	UNICODE_STRING_HK BaseDllName;
} LDR_DATA_TABLE_ENTRY_HK, * PLDR_DATA_TABLE_ENTRY_HK;

typedef struct _PEB_LDR_DATA_HK {
	ULONG Length;
	BOOLEAN Initialized;
	PVOID SsHandle;
	LIST_ENTRY InLoadOrderModuleList;
	LIST_ENTRY InMemoryOrderModuleList;
	LIST_ENTRY InInitializationOrderModuleList;
} PEB_LDR_DATA_HK, * PPEB_LDR_DATA_HK;

typedef struct _PEB_HK {
	BOOLEAN InheritedAddressSpace;
	BOOLEAN ReadImageFileExecOptions;
	BOOLEAN BeingDebugged;
	PVOID Mutant;
	PVOID ImageBaseAddress;
	PPEB_LDR_DATA_HK Ldr;
} PEB_HK, * PPEB_HK;

static HMODULE FindModuleByNamePEB_HK(const char* moduleName)
{
	if (!moduleName) {
#ifdef _WIN64
		PPEB_HK peb = (PPEB_HK)__readgsqword(0x60);
#else
		PPEB_HK peb = (PPEB_HK)__readfsdword(0x30);
#endif
		if (!peb || !peb->ImageBaseAddress) return NULL;
		return (HMODULE)peb->ImageBaseAddress;
	}

#ifdef _WIN64
	PPEB_HK peb = (PPEB_HK)__readgsqword(0x60);
#else
	PPEB_HK peb = (PPEB_HK)__readfsdword(0x30);
#endif
	if (!peb || !peb->Ldr) return NULL;

	char targetName[MAX_PATH];
	strcpy_s(targetName, sizeof(targetName), moduleName);
	_strlwr_s(targetName, sizeof(targetName));

	PLIST_ENTRY head = &peb->Ldr->InMemoryOrderModuleList;
	PLIST_ENTRY entry = head->Flink;
	while (entry != head) {
		PLDR_DATA_TABLE_ENTRY_HK module = CONTAINING_RECORD(entry, LDR_DATA_TABLE_ENTRY_HK, InMemoryOrderLinks);
		if (module->BaseDllName.Buffer && module->BaseDllName.Length) {
			char modulePath[MAX_PATH] = { 0 };
			WideCharToMultiByte(CP_ACP, 0, module->BaseDllName.Buffer,
				module->BaseDllName.Length / sizeof(WCHAR),
				modulePath, sizeof(modulePath) - 1, NULL, NULL);
			_strlwr_s(modulePath, sizeof(modulePath));
			if (strstr(modulePath, targetName)) {
				return (HMODULE)module->DllBase;
			}
		}
		entry = entry->Flink;
	}
	return NULL;
}

// ------------------------------
// Minimal PE export resolver
// ------------------------------
static FARPROC GetProcAddressPE_HK(HMODULE hModule, const char* funcName)
{
	if (!hModule || !funcName) return NULL;
	PBYTE base = (PBYTE)hModule;
	PIMAGE_DOS_HEADER dos = (PIMAGE_DOS_HEADER)base;
	if (dos->e_magic != IMAGE_DOS_SIGNATURE) return NULL;
	PIMAGE_NT_HEADERS nt = (PIMAGE_NT_HEADERS)(base + dos->e_lfanew);
	if (nt->Signature != IMAGE_NT_SIGNATURE) return NULL;
	DWORD rva = nt->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
	if (!rva) return NULL;
	PIMAGE_EXPORT_DIRECTORY exp = (PIMAGE_EXPORT_DIRECTORY)(base + rva);
	PDWORD names = (PDWORD)(base + exp->AddressOfNames);
	PDWORD addrs = (PDWORD)(base + exp->AddressOfFunctions);
	PWORD ords = (PWORD)(base + exp->AddressOfNameOrdinals);
	for (DWORD i = 0; i < exp->NumberOfNames; ++i) {
		const char* name = (const char*)(base + names[i]);
		if (strcmp(name, funcName) == 0) {
			WORD ord = ords[i];
			DWORD fnRva = addrs[ord];
			return (FARPROC)(base + fnRva);
		}
	}
	return NULL;
}

// ------------------------------
// Memory protect via syscall (bypasses hooks)
// ------------------------------
static BOOL ProtectMemoryWithSyscall_HK(PVOID address, SIZE_T size, ULONG newProtect, PULONG oldProtect)
{
	PVOID baseAddress = address;
	SIZE_T regionSize = size;
	NTSTATUS status = Sw3NtProtectVirtualMemory(GetCurrentProcess(), &baseAddress, &regionSize, newProtect, oldProtect);
	return status >= 0;
}

// ------------------------------
// VMM-style inline detour implementation
// ------------------------------
static BOOL InstallInlineDetourGeneric_HK(PVOID targetFunction, PVOID hookFunction, PVOID* ppTrampolineOut)
{
	if (ppTrampolineOut) { *ppTrampolineOut = NULL; }
	if (!targetFunction || !hookFunction) return FALSE;

	BYTE* p = (BYTE*)targetFunction;

	const SIZE_T overwriteLen = 15;
	SIZE_T trampolineSize = overwriteLen + 12;
	BYTE* trampoline = (BYTE*)VirtualAlloc(NULL, trampolineSize, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
	if (!trampoline) {
		DebugOutput("[InlineDetour] VirtualAlloc failed for trampoline");
		return FALSE;
	}

	// CRITICAL: Always read current bytes from target address (VMM-style)
	// This ensures we get the most up-to-date original bytes, even after hook restoration
	memcpy(trampoline, p, overwriteLen);
	DebugOutputF("[InlineDetour] Copied current bytes to trampoline: %02X %02X %02X %02X %02X...",
		p[0], p[1], p[2], p[3], p[4]);
	{
		BYTE* t = trampoline + overwriteLen;
		t[0] = 0x48; t[1] = 0xB8; // mov rax, imm64
		*(void**)(t + 2) = (p + overwriteLen);
		t[10] = 0xFF; t[11] = 0xE0; // jmp rax
	}

	// Patch target: [mov rax, hook][jmp rax] + NOPs
	DWORD oldProtect = 0;
	if (!ProtectMemoryWithSyscall_HK(p, overwriteLen, PAGE_EXECUTE_READWRITE, &oldProtect)) {
		DebugOutputF("[InlineDetour] ProtectMemoryWithSyscall failed: %d", GetLastError());
		VirtualFree(trampoline, 0, MEM_RELEASE);
		return FALSE;
	}
	{
		BYTE stub[32] = { 0 };
		stub[0] = 0x48; stub[1] = 0xB8; // mov rax, imm64
		*(void**)(stub + 2) = hookFunction;
		stub[10] = 0xFF; stub[11] = 0xE0; // jmp rax
		SIZE_T toWrite = overwriteLen < sizeof(stub) ? overwriteLen : sizeof(stub);
		memset(stub + 12, 0x90, toWrite - 12); // fill with NOPs
		memcpy(p, stub, toWrite);
	}
	DWORD tmp;
	ProtectMemoryWithSyscall_HK(p, overwriteLen, oldProtect, &tmp);

	FlushInstructionCache(GetCurrentProcess(), p, overwriteLen);
	FlushInstructionCache(GetCurrentProcess(), trampoline, trampolineSize);

	if (ppTrampolineOut) { *ppTrampolineOut = trampoline; }
	DebugOutputF("[InlineDetour] Successfully installed at %p, trampoline=%p", p, trampoline);
	return TRUE;
}

// ------------------------------
// MinHook monitoring and persistence thread  
// ------------------------------
static PVOID g_monitoredLcCreateExAddr = NULL;
static PVOID g_monitoredLcGetOptionAddr = NULL;
static volatile LONG g_minHookMonitorActive = 0;
static HANDLE g_minHookMonitorThread = NULL;

// Forward declarations for original function pointers (defined later in file)
// Forward declarations - removed to avoid duplicate symbols
// extern HANDLE (*g_LcCreateEx_Orig)(PLC_CONFIG, PPLC_CONFIG_ERRORINFO); // see static definition below
// extern BOOL   (*g_LcGetOption_Orig)(HANDLE, QWORD, PQWORD); // see static definition below
static DWORD WINAPI MinHookMonitorThread_HK(LPVOID)
{
	DebugOutput("[MinHookMonitor] Starting MinHook monitoring thread");
	const int kMaxMonitorTime = 60; // Monitor for 60 seconds
	int checkCount = 0;
	int reinstallCount = 0;
	const int kMaxReinstalls = 5; // Max reinstall attempts

	while (checkCount < kMaxMonitorTime && InterlockedCompareExchange(&g_minHookMonitorActive, 0, 0) != 0) {
		Sleep(500); // Check every 500ms
		checkCount++;

		BOOL needReinstall = FALSE;

		// Check LcCreateEx hook integrity (MinHook uses 0xE9 JMP rel32)
		if (g_monitoredLcCreateExAddr) {
			BYTE* p = (BYTE*)g_monitoredLcCreateExAddr;
			if (p[0] != 0xE9) { // MinHook's JMP rel32 instruction
				DebugOutputF("[MinHookMonitor] LcCreateEx hook corrupted at check #%d: %02X (expected: E9)",
					checkCount, p[0]);
				needReinstall = TRUE;
			}
		}

		// Check LcGetOption hook integrity
		if (g_monitoredLcGetOptionAddr) {
			BYTE* p = (BYTE*)g_monitoredLcGetOptionAddr;
			if (p[0] != 0xE9) { // MinHook's JMP rel32 instruction
				DebugOutputF("[MinHookMonitor] LcGetOption hook corrupted at check #%d: %02X (expected: E9)",
					checkCount, p[0]);
				needReinstall = TRUE;
			}
		}

		if (needReinstall && reinstallCount < kMaxReinstalls) {
			DebugOutputF("[MinHookMonitor] Attempting to reinstall corrupted hooks (attempt #%d)", reinstallCount + 1);

			// Wait longer to avoid conflict with anti-hook mechanism
			Sleep(5000);

			// Reinstall LcCreateEx if corrupted
			if (g_monitoredLcCreateExAddr) {
				BYTE* p = (BYTE*)g_monitoredLcCreateExAddr;
				if (p[0] != 0xE9) {
					DebugOutput("[MinHookMonitor] Rebuilding LcCreateEx hook...");

					// Complete hook rebuild: Disable -> Remove -> Create -> Enable
					MH_STATUS s1 = MH_DisableHook(g_monitoredLcCreateExAddr);
					MH_STATUS s2 = MH_RemoveHook(g_monitoredLcCreateExAddr);
					MH_STATUS s3 = MH_CreateHook(g_monitoredLcCreateExAddr, (LPVOID)Hooked_LcCreateEx_Trampoline_HK, (LPVOID*)&g_LcCreateEx_Orig);
					MH_STATUS s4 = MH_EnableHook(g_monitoredLcCreateExAddr);

					DebugOutputF("[MinHookMonitor] LcCreateEx rebuild: Disable=%d, Remove=%d, Create=%d, Enable=%d", s1, s2, s3, s4);
					if (s4 == MH_OK) {
						DebugOutput("[MinHookMonitor] Successfully rebuilt LcCreateEx hook");
					}
					else {
						DebugOutputF("[MinHookMonitor] Failed to rebuild LcCreateEx hook, final status: %d", s4);
					}
				}
			}

			// Reinstall LcGetOption if corrupted
			if (g_monitoredLcGetOptionAddr) {
				BYTE* p = (BYTE*)g_monitoredLcGetOptionAddr;
				if (p[0] != 0xE9) {
					DebugOutput("[MinHookMonitor] Rebuilding LcGetOption hook...");

					// Complete hook rebuild: Disable -> Remove -> Create -> Enable
					MH_STATUS s1 = MH_DisableHook(g_monitoredLcGetOptionAddr);
					MH_STATUS s2 = MH_RemoveHook(g_monitoredLcGetOptionAddr);
					MH_STATUS s3 = MH_CreateHook(g_monitoredLcGetOptionAddr, (LPVOID)Hooked_LcGetOption_HK, (LPVOID*)&g_LcGetOption_Orig);
					MH_STATUS s4 = MH_EnableHook(g_monitoredLcGetOptionAddr);

					DebugOutputF("[MinHookMonitor] LcGetOption rebuild: Disable=%d, Remove=%d, Create=%d, Enable=%d", s1, s2, s3, s4);
					if (s4 == MH_OK) {
						DebugOutput("[MinHookMonitor] Successfully rebuilt LcGetOption hook");
					}
					else {
						DebugOutputF("[MinHookMonitor] Failed to rebuild LcGetOption hook, final status: %d", s4);
					}
				}
			}

			reinstallCount++;
		}
		else if (checkCount % 20 == 0) { // Every 10 seconds
			DebugOutputF("[MinHookMonitor] Check #%d: MinHook hooks intact", checkCount);
		}

		// If we've reached max reinstalls, reduce check frequency
		if (reinstallCount >= kMaxReinstalls) {
			Sleep(2000); // Check less frequently
		}
	}

	DebugOutput("[MinHookMonitor] MinHook monitoring thread exiting");
	return 0;
}

// Legacy inline detour monitoring thread (kept for reference)
static DWORD WINAPI HookMonitorThread_HK(LPVOID)
{
	DebugOutput("[HookMonitor] Starting hook monitoring thread");
	const int kMaxMonitorTime = 60; // Monitor for 60 seconds
	int checkCount = 0;

	while (checkCount < kMaxMonitorTime && InterlockedCompareExchange(&g_inlineDetourActive, 0, 0) != 0) {
		Sleep(1000);
		checkCount++;

		BOOL needReinstall = FALSE;

		// Check LcCreateEx hook integrity
		if (g_lcCreateExAddr) {
			BYTE* p = (BYTE*)g_lcCreateExAddr;
			// Check if our hook stub is still there (mov rax, hook_addr pattern)
			if (p[0] != 0x48 || p[1] != 0xB8) {
				DebugOutputF("[HookMonitor] LcCreateEx hook corrupted at check #%d: %02X %02X (expected: 48 B8)",
					checkCount, p[0], p[1]);
				needReinstall = TRUE;
			}
		}

		// Check LcGetOption hook integrity  
		if (g_lcGetOptionAddr) {
			BYTE* p = (BYTE*)g_lcGetOptionAddr;
			if (p[0] != 0x48 || p[1] != 0xB8) {
				DebugOutputF("[HookMonitor] LcGetOption hook corrupted at check #%d: %02X %02X (expected: 48 B8)",
					checkCount, p[0], p[1]);
				needReinstall = TRUE;
			}
		}

		if (needReinstall) {
			DebugOutput("[HookMonitor] Attempting to reinstall corrupted hooks");

			// Reinstall LcCreateEx if corrupted
			if (g_lcCreateExAddr && g_trampolineLcCreateEx) {
				PVOID newTrampoline = NULL;
				if (InstallInlineDetourGeneric_HK(g_lcCreateExAddr, (PVOID)Hooked_LcCreateEx_Trampoline_HK, &newTrampoline)) {
					DebugOutput("[HookMonitor] Successfully reinstalled LcCreateEx hook");
					// CRITICAL: Update trampoline pointer to the new one, old trampoline is now invalid
					if (newTrampoline) {
						// Clean up old trampoline
						VirtualFree(g_trampolineLcCreateEx, 0, MEM_RELEASE);
						g_trampolineLcCreateEx = newTrampoline;
						DebugOutputF("[HookMonitor] Updated LcCreateEx trampoline: %p -> %p", g_trampolineLcCreateEx, newTrampoline);
					}
				}
				else {
					DebugOutput("[HookMonitor] Failed to reinstall LcCreateEx hook");
				}
			}

			// Reinstall LcGetOption if corrupted
			if (g_lcGetOptionAddr && g_trampolineLcGetOption) {
				PVOID newTrampoline = NULL;
				if (InstallInlineDetourGeneric_HK(g_lcGetOptionAddr, (PVOID)Hooked_LcGetOption_HK, &newTrampoline)) {
					DebugOutput("[HookMonitor] Successfully reinstalled LcGetOption hook");
					// CRITICAL: Update trampoline pointer to the new one, old trampoline is now invalid
					if (newTrampoline) {
						// Clean up old trampoline
						VirtualFree(g_trampolineLcGetOption, 0, MEM_RELEASE);
						g_trampolineLcGetOption = newTrampoline;
						DebugOutputF("[HookMonitor] Updated LcGetOption trampoline: %p -> %p", g_trampolineLcGetOption, newTrampoline);
					}
				}
				else {
					DebugOutput("[HookMonitor] Failed to reinstall LcGetOption hook");
				}
			}
		}
		else if (checkCount == 1 || checkCount % 10 == 0) {
			DebugOutputF("[HookMonitor] Check #%d: inline detour hooks intact", checkCount);
		}
	}

	DebugOutput("[HookMonitor] Hook monitoring thread exiting");
	return 0;
}

// ------------------------------
// Absolute jump detour helper (15B overwrite, x64 safe)
// ------------------------------
static BOOL IsExecutableAtAddress_HK(PVOID addr)
{
	MEMORY_BASIC_INFORMATION mbi = { 0 };
	if (0 == VirtualQuery(addr, &mbi, sizeof(mbi))) return FALSE;
	if (mbi.State != MEM_COMMIT) return FALSE;
	DWORD p = (mbi.Protect & 0xff);
	if (p & PAGE_GUARD) return FALSE;
	return p == PAGE_EXECUTE || p == PAGE_EXECUTE_READ || p == PAGE_EXECUTE_READWRITE || p == PAGE_EXECUTE_WRITECOPY;
}



static PBYTE FindPattern(const char* pattern)
{
	static uintptr_t moduleAdressmm = 0;
	if (!moduleAdressmm)
		moduleAdressmm = (uintptr_t)FindModuleByNamePEB_HK(NULL);

	if (!moduleAdressmm)
		return 0;
	DebugOutputF("[SIG] FindPattern: moduleAdressmm=%p", moduleAdressmm);
	static auto patternToByteZmm = [](const char* pattern)
		{
			auto       bytesmm = std::vector<int>{};
			const auto startmm = const_cast<char*>(pattern);
			const auto endmm = const_cast<char*>(pattern) + strlen(pattern);

			for (auto currentmm = startmm; currentmm < endmm; ++currentmm)
			{
				if (*currentmm == '?')
				{
					++currentmm;
					if (*currentmm == '?')
						++currentmm;
					bytesmm.push_back(-1);
				}
				else { bytesmm.push_back(strtoul(currentmm, &currentmm, 16)); }
			}
			return bytesmm;
		};

	const auto dosHeadermm = (PIMAGE_DOS_HEADER)moduleAdressmm;
	const auto ntHeadersmm = (PIMAGE_NT_HEADERS)((std::uint8_t*)moduleAdressmm + dosHeadermm->e_lfanew);

	const auto sizeOfImage = ntHeadersmm->OptionalHeader.SizeOfImage;
	auto       patternBytesmm = patternToByteZmm(pattern);
	const auto scanBytesmm = reinterpret_cast<std::uint8_t*>(moduleAdressmm);

	const auto smm = patternBytesmm.size();
	const auto dmm = patternBytesmm.data();

	for (auto imm = 0ul; imm < sizeOfImage - smm; ++imm)
	{
		bool foundmm = true;
		for (auto jmm = 0ul; jmm < smm; ++jmm)
		{
			if (scanBytesmm[imm + jmm] != dmm[jmm] && dmm[jmm] != -1)
			{
				foundmm = false;
				break;
			}
		}
		if (foundmm) { return reinterpret_cast<PBYTE>(&scanBytesmm[imm]); }
	}
	return NULL;
}

// Restrict signature search to main module .text sections
static PBYTE FindSignatureInMainText_HK(const char* patternStr)
{
	DebugOutputF("[SIG] FindSignatureInMainText_HK: searching pattern '%s'", patternStr);
	PBYTE result = FindPattern(patternStr);
	DebugOutputF("[SIG] FindSignatureInMainText_HK: result=%p", result);
	return result;
}

// Forward declarations for signature hook to reference later symbols
static HANDLE Hooked_LcCreateEx_HK(PLC_CONFIG pCfg, PPLC_CONFIG_ERRORINFO ppErr);
// Forward declarations - removed to avoid duplicate symbols
// extern HANDLE (*g_LcCreateEx_Orig)(PLC_CONFIG, PPLC_CONFIG_ERRORINFO); // see static definition below
static BOOL Hooked_LcGetOption_HK(HANDLE h, QWORD f, PQWORD pv);
extern BOOL(*g_LcGetOption_Orig)(HANDLE, QWORD, PQWORD);

// ------------------------------
// RVA-based search for static-linked LcCreateEx
// RVA: 0xDAF9F0
// ------------------------------
static PBYTE FindLcCreateExByRVA_HK()
{
	HMODULE hExe = FindModuleByNamePEB_HK(NULL);
	if (!hExe) {
		DebugOutputF("[RVA] FindLcCreateExByRVA_HK: main module not found");
		return NULL;
	}

	PBYTE base = (PBYTE)hExe;
	PBYTE targetAddr = base + 0xDAF9F0;

	DebugOutputF("[RVA] FindLcCreateExByRVA_HK: base=%p, target=%p", base, targetAddr);

	// Verify the address is within module bounds
	PIMAGE_DOS_HEADER dos = (PIMAGE_DOS_HEADER)base;
	if (dos->e_magic != IMAGE_DOS_SIGNATURE) {
		DebugOutputF("[RVA] FindLcCreateExByRVA_HK: invalid DOS header");
		return NULL;
	}

	PIMAGE_NT_HEADERS nt = (PIMAGE_NT_HEADERS)(base + dos->e_lfanew);
	if (nt->Signature != IMAGE_NT_SIGNATURE) {
		DebugOutputF("[RVA] FindLcCreateExByRVA_HK: invalid NT header");
		return NULL;
	}

	SIZE_T imageSize = nt->OptionalHeader.SizeOfImage;
	if (0xDAF9F0 >= imageSize) {
		DebugOutputF("[RVA] FindLcCreateExByRVA_HK: RVA 0x%X beyond image size 0x%X", 0xDAF9F0, imageSize);
		return NULL;
	}

	DebugOutputF("[RVA] FindLcCreateExByRVA_HK: found LcCreateEx at %p", targetAddr);
	return targetAddr;
}

// Generic RVA->INT3 installer reused by LcCreateEx / LcGetOption
static BOOL HookByRVA_INT3_HK(const char* tag, DWORD rva, LPVOID hook, LPVOID* porig)
{
	HMODULE hExe = FindModuleByNamePEB_HK(NULL);
	if (!hExe) { DebugOutputF("[INT3] %s: main module not found", tag); return FALSE; }
	PBYTE base = (PBYTE)hExe;
	PIMAGE_DOS_HEADER dos = (PIMAGE_DOS_HEADER)base;
	if (dos->e_magic != IMAGE_DOS_SIGNATURE) return FALSE;
	PIMAGE_NT_HEADERS nt = (PIMAGE_NT_HEADERS)(base + dos->e_lfanew);
	if (nt->Signature != IMAGE_NT_SIGNATURE) return FALSE;
	if (rva >= nt->OptionalHeader.SizeOfImage) {
		DebugOutputF("[INT3] %s: RVA 0x%X >= image size 0x%X", tag, rva, nt->OptionalHeader.SizeOfImage);
		return FALSE;
	}
	PBYTE entry = base + rva;
	DebugOutputF("[INT3] %s: base=%p, entry=%p", tag, base, entry);
	if (!IsExecutableAtAddress_HK(entry)) { DebugOutputF("[INT3] %s: entry not executable", tag); return FALSE; }
	if (!HardBreakHook::InstallInt3Hook((PVOID)entry, hook, porig)) { DebugOutputF("[INT3] %s: install failed", tag); return FALSE; }
	DebugOutputF("[INT3] %s: installed INT3 at %p (orig=%p)", tag, entry, porig ? *porig : NULL);
	return TRUE;
}

static BOOL HookLcGetOptionByINT3_HK()
{
	if (!HookByRVA_INT3_HK("LcGetOption", 0xDB14F0, (LPVOID)Hooked_LcGetOption_HK, (LPVOID*)&g_LcGetOption_Orig)) {
		return FALSE;
	}
	// Start anti-restore monitor for LcGetOption INT3
	HMODULE hExe = FindModuleByNamePEB_HK(NULL);
	if (!hExe) return TRUE;
	PBYTE base = (PBYTE)hExe;
	PBYTE entry = base + 0xDB14F0;
	CreateThread(NULL, 0, [](LPVOID param) -> DWORD {
		PBYTE addr = (PBYTE)param;
		int checkCount = 0;
		while (checkCount < 30) {
			Sleep(1000);
			checkCount++;
			BYTE currentByte = *addr;
			if (currentByte != 0xCC) {
				DebugOutputF("[INT3] ANTI-HOOK DETECTED (LcGetOption)! Byte restored to 0x%02X at check #%d", currentByte, checkCount);
				DWORD oldProtect; PVOID targetAddr = addr; SIZE_T size = 1;
				NTSTATUS status = Sw3NtProtectVirtualMemory(GetCurrentProcess(), &targetAddr, &size, PAGE_EXECUTE_READWRITE, &oldProtect);
				if (NT_SUCCESS(status)) {
					*addr = 0xCC;
					targetAddr = addr; size = 1;
					Sw3NtProtectVirtualMemory(GetCurrentProcess(), &targetAddr, &size, oldProtect, &oldProtect);
					DebugOutputF("[INT3] Re-installed 0xCC at LcGetOption %p", addr);
				}
				else {
					DebugOutputF("[INT3] Failed to re-install 0xCC at LcGetOption, status: 0x%X", status);
				}
			}
			else {
				if (checkCount == 1 || checkCount % 5 == 0) {
					DebugOutputF("[INT3] Check #%d: 0xCC still present at LcGetOption %p", checkCount, addr);
				}
			}
		}
		DebugOutput("[INT3] LcGetOption monitoring thread exiting after 30 seconds");
		return 0;
		}, entry, 0, NULL);
	return TRUE;
}
static BOOL HookLcCreateExByVEH_HK()
{
	PBYTE pEntry = FindLcCreateExByRVA_HK();
	if (!pEntry) {
		DebugOutput("[VEH] LcCreateEx RVA not found");
		return FALSE;
	}
	if (!IsExecutableAtAddress_HK(pEntry)) {
		DebugOutput("[VEH] LcCreateEx entry not executable");
		return FALSE;
	}

	DebugOutputF("[VEH] Installing VEH Page Guard Hook for LcCreateEx at %p", pEntry);

	// ʹ�� VEH Page Guard Hook ��� MinHook
	if (!HardBreakHook::InstallPageGuardHook((PVOID)pEntry, (PVOID)Hooked_LcCreateEx_HK, (PVOID*)&g_LcCreateEx_Orig)) {
		DebugOutput("[VEH] Failed to install Page Guard Hook for LcCreateEx");
		return FALSE;
	}

	DebugOutputF("[VEH] Successfully installed VEH Page Guard Hook for LcCreateEx at %p", pEntry);
	DebugOutputF("[VEH] Original function pointer: %p", g_LcCreateEx_Orig);

	return TRUE;
}

static BOOL HookLcCreateExByINT3_HK()
{
	PBYTE pEntry = FindLcCreateExByRVA_HK();
	if (!pEntry) {
		DebugOutput("[INT3] LcCreateEx RVA not found");
		return FALSE;
	}
	if (!IsExecutableAtAddress_HK(pEntry)) {
		DebugOutput("[INT3] LcCreateEx entry not executable");
		return FALSE;
	}

	DebugOutputF("[INT3] Installing INT3 Hook for LcCreateEx at %p", pEntry);

	// ʹ�� INT3 Hook ��� MinHook �� Page Guard
	if (!HardBreakHook::InstallInt3Hook((PVOID)pEntry, (PVOID)Hooked_LcCreateEx_HK, (PVOID*)&g_LcCreateEx_Orig)) {
		DebugOutput("[INT3] Failed to install INT3 Hook for LcCreateEx");
		return FALSE;
	}

	DebugOutputF("[INT3] Successfully installed INT3 Hook for LcCreateEx at %p", pEntry);
	DebugOutputF("[INT3] Original function pointer: %p", g_LcCreateEx_Orig);

	// ��֤INT3�Ƿ�д��ɹ�
	BYTE currentByte = *(PBYTE)pEntry;
	DebugOutputF("[INT3] Current byte at target: 0x%02X (should be 0xCC)", currentByte);

	// ��ӳ�������̼߳��0xCC�Ƿ񱻻�ԭ
	CreateThread(NULL, 0, [](LPVOID param) -> DWORD {
		PBYTE addr = (PBYTE)param;
		int checkCount = 0;

		while (checkCount < 30) { // ���30��
			Sleep(1000); // ÿ����һ��
			checkCount++;

			BYTE currentByte = *addr;
			if (currentByte != 0xCC) {
				DebugOutputF("[INT3] ANTI-HOOK DETECTED! Byte restored to 0x%02X at check #%d", currentByte, checkCount);

				// ��������д��0xCC
				DWORD oldProtect;
				PVOID targetAddr = addr;
				SIZE_T size = 1;
				NTSTATUS status = Sw3NtProtectVirtualMemory(GetCurrentProcess(), &targetAddr, &size, PAGE_EXECUTE_READWRITE, &oldProtect);
				if (NT_SUCCESS(status)) {
					*addr = 0xCC;
					targetAddr = addr;
					size = 1;
					Sw3NtProtectVirtualMemory(GetCurrentProcess(), &targetAddr, &size, oldProtect, &oldProtect);
					DebugOutputF("[INT3] Re-installed 0xCC at address %p", addr);
				}
				else {
					DebugOutputF("[INT3] Failed to re-install 0xCC, status: 0x%X", status);
				}
			}
			else {
				if (checkCount == 1 || checkCount % 5 == 0) {
					DebugOutputF("[INT3] Check #%d: 0xCC still present at %p", checkCount, addr);
				}
			}
		}

		DebugOutput("[INT3] Monitoring thread exiting after 30 seconds");
		return 0;
		}, pEntry, 0, NULL);

	return TRUE;
}

static BOOL HookLcCreateExByRVA_HK()
{
	PBYTE pEntry = FindLcCreateExByRVA_HK();
	if (!pEntry) { DebugOutput("[RVA] LcCreateEx RVA not found"); return FALSE; }
	if (!IsExecutableAtAddress_HK(pEntry)) { DebugOutput("[RVA] LcCreateEx entry not executable"); return FALSE; }

	// Manually set memory protection using syscall stub before MinHook
	// to bypass potential VirtualProtect hooks in packed executables
	DWORD oldProtect = 0;
	SIZE_T regionSize = 64; // Sufficient size for hook installation
	if (!ProtectMemoryWithSyscall_HK(pEntry, regionSize, PAGE_EXECUTE_READWRITE, &oldProtect)) {
		DebugOutput("[RVA] Failed to change memory protection with syscall");
		return FALSE;
	}
	DebugOutputF("[RVA] Memory protection changed: %p, size=%zu, old=0x%X", pEntry, regionSize, oldProtect);

	// Use MinHook directly on the discovered entry
	MH_STATUS createResult = MH_CreateHook((LPVOID)pEntry, (LPVOID)Hooked_LcCreateEx_HK, (LPVOID*)&g_LcCreateEx_Orig);
	if (createResult != MH_OK) {
		DebugOutputF("[RVA] MH_CreateHook failed for LcCreateEx: %d", createResult);
		// Restore original protection
		ProtectMemoryWithSyscall_HK(pEntry, regionSize, oldProtect, &oldProtect);
		return FALSE;
	}

	MH_STATUS enableResult = MH_EnableHook((LPVOID)pEntry);
	if (enableResult != MH_OK) {
		DebugOutputF("[RVA] MH_EnableHook failed for LcCreateEx: %d", enableResult);
		MH_RemoveHook((LPVOID)pEntry);
		// Restore original protection
		ProtectMemoryWithSyscall_HK(pEntry, regionSize, oldProtect, &oldProtect);
		return FALSE;
	}

	DebugOutputF("[RVA] Hooked static LcCreateEx @ %p", pEntry);

	// Verify hook installation by checking if the first bytes were modified
	BYTE firstBytes[16];
	memcpy(firstBytes, pEntry, 16);
	DebugOutputF("[RVA] Hook verification - first 16 bytes: %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X %02X",
		firstBytes[0], firstBytes[1], firstBytes[2], firstBytes[3], firstBytes[4], firstBytes[5], firstBytes[6], firstBytes[7],
		firstBytes[8], firstBytes[9], firstBytes[10], firstBytes[11], firstBytes[12], firstBytes[13], firstBytes[14], firstBytes[15]);

	// Check if MinHook successfully patched the function (should start with JMP instruction)
	if (firstBytes[0] != 0xE9 && firstBytes[0] != 0xFF) {
		DebugOutput("[RVA] WARNING: Hook may not be properly installed - no JMP instruction found");

		// Try to manually verify MinHook state
		void* pTarget = NULL;
		MH_STATUS status = MH_CreateHook((LPVOID)pEntry, (LPVOID)Hooked_LcCreateEx_HK, &pTarget);
		DebugOutputF("[RVA] MinHook re-check status: %d, existing target: %p", status, pTarget);
	}
	else {
		DebugOutput("[RVA] Hook appears to be properly installed");

		// Test if our hook function gets called
		DebugOutput("[RVA] Testing hook by attempting to call original function pointer");
		if (g_LcCreateEx_Orig) {
			DebugOutputF("[RVA] Original function pointer: %p", g_LcCreateEx_Orig);
		}
		else {
			DebugOutput("[RVA] WARNING: Original function pointer is NULL");
		}

		// Add a memory monitoring hook to detect if the memory gets restored
		CreateThread(NULL, 0, [](LPVOID param) -> DWORD {
			PBYTE addr = (PBYTE)param;
			Sleep(5000); // Wait 5 seconds
			BYTE currentBytes[5];
			memcpy(currentBytes, addr, 5);
			DebugOutputF("[RVA] Memory check after 5s: %02X %02X %02X %02X %02X",
				currentBytes[0], currentBytes[1], currentBytes[2], currentBytes[3], currentBytes[4]);
			return 0;
			}, pEntry, 0, NULL);
	}

	return TRUE;
}


// ------------------------------
// LAN server auto-discovery (handshake on 28474)
// ------------------------------
static BOOL GetPrimaryIPv4AndPrefix_HK(ULONG* pIpHostOrder, ULONG* pPrefixLen)
{
	if (!pIpHostOrder || !pPrefixLen) return FALSE;
	*pIpHostOrder = 0; *pPrefixLen = 0;
	ULONG flags = GAA_FLAG_SKIP_ANYCAST | GAA_FLAG_SKIP_MULTICAST | GAA_FLAG_SKIP_DNS_SERVER | GAA_FLAG_INCLUDE_PREFIX;
	ULONG cb = 0; GetAdaptersAddresses(AF_INET, flags, NULL, NULL, &cb);
	PIP_ADAPTER_ADDRESSES pAddrs = (PIP_ADAPTER_ADDRESSES)LocalAlloc(LMEM_ZEROINIT, cb);
	if (!pAddrs) return FALSE;
	DWORD rv = GetAdaptersAddresses(AF_INET, flags, NULL, pAddrs, &cb);
	if (rv != NO_ERROR) { LocalFree(pAddrs); return FALSE; }
	BOOL found = FALSE;
	for (PIP_ADAPTER_ADDRESSES p = pAddrs; p; p = p->Next) {
		if (p->OperStatus != IfOperStatusUp) continue;
		if (p->IfType == IF_TYPE_SOFTWARE_LOOPBACK) continue;
		if (p->IfType != IF_TYPE_ETHERNET_CSMACD && p->IfType != IF_TYPE_IEEE80211) continue;
		for (PIP_ADAPTER_UNICAST_ADDRESS ua = p->FirstUnicastAddress; ua; ua = ua->Next) {
			if (!ua->Address.lpSockaddr) continue;
			if (ua->Address.lpSockaddr->sa_family != AF_INET) continue;
			ULONG prefix = ua->OnLinkPrefixLength; if (prefix > 32) prefix = 24;
			SOCKADDR_IN* si = (SOCKADDR_IN*)ua->Address.lpSockaddr;
			ULONG ipHostOrder = ntohl(si->sin_addr.s_addr);
			if (ipHostOrder == 0) continue;
			*pIpHostOrder = ipHostOrder; *pPrefixLen = prefix; found = TRUE; break;
		}
		if (found) break;
	}
	LocalFree(pAddrs);
	return found;
}

static BOOL VerifyServerWithHandshake_HK(const char* ipStr, USHORT port, int timeoutMs)
{
	if (!ipStr) return FALSE;
	SOCKET s = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
	if (s == INVALID_SOCKET) return FALSE;
	u_long nonBlock = 1; if (ioctlsocket(s, FIONBIO, &nonBlock) == SOCKET_ERROR) { closesocket(s); return FALSE; }
	SOCKADDR_IN addr = { 0 }; addr.sin_family = AF_INET; addr.sin_port = htons(port);
	if (InetPtonA(AF_INET, ipStr, &addr.sin_addr) != 1) { closesocket(s); return FALSE; }
	int cr = connect(s, (SOCKADDR*)&addr, sizeof(addr)); (void)cr; int wsaErr = WSAGetLastError();
	if (wsaErr != WSAEWOULDBLOCK && wsaErr != WSAEINPROGRESS && wsaErr != WSAEISCONN) { closesocket(s); return wsaErr == WSAEISCONN; }
	fd_set wf; FD_ZERO(&wf); FD_SET(s, &wf); TIMEVAL tv; tv.tv_sec = 0; tv.tv_usec = timeoutMs * 1000;
	if (select(0, NULL, &wf, NULL, &tv) <= 0) { closesocket(s); return FALSE; }
	const char* req = "MEMPROCFS_HANDSHAKE_Q";
	if (send(s, req, (int)strlen(req), 0) == SOCKET_ERROR) { closesocket(s); return FALSE; }
	fd_set rf; FD_ZERO(&rf); FD_SET(s, &rf); tv.tv_sec = 0; tv.tv_usec = timeoutMs * 1000;
	if (select(0, &rf, NULL, NULL, &tv) <= 0) { closesocket(s); return FALSE; }
	char buf[64] = { 0 }; int n = recv(s, buf, sizeof(buf) - 1, 0); closesocket(s);
	if (n <= 0) { DebugOutputF("[RPC] Handshake no reply from %s:%u", ipStr, (unsigned)port); return FALSE; }
	BOOL ok = (strcmp(buf, "MEMPROCFS_HANDSHAKE_A") == 0);
	DebugOutputF("[RPC] Handshake %s:%u => %s", ipStr, (unsigned)port, ok ? "OK" : "FAIL");
	return ok;
}

static BOOL DiscoverServerIpInLAN_HK(char* outIp, size_t cchOut, USHORT port)
{
	if (!outIp || cchOut < 16) return FALSE;
	outIp[0] = '\0';
	WSADATA w; if (WSAStartup(MAKEWORD(2, 2), &w) != 0) { return FALSE; }
	ULONG ipHost = 0, prefix = 0;
	if (!GetPrimaryIPv4AndPrefix_HK(&ipHost, &prefix)) { WSACleanup(); return FALSE; }
	ULONG net24 = ipHost & 0xFFFFFF00UL;
	// Print local IP/prefix
	{
		IN_ADDR la; la.s_addr = htonl(ipHost);
		char lip[INET_ADDRSTRLEN] = { 0 };
		if (InetNtopA(AF_INET, &la, lip, sizeof(lip))) {
			DebugOutputF("[RPC] Local IP: %s /%u", lip, (unsigned)prefix);
		}
	}
	for (ULONG host = 1; host <= 254; ++host) {
		ULONG cand = net24 | host; if (cand == ipHost) continue;
		IN_ADDR a; a.s_addr = htonl(cand); char ipStr[INET_ADDRSTRLEN] = { 0 };
		if (!InetNtopA(AF_INET, &a, ipStr, sizeof(ipStr))) continue;
		if (VerifyServerWithHandshake_HK(ipStr, port, 200)) {
			strncpy_s(outIp, cchOut, ipStr, _TRUNCATE);
			DebugOutputF("[RPC] Discovered server: %s:%u", ipStr, (unsigned)port);
			WSACleanup();
			return TRUE;
		}
	}
	WSACleanup();
	return FALSE;
}

// ------------------------------
// Original function pointers
// ------------------------------
static HANDLE(*g_LcCreate_Orig)(PLC_CONFIG) = NULL;
// static HANDLE (*g_LcCreateEx_Orig)(PLC_CONFIG, PPLC_CONFIG_ERRORINFO) = NULL; // already declared above
static VOID(*g_LcClose_Orig)(HANDLE) = NULL;
static VOID(*g_LcMemFree_Orig)(PVOID) = NULL;
static BOOL(*g_LcAllocScatter1_Orig)(DWORD, PPMEM_SCATTER*) = NULL;
static BOOL(*g_LcAllocScatter2_Orig)(DWORD, PBYTE, DWORD, PPMEM_SCATTER*) = NULL;
static BOOL(*g_LcAllocScatter3_Orig)(PBYTE, PBYTE, DWORD, PBYTE, DWORD, PPMEM_SCATTER*) = NULL;
static VOID(*g_LcReadScatter_Orig)(HANDLE, DWORD, PPMEM_SCATTER) = NULL;
static BOOL(*g_LcRead_Orig)(HANDLE, QWORD, DWORD, PBYTE) = NULL;
static VOID(*g_LcWriteScatter_Orig)(HANDLE, DWORD, PPMEM_SCATTER) = NULL;
static BOOL(*g_LcWrite_Orig)(HANDLE, QWORD, DWORD, PBYTE) = NULL;
// static BOOL   (*g_LcGetOption_Orig)(HANDLE, QWORD, PQWORD) = NULL; // already declared above
static BOOL(*g_LcSetOption_Orig)(HANDLE, QWORD, QWORD) = NULL;
static BOOL(*g_LcCommand_Orig)(HANDLE, QWORD, DWORD, PBYTE, PBYTE*, PDWORD) = NULL;

// CreateEx phase gate to align timing with VMM variant
// (g_inCreateExGate declared above)

// Trampoline pointers for inline detours (VMM-style)
// (g_trampolineLcCreateEx, g_trampolineLcGetOption declared above)

// Hook monitoring and persistence
// (g_inlineDetourActive, g_lcCreateExAddr, g_lcGetOptionAddr, g_hookMonitorThread declared above)

// Forward declarations for hook monitoring
// (Hooked_LcCreateEx_Trampoline_HK, Hooked_LcGetOption_HK declared above)

// Lightweight trace to temp file for A/B comparison with VMM variant
static void TraceF(const char* tag, const char* fmt, ...)
{
	static FILE* fp = NULL;
	if (!fp) {
		char path[MAX_PATH] = { 0 };
		char temp[MAX_PATH] = { 0 };
		GetTempPathA(sizeof(temp), temp);
		_snprintf_s(path, sizeof(path), _TRUNCATE, "%sLcTrace.log", temp);
		fopen_s(&fp, path, "a");
	}
	if (!fp) return;
	SYSTEMTIME st; GetLocalTime(&st);
	fprintf(fp, "%04d-%02d-%02d %02d:%02d:%02d.%03d [%s] ",
		st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond, st.wMilliseconds, tag);
	va_list ap; va_start(ap, fmt);
	vfprintf(fp, fmt, ap);
	va_end(ap);
	fputc('\n', fp);
	fflush(fp);
}

// ------------------------------
// Hook implementations
// ------------------------------
static BOOL ShouldRewriteToRpc_HK(PLC_CONFIG pCfg)
{
	if (!pCfg) return FALSE;
	if (pCfg->szRemote[0]) {
		if (!_strnicmp(pCfg->szRemote, "rpc://", 6)) return FALSE; // already rpc
		if (!_strnicmp(pCfg->szRemote, "grpc://", 7)) return FALSE;
		if (!_strnicmp(pCfg->szRemote, "smb://", 6)) return FALSE;
	}
	return TRUE;
}

static void RewriteConfigToRpc_HK(PLC_CONFIG pCfg)
{
	if (!pCfg) return;
	char ip[16] = { 0 };
	const USHORT handshakePort = 28474;
	BOOL fFound = DiscoverServerIpInLAN_HK(ip, sizeof(ip), handshakePort);
	if (!fFound || ip[0] == '\0') {
		DebugOutput("[RPC] No server discovered in LAN.");
		MessageBoxW(NULL, L"δ�ҵ�������", L"��ʾ", MB_ICONERROR | MB_OK);
		return;
	}
	char remote[128] = { 0 };
	sprintf_s(remote, sizeof(remote), "rpc://insecure:%s:nocompress", ip);
	DebugOutputF("[RPC] Rewriting to device=rpc, remote=%s", remote);
	// Env override: remote-only rewrite mode
	char mode[32] = { 0 };
	do {
		DWORD n = GetEnvironmentVariableA("LEECH_REWRITE_MODE", mode, (DWORD)sizeof(mode));
		if (n == 0 || n >= sizeof(mode)) break;
		for (DWORD i = 0; i < n; ++i) { if (mode[i] >= 'A' && mode[i] <= 'Z') mode[i] += 32; }
		if (strstr(mode, "remote-only") || strstr(mode, "remoteonly")) {
			strcpy_s(pCfg->szRemote, sizeof(pCfg->szRemote), remote);
			DebugOutput("[RPC] Rewrite mode: remote-only");
			return;
		}
	} while (0);
	strcpy_s(pCfg->szDevice, sizeof(pCfg->szDevice), "rpc");
	strcpy_s(pCfg->szRemote, sizeof(pCfg->szRemote), remote);
}

static HANDLE Hooked_LcCreate_HK(PLC_CONFIG pCfg)
{
	DumpLcConfig("LcCreate:IN", pCfg);
	LC_CONFIG cfgBackup = { 0 };
	if (pCfg) { memcpy(&cfgBackup, pCfg, sizeof(LC_CONFIG)); }
	if (ShouldRewriteToRpc_HK(pCfg)) { RewriteConfigToRpc_HK(pCfg); }
	DumpLcConfig("LcCreate:REWRITE", pCfg);
	DumpSyntheticArgv("LcCreate:ARGV", pCfg);
	HANDLE h = NULL;
	__try {
		h = g_LcCreate_Orig ? g_LcCreate_Orig(pCfg) : NULL;
	}
	__except (EXCEPTION_EXECUTE_HANDLER) {
		DWORD ec = GetExceptionCode();
		DebugOutputF("[LcCreate] EXCEPTION: 0x%08X. Fallback to original cfg", ec);
		h = g_LcCreate_Orig ? g_LcCreate_Orig(&cfgBackup) : NULL;
	}
	DebugOutputF("[LcCreate] out: handle=%p", h);
	return h;
}

// VMM-style trampoline Hook (no stack frame issues)
static HANDLE Hooked_LcCreateEx_Trampoline_HK(PLC_CONFIG pCfg, PPLC_CONFIG_ERRORINFO ppErr)
{
	DebugOutput("=== LcCreateEx TRAMPOLINE HOOK CALLED ===");
	DebugOutputF("[LcCreateEx] Hook entry point reached! pCfg=%p, ppErr=%p", pCfg, ppErr);
	DumpLcConfig("LcCreateEx:IN", pCfg);
	LC_CONFIG cfgBackup = { 0 };
	if (pCfg) { memcpy(&cfgBackup, pCfg, sizeof(LC_CONFIG)); }
	if (ShouldRewriteToRpc_HK(pCfg)) { RewriteConfigToRpc_HK(pCfg); }
	DumpLcConfig("LcCreateEx:REWRITE", pCfg);
	DumpSyntheticArgv("LcCreateEx:ARGV", pCfg);
	HANDLE h = NULL;
	TraceF("LcCreateEx", "BEGIN pCfg=%p", pCfg);

	// Mark create gate active to align timing with VMM variant
	InterlockedExchange(&g_inCreateExGate, 1);

	// Call original function via trampoline (VMM-style, no stack frame issues)
	// Call original function via MinHook trampoline
	typedef HANDLE(*LcCreateExFunc)(PLC_CONFIG, PPLC_CONFIG_ERRORINFO);
	LcCreateExFunc originalFunc = g_LcCreateEx_Orig;
	if (originalFunc) {
		DebugOutputF("[MinHook] Calling original LcCreateEx via trampoline at %p", originalFunc);
		
		__try {
			h = originalFunc(pCfg, ppErr);
			DebugOutputF("[MinHook] Original LcCreateEx returned: %p", h);
		} __except(EXCEPTION_EXECUTE_HANDLER) {
			DWORD ec = GetExceptionCode();
			DebugOutputF("[LcCreateEx] EXCEPTION: 0x%08X", ec);
			TraceF("LcCreateEx", "EXCEPTION: 0x%08X", ec);
			h = NULL;
		}
	} else {
		DebugOutput("[MinHook] ERROR: No trampoline available for LcCreateEx - g_LcCreateEx_Orig is NULL");
	}
	

	// Clear create gate
	InterlockedExchange(&g_inCreateExGate, 0);

	DebugOutputF("[LcCreateEx] out: handle=%p", h);
	TraceF("LcCreateEx", "END handle=%p", h);
	return h;
}

// Keep old INT3 implementation as backup
static HANDLE Hooked_LcCreateEx_HK(PLC_CONFIG pCfg, PPLC_CONFIG_ERRORINFO ppErr)
{
	DebugOutput("=== LcCreateEx HOOK CALLED ==");
		DumpLcConfig("LcCreateEx:IN", pCfg);
		LC_CONFIG cfgBackup = { 0 };
		if (pCfg) { memcpy(&cfgBackup, pCfg, sizeof(LC_CONFIG)); }
		if (ShouldRewriteToRpc_HK(pCfg)) { RewriteConfigToRpc_HK(pCfg); }
		DumpLcConfig("LcCreateEx:REWRITE", pCfg);
		DumpSyntheticArgv("LcCreateEx:ARGV", pCfg);
		HANDLE h = NULL;
		TraceF("LcCreateEx", "BEGIN pCfg=%p", pCfg);

		// Global re-entrancy guard to mirror VMM path
		static volatile LONG s_inCreateEx = 0;
		if (InterlockedCompareExchange(&s_inCreateEx, 1, 0) != 0) {
			// Nested call: pass-through with temporary INT3 removal only
			PVOID pEntryNested = (PVOID)g_LcCreateEx_Orig;
			PageGuardHookInfo* hiNested = HardBreakHook::FindInt3Hook(pEntryNested);
			if (hiNested) {
				DWORD oldProtN = 0; PVOID addrN = pEntryNested; SIZE_T sizeN = 1;
				if (NT_SUCCESS(Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addrN, &sizeN, PAGE_EXECUTE_READWRITE, &oldProtN))) {
					*(volatile BYTE*)pEntryNested = hiNested->originalBytes[0];
					FlushInstructionCache(GetCurrentProcess(), pEntryNested, 1);
					addrN = pEntryNested; sizeN = 1; Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addrN, &sizeN, oldProtN, &oldProtN);
				}
			}
			h = g_LcCreateEx_Orig ? g_LcCreateEx_Orig(pCfg, ppErr) : NULL;
			if (hiNested) {
				DWORD oldProtN2 = 0; PVOID addrN2 = pEntryNested; SIZE_T sizeN2 = 1;
				if (NT_SUCCESS(Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addrN2, &sizeN2, PAGE_EXECUTE_READWRITE, &oldProtN2))) {
					*(volatile BYTE*)pEntryNested = 0xCC;
					FlushInstructionCache(GetCurrentProcess(), pEntryNested, 1);
					addrN2 = pEntryNested; sizeN2 = 1; Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addrN2, &sizeN2, oldProtN2, &oldProtN2);
				}
			}
			DebugOutputF("[LcCreateEx] out(nested): handle=%p", h);
			return h;
		}

		// Mark create gate active to pause any background anti-restore monitors
		InterlockedExchange(&g_inCreateExGate, 1);

		// INT3 re-entrancy protection: temporarily clear INT3 (0xCC) at entry before calling original
		PVOID pEntry = (PVOID)g_LcCreateEx_Orig;
		PageGuardHookInfo* hi = HardBreakHook::FindInt3Hook(pEntry);
		if (hi) {
			// Write back original first byte
			DWORD oldProt = 0; PVOID addr = pEntry; SIZE_T size = 1;
			NTSTATUS st = Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr, &size, PAGE_EXECUTE_READWRITE, &oldProt);
			if (NT_SUCCESS(st)) {
				*(volatile BYTE*)pEntry = hi->originalBytes[0];
				FlushInstructionCache(GetCurrentProcess(), pEntry, 1);
				// Restore protection
				addr = pEntry; size = 1; Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr, &size, oldProt, &oldProt);
				DebugOutput("[INT3] Temporarily removed 0xCC before calling original LcCreateEx");
			}
		}

		__try {
			h = g_LcCreateEx_Orig ? g_LcCreateEx_Orig(pCfg, ppErr) : NULL;
		}
		__except (EXCEPTION_EXECUTE_HANDLER) {
			DWORD ec = GetExceptionCode();
			DebugOutputF("[LcCreateEx] EXCEPTION: 0x%08X", ec);
			TraceF("LcCreateEx", "EXCEPTION: 0x%08X", ec);
			h = NULL; // single-call semantics; no fallback call
		}

		// Re-install INT3 after original returns
		if (hi) {
			DWORD oldProt2 = 0; PVOID addr2 = pEntry; SIZE_T size2 = 1;
			NTSTATUS st2 = Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr2, &size2, PAGE_EXECUTE_READWRITE, &oldProt2);
			if (NT_SUCCESS(st2)) {
				*(volatile BYTE*)pEntry = 0xCC;
				FlushInstructionCache(GetCurrentProcess(), pEntry, 1);
				addr2 = pEntry; size2 = 1; Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr2, &size2, oldProt2, &oldProt2);
				DebugOutput("[INT3] Re-installed 0xCC after original LcCreateEx returned");
			}
		}
		DebugOutputF("[LcCreateEx] out: handle=%p", h);
		TraceF("LcCreateEx", "END handle=%p", h);
		InterlockedExchange(&s_inCreateEx, 0);
		return h;
	}

	static VOID Hooked_LcClose_HK(HANDLE h)
	{
		DebugOutputF("[LcClose] h=%p", h);
		if (g_LcClose_Orig) g_LcClose_Orig(h);
	}

	static VOID Hooked_LcMemFree_HK(PVOID p)
	{
		DebugOutputF("[LcMemFree] p=%p", p);
		if (g_LcMemFree_Orig) g_LcMemFree_Orig(p);
	}

	static BOOL Hooked_LcAllocScatter1_HK(DWORD c, PPMEM_SCATTER * ppp)
	{
		DebugOutputF("[LcAllocScatter1] c=%u", (unsigned)c);
		return g_LcAllocScatter1_Orig ? g_LcAllocScatter1_Orig(c, ppp) : FALSE;
	}

	static BOOL Hooked_LcAllocScatter2_HK(DWORD cb, PBYTE pb, DWORD c, PPMEM_SCATTER * ppp)
	{
		DebugOutputF("[LcAllocScatter2] cb=%u c=%u pb=%p", (unsigned)cb, (unsigned)c, pb);
		return g_LcAllocScatter2_Orig ? g_LcAllocScatter2_Orig(cb, pb, c, ppp) : FALSE;
	}

	static BOOL Hooked_LcAllocScatter3_HK(PBYTE pf, PBYTE pl, DWORD cb, PBYTE pb, DWORD c, PPMEM_SCATTER * ppp)
	{
		DebugOutputF("[LcAllocScatter3] cb=%u c=%u pf=%p pl=%p pb=%p", (unsigned)cb, (unsigned)c, pf, pl, pb);
		return g_LcAllocScatter3_Orig ? g_LcAllocScatter3_Orig(pf, pl, cb, pb, c, ppp) : FALSE;
	}

	static VOID Hooked_LcReadScatter_HK(HANDLE h, DWORD c, PPMEM_SCATTER pp)
	{
		unsigned logc = c > 4 ? 4 : c;
		DebugOutputF("[LcReadScatter] h=%p c=%u", h, (unsigned)c);
		for (unsigned i = 0; i < logc; ++i) {
			PMEM_SCATTER p = pp[i]; if (!p) break;
			DebugOutputF("  MEM[%u]: A=0x%llX cb=%u", i, (unsigned long long)p->qwA, (unsigned)p->cb);
		}
		if (g_LcReadScatter_Orig) g_LcReadScatter_Orig(h, c, pp);
	}

	static BOOL Hooked_LcRead_HK(HANDLE h, QWORD pa, DWORD cb, PBYTE pb)
	{
		DebugOutputF("[LcRead] h=%p pa=0x%llX cb=%u pb=%p", h, (unsigned long long)pa, (unsigned)cb, pb);
		return g_LcRead_Orig ? g_LcRead_Orig(h, pa, cb, pb) : FALSE;
	}

	static VOID Hooked_LcWriteScatter_HK(HANDLE h, DWORD c, PPMEM_SCATTER pp)
	{
		unsigned logc = c > 4 ? 4 : c;
		DebugOutputF("[LcWriteScatter] h=%p c=%u", h, (unsigned)c);
		for (unsigned i = 0; i < logc; ++i) {
			PMEM_SCATTER p = pp[i]; if (!p) break;
			DebugOutputF("  MEM[%u]: A=0x%llX cb=%u", i, (unsigned long long)p->qwA, (unsigned)p->cb);
		}
		if (g_LcWriteScatter_Orig) g_LcWriteScatter_Orig(h, c, pp);
	}

	static BOOL Hooked_LcWrite_HK(HANDLE h, QWORD pa, DWORD cb, PBYTE pb)
	{
		DebugOutputF("[LcWrite] h=%p pa=0x%llX cb=%u pb=%p", h, (unsigned long long)pa, (unsigned)cb, pb);
		return g_LcWrite_Orig ? g_LcWrite_Orig(h, pa, cb, pb) : FALSE;
	}

	static BOOL Hooked_LcGetOption_HK(HANDLE h, QWORD f, PQWORD pv)
	{
		const char* name = LcOptName_HK(f);
		DebugOutputF("[LcGetOption] h=%p f=%s(0x%llX)", h, name, (unsigned long long)f);
		TraceF("LcGetOption", "h=%p f=%s(0x%llX)", h, name, (unsigned long long)f);

		// If we're inside LcCreateEx and using trampoline, call trampoline directly
		if (InterlockedCompareExchange(&g_inCreateExGate, 0, 0) != 0) {
			if (g_trampolineLcGetOption) {
				// Use trampoline for clean call (VMM-style)
				typedef BOOL(*LcGetOptionFunc)(HANDLE, QWORD, PQWORD);
				LcGetOptionFunc originalFunc = (LcGetOptionFunc)g_trampolineLcGetOption;
				return originalFunc(h, f, pv);
			}
			else {
				// Fallback to INT3 handling for backward compatibility
				PVOID pEntryFast = (PVOID)g_LcGetOption_Orig;
				PageGuardHookInfo* hiFast = HardBreakHook::FindInt3Hook(pEntryFast);
				if (hiFast) {
					DWORD op = 0; PVOID ad = pEntryFast; SIZE_T sz = 1;
					if (NT_SUCCESS(Sw3NtProtectVirtualMemory(GetCurrentProcess(), &ad, &sz, PAGE_EXECUTE_READWRITE, &op))) {
						*(volatile BYTE*)pEntryFast = hiFast->originalBytes[0];
						FlushInstructionCache(GetCurrentProcess(), pEntryFast, 1);
						ad = pEntryFast; sz = 1; Sw3NtProtectVirtualMemory(GetCurrentProcess(), &ad, &sz, op, &op);
					}
				}
				BOOL rr = g_LcGetOption_Orig ? g_LcGetOption_Orig(h, f, pv) : FALSE;
				if (hiFast) {
					DWORD op2 = 0; PVOID ad2 = pEntryFast; SIZE_T sz2 = 1;
					if (NT_SUCCESS(Sw3NtProtectVirtualMemory(GetCurrentProcess(), &ad2, &sz2, PAGE_EXECUTE_READWRITE, &op2))) {
						*(volatile BYTE*)pEntryFast = 0xCC;
						FlushInstructionCache(GetCurrentProcess(), pEntryFast, 1);
						ad2 = pEntryFast; sz2 = 1; Sw3NtProtectVirtualMemory(GetCurrentProcess(), &ad2, &sz2, op2, &op2);
					}
				}
				return rr;
			}
		}

		// Normal case: call via trampoline if available, otherwise use INT3 fallback
		BOOL r = FALSE;
		if (g_trampolineLcGetOption) {
			// Use trampoline for clean call (VMM-style)
			typedef BOOL(*LcGetOptionFunc)(HANDLE, QWORD, PQWORD);
			LcGetOptionFunc originalFunc = (LcGetOptionFunc)g_trampolineLcGetOption;
			__try {
				r = originalFunc(h, f, pv);
			}
			__except (EXCEPTION_EXECUTE_HANDLER) {
				DWORD ec = GetExceptionCode();
				DebugOutputF("[LcGetOption] EXCEPTION: 0x%08X", ec);
				TraceF("LcGetOption", "EXCEPTION: 0x%08X", ec);
				r = FALSE;
			}
		}
		else {
			// Fallback: INT3 re-entrancy protection for LcGetOption
			PVOID pEntry = (PVOID)g_LcGetOption_Orig;
			PageGuardHookInfo* hi = HardBreakHook::FindInt3Hook(pEntry);
			if (hi) {
				DWORD oldProt = 0; PVOID addr = pEntry; SIZE_T size = 1;
				NTSTATUS st = Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr, &size, PAGE_EXECUTE_READWRITE, &oldProt);
				if (NT_SUCCESS(st)) {
					*(volatile BYTE*)pEntry = hi->originalBytes[0];
					FlushInstructionCache(GetCurrentProcess(), pEntry, 1);
					addr = pEntry; size = 1; Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr, &size, oldProt, &oldProt);
					DebugOutput("[INT3] Temporarily removed 0xCC before calling original LcGetOption");
				}
			}

			__try {
				r = g_LcGetOption_Orig ? g_LcGetOption_Orig(h, f, pv) : FALSE;
			}
			__except (EXCEPTION_EXECUTE_HANDLER) {
				DWORD ec = GetExceptionCode();
				DebugOutputF("[LcGetOption] EXCEPTION: 0x%08X", ec);
				TraceF("LcGetOption", "EXCEPTION: 0x%08X", ec);
				r = FALSE;
			}

			// Re-install INT3 after original returns
			if (hi) {
				DWORD oldProt2 = 0; PVOID addr2 = pEntry; SIZE_T size2 = 1;
				NTSTATUS st2 = Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr2, &size2, PAGE_EXECUTE_READWRITE, &oldProt2);
				if (NT_SUCCESS(st2)) {
					*(volatile BYTE*)pEntry = 0xCC;
					FlushInstructionCache(GetCurrentProcess(), pEntry, 1);
					addr2 = pEntry; size2 = 1; Sw3NtProtectVirtualMemory(GetCurrentProcess(), &addr2, &size2, oldProt2, &oldProt2);
					DebugOutput("[INT3] Re-installed 0xCC after original LcGetOption returned");
				}
			}
		}

		if (r && pv) {
			DebugOutputF("[LcGetOption] -> 0x%llX", (unsigned long long)(*pv));
			TraceF("LcGetOption", "RET=1 val=0x%llX", (unsigned long long)(*pv));
		}
		else {
			TraceF("LcGetOption", "RET=0");
		}
		return r;
	}

	static BOOL Hooked_LcSetOption_HK(HANDLE h, QWORD f, QWORD v)
	{
		const char* name = LcOptName_HK(f);
		DebugOutputF("[LcSetOption] h=%p f=%s(0x%llX) v=0x%llX", h, name, (unsigned long long)f, (unsigned long long)v);
		return g_LcSetOption_Orig ? g_LcSetOption_Orig(h, f, v) : FALSE;
	}

	static void DumpHexPrefix(const char* tag, const void* p, DWORD cb, DWORD max)
	{
		if (!p || cb == 0) { DebugOutputF("[%s] (null)", tag); return; }
		char line[256] = { 0 }; size_t o = 0; DWORD n = cb < max ? cb : max; const unsigned char* b = (const unsigned char*)p;
		for (DWORD i = 0; i < n&& o + 3 < sizeof(line); ++i) { int w = _snprintf_s(line + o, sizeof(line) - o, _TRUNCATE, "%02X ", b[i]); if (w > 0) o += (size_t)w; }
		DebugOutputF("[%s] %u bytes: %s%s", tag, (unsigned)cb, line, (cb > n ? "..." : ""));
	}

	static BOOL Hooked_LcCommand_HK(HANDLE h, QWORD cmd, DWORD cbIn, PBYTE pbIn, PBYTE * ppbOut, PDWORD pcbOut)
	{
		const char* name = LcCmdName_HK(cmd);
		DebugOutputF("[LcCommand] h=%p cmd=%s(0x%016llX) cbIn=%u pbIn=%p", h, name, (unsigned long long)cmd, (unsigned)cbIn, pbIn);
		if (pbIn && cbIn) DumpHexPrefix("LcCommand:IN", pbIn, cbIn, 64);
		BOOL r = g_LcCommand_Orig ? g_LcCommand_Orig(h, cmd, cbIn, pbIn, ppbOut, pcbOut) : FALSE;
		if (r && ppbOut && pcbOut && *ppbOut && *pcbOut) DumpHexPrefix("LcCommand:OUT", *ppbOut, *pcbOut, 64);
		DebugOutputF("[LcCommand] -> %s (cbOut=%u)", r ? "TRUE" : "FALSE", (unsigned)(pcbOut ? *pcbOut : 0));
		return r;
	}


	static volatile LONG g_lcHooksInstalled = 0;

	// ------------------------------
	// IAT Hooking for LcCreate / LcCreateEx
	// ------------------------------
	static BOOL InstallIATForModule_HK(HMODULE hModule)
	{
		if (!hModule) return FALSE;
		PBYTE base = (PBYTE)hModule;
		PIMAGE_DOS_HEADER dos = (PIMAGE_DOS_HEADER)base;
		if (dos->e_magic != IMAGE_DOS_SIGNATURE) return FALSE;
		PIMAGE_NT_HEADERS nt = (PIMAGE_NT_HEADERS)(base + dos->e_lfanew);
		if (nt->Signature != IMAGE_NT_SIGNATURE) return FALSE;
		DWORD impRva = nt->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_IMPORT].VirtualAddress;
		if (!impRva) return FALSE;
		PIMAGE_IMPORT_DESCRIPTOR imp = (PIMAGE_IMPORT_DESCRIPTOR)(base + impRva);
		BOOL hooked = FALSE;
		for (; imp->Name; ++imp) {
			LPCSTR dllName = (LPCSTR)(base + imp->Name);
			if (!dllName) continue;
			char dllLower[MAX_PATH] = { 0 };
			strncpy_s(dllLower, dllName, _TRUNCATE);
			_strlwr_s(dllLower, sizeof(dllLower));
			if (strstr(dllLower, "leechcore.dll") == NULL) continue;
			PIMAGE_THUNK_DATA thunk = (PIMAGE_THUNK_DATA)(base + imp->FirstThunk);
			PIMAGE_THUNK_DATA origThunk = imp->OriginalFirstThunk ? (PIMAGE_THUNK_DATA)(base + imp->OriginalFirstThunk) : thunk;
			for (; thunk->u1.Function; ++thunk, ++origThunk) {
				BOOL isOrdinal = (origThunk->u1.Ordinal & IMAGE_ORDINAL_FLAG) != 0;
				if (isOrdinal) continue;
				PIMAGE_IMPORT_BY_NAME ibn = (PIMAGE_IMPORT_BY_NAME)(base + origThunk->u1.AddressOfData);
				LPCSTR fn = (LPCSTR)ibn->Name;
				if (!fn) continue;
				PVOID targetHook = NULL;
				if (strcmp(fn, "LcCreate") == 0) targetHook = (PVOID)Hooked_LcCreate_HK;
				else if (strcmp(fn, "LcCreateEx") == 0) targetHook = (PVOID)Hooked_LcCreateEx_HK;
				else continue;
				// Save original pointer once
				if (strcmp(fn, "LcCreate") == 0 && !g_LcCreate_Orig) g_LcCreate_Orig = (HANDLE(*)(PLC_CONFIG))thunk->u1.Function;
				if (strcmp(fn, "LcCreateEx") == 0 && !g_LcCreateEx_Orig) g_LcCreateEx_Orig = (HANDLE(*)(PLC_CONFIG, PPLC_CONFIG_ERRORINFO))thunk->u1.Function;
				DWORD oldProt = 0;
				if (!ProtectMemoryWithSyscall_HK(&thunk->u1.Function, sizeof(PVOID), PAGE_READWRITE, &oldProt)) continue;
				thunk->u1.Function = (ULONG_PTR)targetHook;
				DWORD tmpProt = 0; ProtectMemoryWithSyscall_HK(&thunk->u1.Function, sizeof(PVOID), oldProt, &tmpProt);
				FlushInstructionCache(GetCurrentProcess(), &thunk->u1.Function, sizeof(PVOID));
				DebugOutputF("[IAT] %p: %s -> %p (orig=%p)", hModule, fn, targetHook,
					(strcmp(fn, "LcCreate") == 0) ? (PVOID)g_LcCreate_Orig : (PVOID)g_LcCreateEx_Orig);
				hooked = TRUE;
			}
		}
		return hooked;
	}

	static BOOL HookWithMinHook_LC(HMODULE hLC)
	{
		if (!hLC) return FALSE;
		struct HookSpec { const char* name; LPVOID hook; LPVOID* porig; };
		FARPROC addr;
		int ok = 0, total = 0;
		HookSpec specs[] = {
			// ��չ��ͬʱ��ס LcCreateEx / LcGetOption / LcCommand �������
			{"LcCreateEx", (LPVOID)Hooked_LcCreateEx_HK, (LPVOID*)&g_LcCreateEx_Orig},
			{"LcGetOption", (LPVOID)Hooked_LcGetOption_HK, (LPVOID*)&g_LcGetOption_Orig},

		};
		for (size_t i = 0; i < sizeof(specs) / sizeof(specs[0]); ++i) {
			total++;
			addr = GetProcAddressPE_HK(hLC, specs[i].name);
			if (!addr) { DebugOutputF("[MH] Missing export: %s", specs[i].name); continue; }

			// Pre-apply memory protection using syscall stub to bypass VirtualProtect hooks
			DWORD oldProtect = 0;
			SIZE_T regionSize = 64;
			if (!ProtectMemoryWithSyscall_HK((PBYTE)addr, regionSize, PAGE_EXECUTE_READWRITE, &oldProtect)) {
				DebugOutputF("[MH] Failed to change memory protection for %s with syscall", specs[i].name);
				continue;
			}
			DebugOutputF("[MH] Memory protection changed for %s: %p, old=0x%X", specs[i].name, addr, oldProtect);

			MH_STATUS s1 = MH_CreateHook((LPVOID)addr, specs[i].hook, specs[i].porig);
			if (s1 != MH_OK) {
				DebugOutputF("[MH] CreateHook failed %s: %d", specs[i].name, (int)s1);
				// Restore original protection on failure
				ProtectMemoryWithSyscall_HK((PBYTE)addr, regionSize, oldProtect, &oldProtect);
				continue;
			}
			MH_STATUS s2 = MH_EnableHook((LPVOID)addr);
			if (s2 != MH_OK) {
				DebugOutputF("[MH] EnableHook failed %s: %d", specs[i].name, (int)s2);
				MH_RemoveHook((LPVOID)addr);
				// Restore original protection on failure
				ProtectMemoryWithSyscall_HK((PBYTE)addr, regionSize, oldProtect, &oldProtect);
				continue;
			}
			ok++;
			DebugOutputF("[MH] Hooked %s @ %p", specs[i].name, addr);

			// Verify hook installation
			BYTE firstBytes[5];
			memcpy(firstBytes, addr, 5);
			DebugOutputF("[MH] Hook verification %s - first 5 bytes: %02X %02X %02X %02X %02X",
				specs[i].name, firstBytes[0], firstBytes[1], firstBytes[2], firstBytes[3], firstBytes[4]);
			if (firstBytes[0] != 0xE9 && firstBytes[0] != 0xFF) {
				DebugOutputF("[MH] WARNING: Hook %s may not be properly installed", specs[i].name);
			}
		}
		DebugOutputF("[MH] Hook summary: %d/%d installed", ok, total);
		return ok >= 1; // ���� LcCreateEx Ӧ��װ�ɹ�
	}

	// Helper function to try inline detour on RVA-based addresses
	static BOOL TryInlineDetourByRVA_HK()
	{
		HMODULE hExe = FindModuleByNamePEB_HK(NULL);
		if (!hExe) {
			DebugOutput("[InlineDetour] Main module not found for RVA-based hooks");
			return FALSE;
		}

		PBYTE base = (PBYTE)hExe;
		// Validate image headers
		PIMAGE_DOS_HEADER dos = (PIMAGE_DOS_HEADER)base;
		if (dos->e_magic != IMAGE_DOS_SIGNATURE) return FALSE;
		PIMAGE_NT_HEADERS nt = (PIMAGE_NT_HEADERS)(base + dos->e_lfanew);
		if (nt->Signature != IMAGE_NT_SIGNATURE) return FALSE;

		BOOL inlineOk = FALSE;

		// Try LcCreateEx at known RVA
		DWORD lcCreateExRVA = 0xDAF9F0;
		if (lcCreateExRVA < nt->OptionalHeader.SizeOfImage) {
			PBYTE pLcCreateEx = base + lcCreateExRVA;
			DebugOutputF("[InlineDetour] Trying LcCreateEx at RVA 0x%X (%p)", lcCreateExRVA, pLcCreateEx);

			if (IsExecutableAtAddress_HK(pLcCreateEx)) {
				if (InstallInlineDetourGeneric_HK(pLcCreateEx, (PVOID)Hooked_LcCreateEx_Trampoline_HK, &g_trampolineLcCreateEx)) {
					DebugOutput("[InlineDetour] Successfully installed LcCreateEx inline detour via RVA");
					g_lcCreateExAddr = pLcCreateEx; // Store for monitoring
					inlineOk = TRUE;
				}
				else {
					DebugOutput("[InlineDetour] Failed to install LcCreateEx inline detour via RVA");
				}
			}
			else {
				DebugOutput("[InlineDetour] LcCreateEx RVA address not executable");
			}
		}

		// Try LcGetOption at known RVA  
		DWORD lcGetOptionRVA = 0xDB14F0;
		if (lcGetOptionRVA < nt->OptionalHeader.SizeOfImage) {
			PBYTE pLcGetOption = base + lcGetOptionRVA;
			DebugOutputF("[InlineDetour] Trying LcGetOption at RVA 0x%X (%p)", lcGetOptionRVA, pLcGetOption);

			if (IsExecutableAtAddress_HK(pLcGetOption)) {
				if (InstallInlineDetourGeneric_HK(pLcGetOption, (PVOID)Hooked_LcGetOption_HK, &g_trampolineLcGetOption)) {
					DebugOutput("[InlineDetour] Successfully installed LcGetOption inline detour via RVA");
					g_lcGetOptionAddr = pLcGetOption; // Store for monitoring
					inlineOk = TRUE;
				}
				else {
					DebugOutput("[InlineDetour] Failed to install LcGetOption inline detour via RVA");
				}
			}
			else {
				DebugOutput("[InlineDetour] LcGetOption RVA address not executable");
			}
		}

		return inlineOk;
	}

	static BOOL TryInstallLeechCoreHooks_HK()
	{
		if (InterlockedCompareExchange(&g_lcHooksInstalled, 1, 1)) return TRUE;
		HMODULE hLC = FindModuleByNamePEB_HK("leechcore.dll");

		if (!hLC) {
			DebugOutput("[Hook] leechcore.dll not loaded; trying RVA-based MinHook with syscall VirtualProtect");

			// Initialize MinHook with syscall-based VirtualProtect
			MH_Initialize();

			// Try RVA-based MinHook hooks
			HMODULE hExe = FindModuleByNamePEB_HK(NULL);
			if (hExe) {
				PBYTE base = (PBYTE)hExe;
				PIMAGE_DOS_HEADER dos = (PIMAGE_DOS_HEADER)base;
				if (dos->e_magic == IMAGE_DOS_SIGNATURE) {
					PIMAGE_NT_HEADERS nt = (PIMAGE_NT_HEADERS)(base + dos->e_lfanew);
					if (nt->Signature == IMAGE_NT_SIGNATURE) {
						// Try LcCreateEx at RVA 0xDAF9F0
						DWORD lcCreateExRVA = 0xDAF9F0;
						if (lcCreateExRVA < nt->OptionalHeader.SizeOfImage) {
							PBYTE pLcCreateEx = base + lcCreateExRVA;
							DebugOutputF("[MinHook] Trying LcCreateEx at RVA 0x%X (%p)", lcCreateExRVA, pLcCreateEx);

							MH_STATUS s1 = MH_CreateHook((LPVOID)pLcCreateEx, (LPVOID)Hooked_LcCreateEx_Trampoline_HK, (LPVOID*)&g_LcCreateEx_Orig);
							if (s1 == MH_OK) {
								MH_STATUS s2 = MH_EnableHook((LPVOID)pLcCreateEx);
								if (s2 == MH_OK) {
									DebugOutput("[MinHook] Successfully hooked LcCreateEx via RVA with syscall VirtualProtect");
									g_monitoredLcCreateExAddr = pLcCreateEx; // Store for monitoring
								}
							}
						}

						// Try LcGetOption at RVA 0xDB14F0  
						DWORD lcGetOptionRVA = 0xDB14F0;
						if (lcGetOptionRVA < nt->OptionalHeader.SizeOfImage) {
							PBYTE pLcGetOption = base + lcGetOptionRVA;
							DebugOutputF("[MinHook] Trying LcGetOption at RVA 0x%X (%p)", lcGetOptionRVA, pLcGetOption);

							MH_STATUS s1 = MH_CreateHook((LPVOID)pLcGetOption, (LPVOID)Hooked_LcGetOption_HK, (LPVOID*)&g_LcGetOption_Orig);
							if (s1 == MH_OK) {
								MH_STATUS s2 = MH_EnableHook((LPVOID)pLcGetOption);
								if (s2 == MH_OK) {
									DebugOutput("[MinHook] Successfully hooked LcGetOption via RVA with syscall VirtualProtect");
									g_monitoredLcGetOptionAddr = pLcGetOption; // Store for monitoring
								}
							}
						}
					}
				}
			}

			if (g_LcCreateEx_Orig || g_LcGetOption_Orig) {
				InterlockedExchange(&g_lcHooksInstalled, 1);

				// Start MinHook monitoring thread to detect and fix hook corruption
				if (g_monitoredLcCreateExAddr || g_monitoredLcGetOptionAddr) {
					InterlockedExchange(&g_minHookMonitorActive, 1);
					g_minHookMonitorThread = CreateThread(NULL, 0, MinHookMonitorThread_HK, NULL, 0, NULL);
					if (g_minHookMonitorThread) {
						DebugOutput("[Hook] Started MinHook monitoring thread");
					}
				}

				DebugOutput("[Hook] RVA-based MinHook hooks installed with syscall VirtualProtect");
				return TRUE;
			}

			return FALSE;
		}

		// Fallback to MinHook with syscall VirtualProtect for loaded leechcore.dll
		DebugOutput("[Hook] Using MinHook with syscall VirtualProtect for loaded leechcore.dll");
		MH_Initialize();
		if (HookWithMinHook_LC(hLC)) {
			InterlockedExchange(&g_lcHooksInstalled, 1);
			DebugOutput("[Hook] MinHook hooks installed for LcCreateEx/LcGetOption with syscall VirtualProtect");
			return TRUE;
		}
		DebugOutput("[Hook] MinHook install failed");
		return FALSE;
	}

	static DWORD WINAPI LeechCoreHookRetryThread_HK(LPVOID)
	{
		const int kMaxAttempts = 10;
		for (int i = 0; i < kMaxAttempts; ++i) {
			if (TryInstallLeechCoreHooks_HK()) return 0;
			Sleep(3000);
		}
		return 0;
	}

	extern "C" void InstallLeechCoreHooks()
	{
		if (TryInstallLeechCoreHooks_HK()) return;
		HANDLE h = CreateThread(NULL, 0, LeechCoreHookRetryThread_HK, NULL, 0, NULL);
		if (h) { CloseHandle(h); }
	}

	// ------------------------------
	// DllMain: init WinUSB forwarders + install LeechCore hooks
	// ------------------------------
	BOOL APIENTRY DllMain(HMODULE hModule, DWORD dwReason, LPVOID)
	{
		if (dwReason == DLL_PROCESS_ATTACH) {
			DisableThreadLibraryCalls(hModule);
			DebugOutput("[DLL] PROCESS_ATTACH");
			// Attach console if none exists to see stdout/stderr
			if (!GetConsoleWindow()) {
				if (AllocConsole()) {
					FILE* fp = nullptr;
					freopen_s(&fp, "CONOUT$", "w", stdout);
					freopen_s(&fp, "CONOUT$", "w", stderr);
					freopen_s(&fp, "CONIN$", "r", stdin);
					setvbuf(stdout, NULL, _IONBF, 0);
					setvbuf(stderr, NULL, _IONBF, 0);
					DebugOutput("[DLL] Console allocated");
				}
				else {
					DebugOutput("[DLL] AllocConsole failed");
				}
			}
			else {
				DebugOutput("[DLL] Console present");
			}
			if (LoadOriginalWinUsb_HK() && InitWinUsbForwarders_HK()) {
				MH_Initialize();
				InstallLeechCoreHooks();
				//MessageBox(NULL, L"test", L"test", MB_OK);
			}

		}
		if (dwReason == DLL_PROCESS_DETACH) {
			DebugOutput("[DLL] PROCESS_DETACH");

			// Stop MinHook monitoring
			InterlockedExchange(&g_minHookMonitorActive, 0);
			if (g_minHookMonitorThread) {
				WaitForSingleObject(g_minHookMonitorThread, 2000); // Wait up to 2 seconds
				CloseHandle(g_minHookMonitorThread);
				g_minHookMonitorThread = NULL;
			}

			// Stop inline detour monitoring
			InterlockedExchange(&g_inlineDetourActive, 0);
			if (g_hookMonitorThread) {
				WaitForSingleObject(g_hookMonitorThread, 2000); // Wait up to 2 seconds
				CloseHandle(g_hookMonitorThread);
				g_hookMonitorThread = NULL;
			}

			// Cleanup trampolines
			if (g_trampolineLcCreateEx) {
				VirtualFree(g_trampolineLcCreateEx, 0, MEM_RELEASE);
				g_trampolineLcCreateEx = NULL;
			}
			if (g_trampolineLcGetOption) {
				VirtualFree(g_trampolineLcGetOption, 0, MEM_RELEASE);
				g_trampolineLcGetOption = NULL;
			}

			MH_DisableHook(MH_ALL_HOOKS);
			MH_Uninitialize();
			if (g_hOriginalDll) { FreeLibrary(g_hOriginalDll); g_hOriginalDll = NULL; }
		}
		return TRUE;
	}


