// EXE entry point is compiled only when not building as Notepad++ plugin DLL.
#ifndef DLLMODE
#include <WinSock2.h>  // must be included before Windows.h
#include <WS2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#include <Windows.h>
#include <iostream>
#include <fstream>
#include <signal.h>
#include "../include/VMP.h"
#include "../include/CustomLeechServer.h"
#include "../include/liangzi.hpp"

// --- Handshake Server ---
static HANDLE g_hHandshakeThread = NULL;
static volatile BOOL g_fShutdownHandshake = FALSE;

static volatile BOOL g_fClientConnected = FALSE;
static char g_connectedClientInfo[256] = {0};

DWORD WINAPI HandshakeThread(LPVOID lpParam)
{
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        DBG_PRINTF("[Handshake] WSAStartup failed.\n");
        return 1;
    }

    SOCKET ListenSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (ListenSocket == INVALID_SOCKET) {
        DBG_PRINTF("[Handshake] Socket creation failed.\n");
        WSACleanup();
        return 1;
    }

    sockaddr_in service;
    service.sin_family = AF_INET;
    service.sin_addr.s_addr = INADDR_ANY;
    service.sin_port = htons(DEFAULT_GRPC_PORT + 1); // Listen on RPC Port + 1

    if (::bind(ListenSocket, (SOCKADDR*)&service, sizeof(service)) == SOCKET_ERROR) {
        DBG_PRINTF("[Handshake] Bind failed on port %d.\n", DEFAULT_GRPC_PORT + 1);
        closesocket(ListenSocket);
        WSACleanup();
        return 1;
    }

    if (listen(ListenSocket, 1) == SOCKET_ERROR) {
        DBG_PRINTF("[Handshake] Listen failed.\n");
        closesocket(ListenSocket);
        WSACleanup();
        return 1;
    }

    DBG_PRINTF("[Handshake] Server listening on port %d (等待第一个客户端).\n", DEFAULT_GRPC_PORT + 1);

    while (!g_fShutdownHandshake && !g_fClientConnected)
    {
        // Use select for non-blocking accept
        fd_set readSet;
        FD_ZERO(&readSet);
        FD_SET(ListenSocket, &readSet);
        timeval timeout;
        timeout.tv_sec = 1;  // 1 second timeout
        timeout.tv_usec = 0;

        int selectResult = select(0, &readSet, NULL, NULL, &timeout);
        if (selectResult == 0) { 
            continue; // Timeout, check shutdown flag
        }
        if (selectResult == SOCKET_ERROR) {
            break; // Error
        }

        SOCKET AcceptSocket = accept(ListenSocket, NULL, NULL);
        if (AcceptSocket == INVALID_SOCKET) {
            continue;
        }

        // 获取客户端地址信息
        sockaddr_in clientAddr;
        int clientAddrLen = sizeof(clientAddr);
        getpeername(AcceptSocket, (sockaddr*)&clientAddr, &clientAddrLen);
        char clientIp[INET_ADDRSTRLEN] = {0};
        InetNtopA(AF_INET, &clientAddr.sin_addr, clientIp, INET_ADDRSTRLEN);

        char recvbuf[64] = {0};
        const char* magicRequest = "MEMPROCFS_HANDSHAKE_Q";
        const char* magicResponse = "MEMPROCFS_HANDSHAKE_A";

        int iResult = recv(AcceptSocket, recvbuf, sizeof(recvbuf), 0);
        if (iResult > 0) {
            if (strcmp(recvbuf, magicRequest) == 0) {
                send(AcceptSocket, magicResponse, (int)strlen(magicResponse), 0);
                
                // 标记客户端已连接并记录信息
                g_fClientConnected = TRUE;
                snprintf(g_connectedClientInfo, sizeof(g_connectedClientInfo), 
                        "客户端IP: %s, 连接时间: %llu", clientIp, GetTickCount64());
                
                DBG_PRINTF("[Handshake] 客户端已连接: %s\n", clientIp);
                DBG_PRINTF("[Handshake] 关闭握手监听端口，服务器进入独占模式\n");
            }
        }
        closesocket(AcceptSocket);
    }

    closesocket(ListenSocket);
    WSACleanup();
    DBG_PRINTF("[Handshake] Server shutdown. %s\n", 
              g_fClientConnected ? "已绑定客户端" : "未绑定客户端");
    return 0;
}
// --- End Handshake Server ---



// Global flag for graceful shutdown
static BOOL g_fShutdown = FALSE;
static HANDLE g_hQuitEvent = NULL;

// Signal handler for graceful shutdown
BOOL WINAPI ConsoleCtrlHandler(DWORD dwCtrlType)
{
    switch (dwCtrlType) {
        case CTRL_C_EVENT:
        case CTRL_BREAK_EVENT:
        case CTRL_CLOSE_EVENT:
        case CTRL_SHUTDOWN_EVENT:
            std::cout << "\nShutdown signal received, stopping server..." << std::endl;
            g_fShutdown = TRUE;
            g_fShutdownHandshake = TRUE; // Signal handshake thread to stop
            if (g_hQuitEvent) SetEvent(g_hQuitEvent);
            return TRUE;
        default:
            return FALSE;
    }
}

int main(int argc, char* argv[])
{
    auto ReadAllTrimmed = [](const char* path) -> std::string {
        std::ifstream ifs(path, std::ios::binary);
        if (!ifs) return {};
        std::string s((std::istreambuf_iterator<char>(ifs)), std::istreambuf_iterator<char>());
        if (s.size() >= 3 && (unsigned char)s[0] == 0xEF && (unsigned char)s[1] == 0xBB && (unsigned char)s[2] == 0xBF) {
            s.erase(0, 3);
        }
        auto isws = [](char c) { return c == ' ' || c == '\t' || c == '\r' || c == '\n'; };
        size_t i = 0; while (i < s.size() && isws(s[i])) ++i;
        size_t j = s.size(); while (j > i && isws(s[j - 1])) --j;
        return s.substr(i, j - i);
    };

    CUSTOM_SERVER_CONFIG config;
    CustomLeechServer* pServer;
    
    VMProtectBegin("MainEntry");
	const auto init_ret = lz.init();
	if (!init_ret)
	{
        MessageBox(NULL, L"init faild!", L"", MB_ICONWARNING | MB_OK);
		return 0;
	}

    DBG_PRINTF("CustomLeechServer - MS-RPC Memory Access Server\n");
    DBG_PRINTF("==============================================\n");
    
    // Start Handshake Server Thread
    g_hHandshakeThread = CreateThread(NULL, 0, HandshakeThread, NULL, 0, NULL);
    if (!g_hHandshakeThread) {
        DBG_PRINTF("Failed to create handshake server thread.\n");
    }

    // Initialize with fixed configuration
    ZeroMemory(&config, sizeof(CUSTOM_SERVER_CONFIG));
    strcpy_s(config.szListenAddress, MAX_PATH_LENGTH, "0.0.0.0");  // Listen on all interfaces
    config.dwPort = DEFAULT_GRPC_PORT;  // Use default port 28473 (LeechCore standard)
    config.fInsecure = TRUE;  // Use insecure connection (no authentication)
    strcpy_s(config.szDriverName, MAX_PATH_LENGTH, "auto-generated");  // Driver name will be auto-generated
    
    DBG_PRINTF("Configuration:\n");
    DBG_PRINTF("  Listen Address: %s (all interfaces)\n", config.szListenAddress);
    DBG_PRINTF("  RPC Port: %lu (MS-RPC over TCP)\n", (unsigned long)config.dwPort);
    DBG_PRINTF("  Handshake Port: %lu (TCP, 一对一模式)\n", (unsigned long)(config.dwPort + 1));
    DBG_PRINTF("  Mode: 独占模式 (第一个客户端连接后关闭握手端口)\n");
    DBG_PRINTF("  Protocol: ncacn_ip_tcp (MS-RPC over TCP)\n");
    DBG_PRINTF("  Named Pipe: \\pipe\\CustomLeechServer (local connections)\n");
    DBG_PRINTF("  Security: %s\n", (config.fInsecure ? "Insecure" : "Secure"));
    DBG_PRINTF("  Driver: Auto-generated device name\n\n");
    
    // Set up console control handler and quit event for graceful shutdown
    g_hQuitEvent = CreateEventA(NULL, TRUE, FALSE, NULL);
    if (!SetConsoleCtrlHandler(ConsoleCtrlHandler, TRUE)) {
        DBG_PRINTF("Warning: Could not set console control handler\n");
    }
    
    // Get server instance
    pServer = CustomLeechServer::GetInstance();
    
    // Initialize server (driver name will be auto-generated)
    if (!pServer->Initialize(config.szListenAddress, config.dwPort, config.fInsecure, nullptr)) {
        DBG_PRINTF("Failed to initialize CustomLeechServer\n");
        DBG_PRINTF("\nTroubleshooting tips:\n");
        DBG_PRINTF("  1. Check if port %lu is already in use\n", (unsigned long)config.dwPort);
        DBG_PRINTF("  2. Run 'netstat -ano | findstr :%lu' to find conflicting process\n", (unsigned long)config.dwPort);
        DBG_PRINTF("  3. Try running as Administrator if using privileged ports\n");
        DBG_PRINTF("  4. Check Windows Firewall settings for port %lu\n", (unsigned long)config.dwPort);
        DBG_PRINTF("\nFor detailed configuration, see RPC_CONFIG.md\n");
        return 1;
    }
    
    // Start server
    if (!pServer->Start()) {
        DBG_PRINTF("Failed to start CustomLeechServer\n");
        pServer->Shutdown();
        return 1;
    }
    
    // Main loop - wait only for Ctrl+C event (do not exit if server thread ends)
    DBG_PRINTF("\nCustomLeechServer is running. Press Ctrl+C to stop.\n");
    DBG_PRINTF("=======================================================\n");
    if (g_hQuitEvent) {
        WaitForSingleObject(g_hQuitEvent, INFINITE);
    } else {
        while (!g_fShutdown) { Sleep(1000); }
    }
    
    // Cleanup
    DBG_PRINTF("\nShutting down CustomLeechServer...\n");
    pServer->Shutdown();
    
    // Wait for handshake thread to exit
    if (g_hHandshakeThread) {
        WaitForSingleObject(g_hHandshakeThread, 2000);
        CloseHandle(g_hHandshakeThread);
    }
    
    // 显示最终连接状态
    if (g_fClientConnected) {
        DBG_PRINTF("\n服务器关闭时状态: 已绑定客户端\n%s\n", g_connectedClientInfo);
    } else {
        DBG_PRINTF("\n服务器关闭时状态: 未绑定任何客户端\n");
    }

    DBG_PRINTF("CustomLeechServer shutdown complete.\n");
    if (g_hQuitEvent) { CloseHandle(g_hQuitEvent); g_hQuitEvent = NULL; }
    VMProtectEnd();
    return 0;
}

#endif // !DLLMODE
