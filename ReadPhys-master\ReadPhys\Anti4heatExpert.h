#pragma once
#include "KliGlobal.h"
#include "kli.hpp"

// KLI缓存函数指针已在KliGlobal.h中统一声明

namespace Anti4heatExpert
{
	typedef struct _PHYSICAL_PAGE_INFO
	{
		PVOID BaseAddress;
		PVOID PteAddress;
		SIZE_T Size;
	} PHYSICAL_PAGE_INFO, * PPHYSICAL_PAGE_INFO;

	typedef struct _PAGE_TABLE_INFO
	{
		ULONG PageType;
		ULONG64 Pxe;
		ULONG64 Ppe;
		ULONG64 Pde;
		ULONG64 Pte;
	} PAGE_TABLE_INFO, * PPAGE_TABLE_INFO;

	KIRQL __fastcall RaiseIRQL(KIRQL NewIrql);

	KIRQL __fastcall RaiseIrqlToDpcLv();

	void __fastcall LowerIrql(KIRQL Irql);

	bool __fastcall IsPhysPageInRange(ULONG64 PhysPageBase, ULONG64 Size);

	bool __fastcall IsVaPhysicalAddressValid(PVOID VirtualAddress);

	PVOID __fastcall GetPML4Base(PHYSICAL_ADDRESS DirectoryTableBase);

	ULONG __fastcall InitializePteBase();

	ULONG64 __fastcall GetPteAddress(PVOID VirtualAddress);

	ULONG __fastcall AllocatePhysicalPage(
		PHYSICAL_PAGE_INFO* PhysicalPageInfo,
		SIZE_T Size);

	void __fastcall FreePhysicalPage(
		PHYSICAL_PAGE_INFO* PageInfo);

	ULONG __fastcall ReadPhysicalPage(
		PHYSICAL_PAGE_INFO* TransferPageInfo,
		ULONG64 PhysPageBase,
		PVOID Buffer,
		SIZE_T Size);

	ULONG __fastcall GetPageTableInfo(
		PHYSICAL_PAGE_INFO* TransferPageInfo,
		ULONG64 Cr3,
		ULONG64 PageAddress,
		PAGE_TABLE_INFO* PageTableInfo);

	ULONG __fastcall GetPhysPageAddress(
		PHYSICAL_PAGE_INFO* TransferPageInfo,
		ULONG64 TargetCr3,
		PVOID PageVa,
		PULONG64 pPhysicalAddress);

	ULONG __fastcall GetPhysPageSize(
		PHYSICAL_PAGE_INFO* TransferPageInfo,
		ULONG64 PageAddress,
		PULONG64 pPageSize,
		ULONG64 TargetCr3);
}
