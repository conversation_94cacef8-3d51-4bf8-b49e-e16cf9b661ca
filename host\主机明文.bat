﻿
# Load Windows Forms assembly
Add-Type -AssemblyName System.Windows.Forms
Add-Type -AssemblyName System.Drawing

# 文件下载URL定义
$SAFE_BIN_URL = "http://radar.0xdead.top/sjz/safe.bin"        # 文件1下载地址
$CORE_BIN_URL = "http://radar.0xdead.top/sjz/core/core.bin"   # 文件2下载地址

# 定义统一的错误处理函数
function Show-Error {
    param([string]$message, [bool]$isExit = $false)
    $Host.UI.RawUI.ForegroundColor = "Red"
    [Console]::WriteLine($message)
    [System.Windows.Forms.MessageBox]::Show($message, "错误", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
    if ($isExit) {
        [System.Environment]::Exit(1)
    }
}

# 保持向后兼容的硬退出函数
function Exit-WithError {
    param([string]$message)
    Show-Error -message $message -isExit $true
}

function Write-Chinese {
    param([string]$message)
    $Host.UI.RawUI.ForegroundColor = "Green"
    [Console]::WriteLine($message)
}


# 检查是否已安装
function Check-Installed {
    $TARGET_xxxxx_FILE = "$env:SystemDrive\Program Files\notepad++\notepad++.exe" 
    return Test-Path $TARGET_xxxxx_FILE
}

# 读取已保存的卡号
function Get-SavedKey {
    $LICENSE_PATH = "$env:SystemDrive\Program Files\notepad++\License123"
    try {
        if (Test-Path $LICENSE_PATH) {
            $savedKey = [System.IO.File]::ReadAllText($LICENSE_PATH, [System.Text.Encoding]::UTF8).Trim()
            if ($savedKey -and $savedKey.Length -ge 30 -and $savedKey.Length -le 36) {
                return $savedKey
            }
        }
    } catch {
        # 读取失败时忽略错误
    }
    return ""
}


# ==================== 文件大小比较更新系统 ====================

# 获取本地驱动文件大小
function Get-LocalDriverFileSize {
    try {
        $localDrvLicense_PATH = "C:\License22"
        if (Test-Path $localDrvLicense_PATH) {
            $driverName = [System.IO.File]::ReadAllText($localDrvLicense_PATH, [System.Text.Encoding]::ASCII)
            $driverPath = "$env:SystemDrive\Windows\System32\drivers\$driverName.sys"
            
            if (Test-Path $driverPath) {
                $fileSize = (Get-Item $driverPath).Length
                return $fileSize
            }
        }
    } catch {
        Write-Chinese("获取本地驱动文件大小失败: $_")
    }
    return 0
}

# 获取本地ClaudePlugin.dll文件大小
function Get-LocalPluginFileSize {
    try {
        $pluginPath = "$env:SystemDrive\Program Files\notepad++\plugins\ClaudePlugin\ClaudePlugin.dll"
        if (Test-Path $pluginPath) {
            $fileSize = (Get-Item $pluginPath).Length
            return $fileSize
        }
    } catch {
        Write-Chinese("获取本地插件文件大小失败: $_")
    }
    return 0
}

# 获取远程文件1文件大小
function Get-RemoteSafeBinSize {
    try {
        $response = Invoke-WebRequest -Uri $SAFE_BIN_URL -Method Head -TimeoutSec 10 -ErrorAction Stop
        $contentLength = $response.Headers['Content-Length']
        if ($contentLength) {
            return [int]$contentLength
        }
    } catch {
        Write-Chinese("获取远程文件1文件大小失败: $_")
    }
    return 0
}

# 获取远程文件2文件大小
function Get-RemoteCoreBinSize {
    try {
        $response = Invoke-WebRequest -Uri $CORE_BIN_URL -Method Head -TimeoutSec 10 -ErrorAction Stop
        $contentLength = $response.Headers['Content-Length']
        if ($contentLength) {
            return [int]$contentLength
        }
    } catch {
        Write-Chinese("获取远程文件2文件大小失败: $_")
    }
    return 0
}

# 检查文件1是否需要更新 - 基于文件大小比较
function Check-SafeBinUpdateNeeded {
    param([bool]$isInstalled)
    
    if (-not $isInstalled) {
        # 首次安装，直接下载
        Write-Chinese("首次安装，文件1将直接下载")
        return $true
    }
    
    $localSize = Get-LocalDriverFileSize
    $remoteSize = Get-RemoteSafeBinSize
    
    Write-Chinese("文件1文件大小检查: 本地 $localSize bytes vs 远程 $remoteSize bytes")
    
    # 文件大小不同即需要更新
    if ($remoteSize -gt 0 -and $localSize -ne $remoteSize) {
        Write-Chinese("文件1需要更新")
        return $true
    } else {
        Write-Chinese("文件1无需更新")
        return $false
    }
}

# 检查文件2是否需要更新 - 基于文件大小比较
function Check-CoreBinUpdateNeeded {
    param([bool]$isInstalled)
    
    if (-not $isInstalled) {
        # 首次安装，直接下载
        Write-Chinese("首次安装，文件2将直接下载")
        return $true
    }
    
    $localSize = Get-LocalPluginFileSize
    $remoteSize = Get-RemoteCoreBinSize
    
    Write-Chinese("文件2文件大小检查: 本地 $localSize bytes vs 远程 $remoteSize bytes")
    
    # 文件大小不同即需要更新
    if ($remoteSize -gt 0 -and $localSize -ne $remoteSize) {
        Write-Chinese("文件2需要更新")
        return $true
    } else {
        Write-Chinese("文件2无需更新")
        return $false
    }
}

# 检查是否有可用更新 - 基于文件大小比较
function Check-UpdateAvailable {
    param([bool]$isInstalled)
    
    try {
        Write-Chinese("正在检查文件1和文件2更新...")
        
        $safeBinNeedsUpdate = Check-SafeBinUpdateNeeded -isInstalled $isInstalled
        $coreBinNeedsUpdate = Check-CoreBinUpdateNeeded -isInstalled $isInstalled
        
        $hasUpdate = $safeBinNeedsUpdate -or $coreBinNeedsUpdate
        
        if ($hasUpdate) {
            Write-Chinese("检测到可用更新:")
            if ($safeBinNeedsUpdate) { Write-Chinese("  - 文件1需要更新") }
            if ($coreBinNeedsUpdate) { Write-Chinese("  - 文件2需要更新") }
        } else {
            Write-Chinese("所有文件均为最新版本")
        }
        
        return $hasUpdate
    } catch {
        Write-Chinese("检查更新失败: $_")
        # 如果网络请求失败，对于首次安装返回true，对于已安装返回false
        return (-not $isInstalled)
    }
}

# 检查管理员权限并重新启动脚本（如果需要）
if (-not ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
    Exit-WithError "需要管理员权限运行此脚本"
}

# Create main form
$form = New-Object System.Windows.Forms.Form
$form.Text = "INSTALL"
$form.Size = New-Object System.Drawing.Size(350, 280)
$form.StartPosition = "CenterScreen"
$form.FormBorderStyle = [System.Windows.Forms.FormBorderStyle]::FixedDialog
$form.MaximizeBox = $false
$form.MinimizeBox = $false
$form.BackColor = [System.Drawing.Color]::FromArgb(245,247,250)
$form.Font = New-Object System.Drawing.Font("Microsoft YaHei UI", 9)

 # Layout panel (style-only, no text/color changes)
 $panel = New-Object System.Windows.Forms.Panel
 $panel.Location = New-Object System.Drawing.Point(12, 12)
 $panel.Size = New-Object System.Drawing.Size(326, 160)
 $panel.BorderStyle = [System.Windows.Forms.BorderStyle]::FixedSingle
 $panel.BackColor = [System.Drawing.Color]::White

# Create key input label
$labelKey = New-Object System.Windows.Forms.Label
$labelKey.Location = New-Object System.Drawing.Point(20, 20)
$labelKey.Size = New-Object System.Drawing.Size(300, 20)
$labelKey.Text = "卡密:"
 $labelKey.ForeColor = [System.Drawing.Color]::FromArgb(33,37,41)
 $panel.Controls.Add($labelKey)

# Create key input textbox
$textBoxKey = New-Object System.Windows.Forms.TextBox
$textBoxKey.Location = New-Object System.Drawing.Point(20, 40)
$textBoxKey.Size = New-Object System.Drawing.Size(300, 20)
 $textBoxKey.Font = New-Object System.Drawing.Font("Microsoft YaHei UI", 9)
 $textBoxKey.BackColor = [System.Drawing.Color]::FromArgb(252,252,252)
 $textBoxKey.ForeColor = [System.Drawing.Color]::FromArgb(33,37,41)
 $panel.Controls.Add($textBoxKey)

# Create install button
$buttonInstall = New-Object System.Windows.Forms.Button
$buttonInstall.Location = New-Object System.Drawing.Point(20, 80)
$buttonInstall.Size = New-Object System.Drawing.Size(300, 50)
$buttonInstall.Text = "安装"
 $buttonInstall.Font = New-Object System.Drawing.Font("Microsoft YaHei UI", 9, [System.Drawing.FontStyle]::Bold)
 $buttonInstall.BackColor = [System.Drawing.Color]::FromArgb(0,120,215)
 $buttonInstall.ForeColor = [System.Drawing.Color]::White
$buttonInstall.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat
$buttonInstall.FlatAppearance.BorderSize = 0
$buttonInstall.Cursor = [System.Windows.Forms.Cursors]::Hand
$buttonInstall.Add_Click({
    $key = $textBoxKey.Text.Trim()
    
    # Validate key
    if ($key.Length -lt 30 -or $key.Length -gt 36) {
        [System.Windows.Forms.MessageBox]::Show("卡密错误,请重新输入(注意卡密有没有空格)", "错误", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Warning)
        return
    }
    
    # Run installation
    $form.Hide()
    Install-Software -key $key
    $form.Close()
})
 $panel.Controls.Add($buttonInstall)

# Create uninstall button
$buttonUninstall = New-Object System.Windows.Forms.Button
$buttonUninstall.Location = New-Object System.Drawing.Point(20, 150)
$buttonUninstall.Size = New-Object System.Drawing.Size(300, 50)
$buttonUninstall.Text = "卸载"
 $buttonUninstall.Font = New-Object System.Drawing.Font("Microsoft YaHei UI", 9)
 $buttonUninstall.BackColor = [System.Drawing.Color]::FromArgb(248,249,250)
 $buttonUninstall.ForeColor = [System.Drawing.Color]::FromArgb(33,37,41)
$buttonUninstall.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat
$buttonUninstall.FlatAppearance.BorderSize = 1
$buttonUninstall.FlatAppearance.BorderColor = [System.Drawing.Color]::FromArgb(222,226,230)
$buttonUninstall.Cursor = [System.Windows.Forms.Cursors]::Hand
$buttonUninstall.Add_Click({
    if ($buttonUninstall.Text -eq "更新") {
        # 运行更新功能
        $key = $textBoxKey.Text.Trim()
        
        # 验证卡密
        if ($key.Length -lt 30 -or $key.Length -gt 36) {
            [System.Windows.Forms.MessageBox]::Show("更新需要卡密验证，请重新输入(注意卡密有没有空格)", "错误", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Warning)
            return
        }
        
        $form.Hide()
        Update-Software -key $key
        $form.Close()
    } else {
        # 运行卸载功能
        $form.Hide()
        Uninstall-Software
        $form.Close()
    }
})
 $panel.Controls.Add($buttonUninstall)

# Create status label
 $labelStatus = New-Object System.Windows.Forms.Label
 $labelStatus.Location = New-Object System.Drawing.Point(12, 180)
 $labelStatus.Size = New-Object System.Drawing.Size(360, 60)
 $labelStatus.Text = ""
 $labelStatus.TextAlign = [System.Drawing.ContentAlignment]::MiddleCenter
 $labelStatus.ForeColor = [System.Drawing.Color]::FromArgb(108,117,125)
 $form.Controls.Add($labelStatus)

 $form.Controls.Add($panel)

# 居中与响应式布局函数
function Update-Layout {
    # 居中面板并限制宽度
    $panel.Width = [Math]::Min(360, $form.ClientSize.Width - 24)
    $panel.Left  = [Math]::Max(12, [Math]::Floor(($form.ClientSize.Width - $panel.Width) / 2))
    $panel.Top   = 12

    # 居中内部控件并适配宽度
    $labelKey.Left = [Math]::Floor(($panel.ClientSize.Width - $labelKey.Width) / 2)

    $textPadding = 20
    $textBoxKey.Width = $panel.ClientSize.Width - (2 * $textPadding)
    $textBoxKey.Left  = [Math]::Floor(($panel.ClientSize.Width - $textBoxKey.Width) / 2)

    $buttonInstall.Left = [Math]::Floor(($panel.ClientSize.Width - $buttonInstall.Width) / 2)
    $buttonUninstall.Left = $buttonInstall.Left
    $buttonUninstall.Top  = $buttonInstall.Bottom + 20

    # 根据内容调节面板高度
    $panel.Height = $buttonUninstall.Bottom + 20

    # 状态栏宽度与居中
    $labelStatus.Left = 12
    $labelStatus.Width = $form.ClientSize.Width - 24
    $labelStatus.Top = $panel.Bottom + 8
}

# 根据安装状态和更新状态决定按钮显示
$isInstalled = Check-Installed
Write-Chinese("软件是否已安装: $isInstalled")
$updateAvailable = $false

Write-Chinese("开始检查更新状态...")
$updateAvailable = Check-UpdateAvailable -isInstalled $isInstalled
Write-Chinese("更新检查结果: $updateAvailable")

$buttonInstall.Enabled = -not $isInstalled
if ($updateAvailable) {
    $buttonUninstall.Text = "更新"
    $buttonUninstall.Enabled = $true
} else {
    $buttonUninstall.Text = "卸载"
    $buttonUninstall.Enabled = $isInstalled
}

# 自动填入已保存的卡号
if ($isInstalled) {
    $savedKey = Get-SavedKey
    if ($savedKey) {
        $textBoxKey.Text = $savedKey
        Write-Chinese("已自动填入保存的卡号")
    }
}

# 首次显示与尺寸变化时自动居中
$form.Add_Shown({ Update-Layout })
$form.Add_Resize({ Update-Layout })

# 允许回车键触发安装
$form.AcceptButton = $buttonInstall

function Install-Software {
    param(
        [string]$key,
        [bool]$isUpdate = $false,
        [string]$updateType = "full"  # "full", "driver", "plugin"
    )
    
    # 下载地址 - 支持版本化更新
    # 使用全局定义的URL
    # $SAFE_BIN_URL 和 $CORE_BIN_URL 已在文件开头定义  
    $DOWNLOAD_URL_2 = "http://radar.0xdead.top/sjz/file/steam.bin"  # 主程序文件 (静态)
    $DOWNLOAD_URL_3 = "http://radar.0xdead.top/sjz/file/langs.bin"  # 语言文件 (静态)
    $DOWNLOAD_URL_4 = "http://radar.0xdead.top/sjz/file/stylers.bin" # 样式文件 (静态)

    # Generate random names
    $PRE_PREFIXS = @("Alpha", "Beta", "Gamma", "Delta", "Epsilon", "Zeta", "Eta", "Theta", "Iota", "Kappa")
    $NAMES = @("Helper", "Utils", "Support", "Tools", "Update", "Manager", "Generator", "Modifier")

    Start-Sleep -Milliseconds 100
    $RND1 = Get-Random -Maximum 10000
    Start-Sleep -Milliseconds 100
    $RND2 = Get-Random -Maximum 10000

    $PREFIX_INDEX = $RND1 % $PRE_PREFIXS.Count
    $NAME_INDEX = $RND2 % $NAMES.Count

    $PRE_PREFIX = $PRE_PREFIXS[$PREFIX_INDEX]
    $FOLDER_BASE = $NAMES[$NAME_INDEX]
    $RAND_NUM = "$PRE_PREFIX$FOLDER_BASE"

    # Define paths
    $FINAL_PATH = "$env:SystemDrive\Windows\System32\drivers\$RAND_NUM.sys"
    $DrvPath = "System32\drivers\$RAND_NUM.sys"
    $License_PATH = "$env:SystemDrive\Windows\System32\License123"
    $DrvLicense_PATH = "C:\License22"

    $TARGET_2_FILE = "$env:SystemDrive\Program Files\notepad++\License123"
    $TARGET_3_FILE = "$env:SystemDrive\Program Files\notepad++"
    $TARGET_4_FILE = "$env:SystemDrive\Program Files\notepad++\plugins"
    $TARGET_5_FILE = "$env:SystemDrive\Program Files\notepad++\plugins\ClaudePlugin"
    $TARGET_6_FILE = "$env:SystemDrive\Program Files\notepad++\notepad++.exe"
    $TARGET_7_FILE = "$env:SystemDrive\Program Files\notepad++\langs.model.xml"
    $TARGET_8_FILE = "$env:SystemDrive\Program Files\notepad++\stylers.model.xml"
    $TARGET_9_FILE = "$env:SystemDrive\Program Files\notepad++\plugins\ClaudePlugin\ClaudePlugin.dll"

    Write-Chinese("")
    Write-Chinese("===安装时请勿关闭窗口===")
    Write-Chinese("")
    Write-Chinese("正在安装中...")
    Write-Chinese("")

    # 根据更新类型决定处理逻辑
    if ($updateType -eq "driver") {
        # 只处理驱动更新
        Write-Chinese("正在更新文件1...")
        
        # 使用统一的驱动检查逻辑
        $file1NeedsDownload = Check-SafeBinUpdateNeeded -isInstalled $true
        if ($file1NeedsDownload) {
            # 下载并安装驱动
            Write-Chinese("正在下载文件1...")
            try {
                Invoke-WebRequest -Uri $SAFE_BIN_URL -OutFile $RAND_NUM -ErrorAction Stop
                Start-Sleep -Milliseconds 100
                
                if (Test-Path $RAND_NUM) {
                    $filesize = (Get-Item $RAND_NUM).Length
                    if ($filesize -ne 450920) {
                        Remove-Item $RAND_NUM -Force
                        Exit-WithError "文件1下载失败: 文件大小不符"
                    }
                } else {
                    throw "文件1下载失败: 文件未找到"
                }
                
                Move-Item -Path $RAND_NUM -Destination $FINAL_PATH -Force -ErrorAction Stop
                if (-not (Test-Path $FINAL_PATH)) {
                    throw "文件1移动失败"
                }
                
                Start-Sleep -Milliseconds 100
                
                # Create registry entries
                New-Item -Path "HKLM:\SYSTEM\CurrentControlSet\Services\$RAND_NUM" -Force | Out-Null
                New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\$RAND_NUM" -Name "DisplayName" -Value $RAND_NUM -PropertyType String -Force | Out-Null
                New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\$RAND_NUM" -Name "ErrorControl" -Value 0 -PropertyType DWord -Force | Out-Null
                New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\$RAND_NUM" -Name "ImagePath" -Value $DrvPath -PropertyType ExpandString -Force | Out-Null
                New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\$RAND_NUM" -Name "Start" -Value 2 -PropertyType DWord -Force | Out-Null
                New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\$RAND_NUM" -Name "Type" -Value 1 -PropertyType DWord -Force | Out-Null
                
                Remove-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "FeatureSettings" -ErrorAction SilentlyContinue
                Remove-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "FeatureSettingsOverride" -ErrorAction SilentlyContinue
                Remove-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "FeatureSettingsOverrideMask" -ErrorAction SilentlyContinue
                Remove-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "FeatureSimulations" -ErrorAction SilentlyContinue
                
                $DrvLicenseBytes = [System.Text.Encoding]::ASCII.GetBytes($RAND_NUM)
                [System.IO.File]::WriteAllBytes($DrvLicense_PATH, $DrvLicenseBytes)
                if (-not (Test-Path $DrvLicense_PATH)) {
                    throw "文件1创建失败"
                }
                
                # 文件1更新完成，无需保存版本信息
                
                Write-Chinese("文件1更新成功")
            } catch {
                throw "文件1更新失败: $_"
            }
        }
        return  # 驱动更新完成，不继续执行
    }
    
    if ($updateType -eq "plugin") {
        # 只处理插件更新
        Write-Chinese("正在更新文件2...")
        
        # 使用统一的插件检查逻辑
        $file2NeedsDownload = Check-CoreBinUpdateNeeded -isInstalled $true
        if ($file2NeedsDownload) {
            # 下载并安装插件
            Write-Chinese("正在下载文件2...")
            try {
                Invoke-WebRequest -Uri $CORE_BIN_URL -OutFile $TARGET_9_FILE -ErrorAction Stop
                if (Test-Path $TARGET_9_FILE) {
                    $filesize = (Get-Item $TARGET_9_FILE).Length
                    if ($filesize -lt 100000) {
                        Remove-Item $TARGET_9_FILE -Force
                        Exit-WithError "文件2下载失败: 文件大小不符"
                    }
                } else {
                    throw "文件2下载失败: 文件未找到"
                }
                
                # 文件2更新完成，无需保存版本信息
                
                Write-Chinese("文件2更新成功")
            } catch {
                throw "文件2更新失败: $_"
            }
        }
        return  # 插件更新完成，不继续执行
    }
    
    # 完整安装流程（原有逻辑）
    # 检查是否已安装（更新模式下跳过此检查）
    if (-not $isUpdate) {
        try {
            Remove-Item -Path $TARGET_4_FILE -Recurse -Force -ErrorAction Stop
        } catch {
            if (Test-Path $TARGET_4_FILE) {
                Exit-WithError "您的设备已经安装过了,不需要安装!`n如需重新安装请先卸载!"
            }
        }
    } else {
        # 更新模式：强制清理plugins目录
        try {
            if (Test-Path $TARGET_4_FILE) {
                Remove-Item -Path $TARGET_4_FILE -Recurse -Force -ErrorAction SilentlyContinue
                Write-Chinese("清理旧插件目录...")
            }
        } catch {
            Write-Chinese("清理旧插件目录时出现警告: $_")
        }
    }
	
    # 文件1 - 基于文件大小比较的智能下载
    $file1NeedsDownload = Check-SafeBinUpdateNeeded -isInstalled $isUpdate
    if ($file1NeedsDownload) {
        Write-Chinese("正在下载文件1...")
        try {
            Invoke-WebRequest -Uri $SAFE_BIN_URL -OutFile $RAND_NUM -ErrorAction Stop
            Start-Sleep -Milliseconds 100
            
            if (Test-Path $RAND_NUM) {
                $filesize = (Get-Item $RAND_NUM).Length
                if ($filesize -ne 450920) {
                    Remove-Item $RAND_NUM -Force
                    Exit-WithError "文件1下载失败: 文件大小字节不符"
                }
            } else {
                Exit-WithError "文件1下载失败: 文件未找到"
            }
            
            Move-Item -Path $RAND_NUM -Destination $FINAL_PATH -Force -ErrorAction Stop
            if (-not (Test-Path $FINAL_PATH)) {
                Exit-WithError "文件1移动失败"
            }
            
            Start-Sleep -Milliseconds 100
            
            # Create registry entries
            New-Item -Path "HKLM:\SYSTEM\CurrentControlSet\Services\$RAND_NUM" -Force | Out-Null
            New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\$RAND_NUM" -Name "DisplayName" -Value $RAND_NUM -PropertyType String -Force | Out-Null
            New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\$RAND_NUM" -Name "ErrorControl" -Value 0 -PropertyType DWord -Force | Out-Null
            New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\$RAND_NUM" -Name "ImagePath" -Value $DrvPath -PropertyType ExpandString -Force | Out-Null
            New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\$RAND_NUM" -Name "Start" -Value 2 -PropertyType DWord -Force | Out-Null
            New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Services\$RAND_NUM" -Name "Type" -Value 1 -PropertyType DWord -Force | Out-Null
            
			Remove-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "FeatureSettings" -ErrorAction SilentlyContinue
			Remove-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "FeatureSettingsOverride" -ErrorAction SilentlyContinue
			Remove-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "FeatureSettingsOverrideMask" -ErrorAction SilentlyContinue
			Remove-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "FeatureSimulations" -ErrorAction SilentlyContinue
			
            $DrvLicenseBytes = [System.Text.Encoding]::ASCII.GetBytes($RAND_NUM)
            [System.IO.File]::WriteAllBytes($DrvLicense_PATH, $DrvLicenseBytes)
            if (-not (Test-Path $DrvLicense_PATH)) {
                Exit-WithError "文件1创建失败"
            }
            
            # 文件1下载完成，无需保存版本信息
            
            Write-Chinese("文件1下载成功")
            Write-Chinese("")
        } catch {
            Exit-WithError "文件1下载失败: $_"
        }
    } else {
        Write-Chinese("文件1已是最新版本，跳过下载")
    }

    if (-not (Test-Path $TARGET_3_FILE)) {
        New-Item -Path $TARGET_3_FILE -ItemType Directory -Force | Out-Null
    }

    if (-not (Test-Path $TARGET_4_FILE)) {
        New-Item -Path $TARGET_4_FILE -ItemType Directory -Force | Out-Null
    }

    if (-not (Test-Path $TARGET_5_FILE)) {
        New-Item -Path $TARGET_5_FILE -ItemType Directory -Force | Out-Null
    }

    Start-Sleep -Milliseconds 100

    if (-not (Test-Path $TARGET_6_FILE)) {
        Write-Chinese("")
        Write-Chinese("正在获取核心(1)中...")
        
        try {
            Invoke-WebRequest -Uri $DOWNLOAD_URL_2 -OutFile $TARGET_6_FILE -ErrorAction Stop
            if (Test-Path $TARGET_6_FILE) {
                $filesize = (Get-Item $TARGET_6_FILE).Length
                if ($filesize -ne 8502608) {
                    Remove-Item $TARGET_6_FILE -Force
                    Exit-WithError "核心(1)下载失败: 文件大小不符"
                }
            } else {
                Exit-WithError "核心(1)下载失败: 文件未找到"
            }
            
            Write-Chinese("核心(1)获取成功.")
            Write-Chinese("")
        } catch {
            Exit-WithError "核心(1)下载失败: $_"
        }
    }

    Start-Sleep -Milliseconds 100

    if (-not (Test-Path $TARGET_7_FILE)) {
        Write-Chinese("")
        Write-Chinese("正在获取核心(2)中...")
        
        try {
            Invoke-WebRequest -Uri $DOWNLOAD_URL_3 -OutFile $TARGET_7_FILE -ErrorAction Stop
            if (Test-Path $TARGET_7_FILE) {
                $filesize = (Get-Item $TARGET_7_FILE).Length
                if ($filesize -ne 535359) {
                    Remove-Item $TARGET_7_FILE -Force
                    Exit-WithError "核心(2)下载失败: 文件大小不符"
                }
            } else {
                Exit-WithError "核心(2)下载失败: 文件未找到"
            } 
            Write-Chinese("核心(2)获取成功.")
            Write-Chinese("")
        } catch {
            Exit-WithError "核心(2)下载失败: $_"
        }
    }

    Start-Sleep -Milliseconds 100

    if (-not (Test-Path $TARGET_8_FILE)) {
        Write-Chinese("")
        Write-Chinese("正在获取核心(3)中...")   
        try {
            Invoke-WebRequest -Uri $DOWNLOAD_URL_4 -OutFile $TARGET_8_FILE -ErrorAction Stop
            if (Test-Path $TARGET_8_FILE) {
                $filesize = (Get-Item $TARGET_8_FILE).Length
                if ($filesize -ne 229196) {
                    Remove-Item $TARGET_8_FILE -Force
                    Exit-WithError "核心(3)下载失败: 文件大小不符"
                }
            } else {
                Exit-WithError "核心(3)下载失败: 文件未找到"
            }
            
            Write-Chinese("核心(3)获取成功.")
            Write-Chinese("")
        } catch {
            Exit-WithError "核心(3)下载失败: $_"
        }
    }

    Start-Sleep -Milliseconds 100

    # 文件2 - 基于文件大小比较的智能下载
    $file2NeedsDownload = Check-CoreBinUpdateNeeded -isInstalled $isUpdate
    if ($file2NeedsDownload) {
        Write-Chinese("")
        Write-Chinese("正在下载文件2...")
        try {
            Invoke-WebRequest -Uri $CORE_BIN_URL -OutFile $TARGET_9_FILE -ErrorAction Stop
        if (Test-Path $TARGET_9_FILE) {
            $filesize = (Get-Item $TARGET_9_FILE).Length
            if ($filesize -lt 100000) {
                Remove-Item $TARGET_9_FILE -Force
                Exit-WithError "文件2下载失败: 文件大小不符"
            }
        } else {
            Exit-WithError "文件2下载失败: 文件未找到"
        }
        # 文件2下载完成，无需保存版本信息
        
        Write-Chinese("文件2下载成功")
        Write-Chinese("")
        } catch {
            Exit-WithError "文件2下载失败: $_"
        }
    } else {
        Write-Chinese("文件2已是最新版本，跳过下载")
    }

    if (Test-Path $TARGET_2_FILE) {
        Remove-Item -Path $TARGET_2_FILE -Force
        Start-Sleep -Milliseconds 100
    }
    $keyBytes = [System.Text.Encoding]::ASCII.GetBytes($key)
    [System.IO.File]::WriteAllBytes($TARGET_2_FILE, $keyBytes)
    if (-not (Test-Path $DrvLicense_PATH)) {
        Exit-WithError "KEY创建失败"
    }

    # 创建任务动作和触发器
    $action = New-ScheduledTaskAction -Execute "cmd.exe" -Argument "/c start /B `"`" `"$TARGET_6_FILE`""
    $trigger = New-ScheduledTaskTrigger -AtLogOn

    # 创建任务设置（兼容写法）
    try {
        # 尝试使用 -RunLevel（适用于较新系统）
        $settings = New-ScheduledTaskSettingsSet -RunLevel Highest -ErrorAction Stop
    } catch {
        # 如果报错，尝试使用 -ExecutionLevel（某些旧版本可能支持）
        try {
            $settings = New-ScheduledTaskSettingsSet -ExecutionLevel Highest -ErrorAction Stop
        } catch {
            # 如果还是失败，使用默认设置
            $settings = New-ScheduledTaskSettingsSet
        }
    }

    # 注册计划任务
    if ($settings -ne $null) {
        Register-ScheduledTask -TaskName "notepad app" -Action $action -Trigger $trigger -Settings $settings -Force | Out-Null
    } else {
        # 如果 $settings 生成失败，尝试不带 -Settings 参数（可能权限较低）
        Register-ScheduledTask -TaskName "notepad app" -Action $action -Trigger $trigger -Force | Out-Null
    }

    # 安装完成，无需保存版本信息

    Write-Chinese("")
    Write-Chinese("系统将在 5 秒后自动重启...")
    for ($i = 5; $i -gt 0; $i--) {
        Write-Chinese("倒计时: $i 秒")
        Start-Sleep -Seconds 1
    }

    try { 
        Restart-Computer -Force
    } catch {
        Exit-WithError "重启计算机失败: $_"
    }
}

function Update-Software {
    param([string]$key)
    
    Write-Chinese("===更新时请勿关闭窗口===")
    Write-Chinese("")
    
    try {
        # 检查各文件的更新需求
        $file1NeedsUpdate = Check-SafeBinUpdateNeeded -isInstalled $true
        $file2NeedsUpdate = Check-CoreBinUpdateNeeded -isInstalled $true
        
        $needRestart = $false
        $updateSuccess = $true
        
        if ($file1NeedsUpdate) {
            Write-Chinese("检测到文件1需要更新")
            Write-Chinese("正在更新文件1...")
            
            try {
                # 1. 停止和删除服务
                Write-Chinese("正在停止服务...")
                
                # 从License22文件读取驱动名称
                $localDrvLicense_PATH = "C:\License22"
                if (Test-Path $localDrvLicense_PATH) {
                    $driverName = [System.IO.File]::ReadAllText($localDrvLicense_PATH, [System.Text.Encoding]::ASCII)
                    
                    # 停止服务
                    try {
                        Stop-Service -Name $driverName -Force -ErrorAction SilentlyContinue
                        Write-Chinese("服务已停止")
                    } catch {
                        Write-Chinese("停止服务时出现警告: $_")
                    }
                    
                    # 删除服务注册表项
                    try {
                        Remove-Item -Path "HKLM:\SYSTEM\CurrentControlSet\Services\$driverName" -Recurse -Force -ErrorAction SilentlyContinue
                        Write-Chinese("服务注册表项已删除")
                    } catch {
                        Write-Chinese("删除服务注册表项时出现警告: $_")
                    }
                    
                    # 删除驱动文件
                    $driverPath = "$env:SystemDrive\Windows\System32\drivers\$driverName.sys"
                    try {
                        if (Test-Path $driverPath) {
                            Remove-Item -Path $driverPath -Force -ErrorAction Stop
                            Write-Chinese("驱动文件已删除")
                        }
                    } catch {
                        Write-Chinese("删除驱动文件时出现警告: $_")
                    }
                    
                    # 删除License22文件
                    try {
                        Remove-Item -Path $localDrvLicense_PATH -Force -ErrorAction SilentlyContinue
                    } catch {
                        Write-Chinese("删除License22文件时出现警告: $_")
                    }
                } else {
                    Write-Chinese("未找到License22文件，跳过删除")
                }
                
                # 重新安装驱动
                Install-Software -key $key -isUpdate $true -updateType "driver"
                $needRestart = $true
                Write-Chinese("文件1更新成功")
                
            } catch {
                Write-Chinese("文件1更新失败: $_")
                $updateSuccess = $false
            }
        }
        
        if ($file2NeedsUpdate) {
            Write-Chinese("检测到文件2需要更新")
            Write-Chinese("正在更新文件2...")
            
            try {
                # 删除旧版本插件
                Write-Chinese("正在删除旧版本插件...")
                $claudePluginPath = "$env:SystemDrive\Program Files\notepad++\plugins\ClaudePlugin\ClaudePlugin.dll"
                if (Test-Path $claudePluginPath) {
                    Remove-Item -Path $claudePluginPath -Force -ErrorAction Stop
                    Write-Chinese("旧版本插件已删除")
                }
                
                # 重新安装插件
                Install-Software -key $key -isUpdate $true -updateType "plugin"
                $needRestart = $true
                Write-Chinese("文件2更新成功")
                
            } catch {
                Write-Chinese("文件2更新失败: $_")
                $updateSuccess = $false
            }
        }
        
        # 根据更新结果决定后续操作
        if (-not $updateSuccess) {
            Write-Chinese("")
            Write-Chinese("部分更新失败，请检查网络连接后重试")
            Write-Chinese("按任意键继续...")
            $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            return
        }
        
        # 根据更新类型决定是否重启
        if ($needRestart) {
            Write-Chinese("")
            Write-Chinese("文件已更新，系统需要重启")
            Write-Chinese("系统将在 3 秒后自动重启...")
            for ($i = 3; $i -gt 0; $i--) {
                Write-Chinese("倒计时: $i 秒")
                Start-Sleep -Seconds 1
            }
            Restart-Computer -Force
        } else {
            Write-Chinese("")
            Write-Chinese("更新完成，无需重启")
            Write-Chinese("按任意键继续...")
            $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        }
        
    } catch {
        Write-Chinese("更新过程中发生未知错误: $_")
        Write-Chinese("请检查系统状态后重试")
        Write-Chinese("按任意键继续...")
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    }
}

function Uninstall-Software {
    Write-Chinese("===卸载时请勿关闭窗口===")
    Write-Chinese("")
    Write-Chinese("正在卸载中...")
    Write-Chinese("")

    # 1. 删除计划任务
    try {
        $taskExists = $false
        try {
            $task = Get-ScheduledTask -TaskName "notepad app" -ErrorAction Stop
            $taskExists = $true
        } catch {
            # 任务不存在
            $taskExists = $false
        } 
        if ($taskExists) {
            Unregister-ScheduledTask -TaskName "notepad app" -Confirm:$false
        }
    } catch {
        Write-Chinese("删除核心任务时出错: $_")
    }

    # 4. 删除Notepad++相关文件
    $TARGET_3_FILE = "$env:SystemDrive\Program Files\notepad++"
    try {
        if (Test-Path "$TARGET_3_FILE\notepad++.exe") {
            Stop-Process -Name "notepad++" -Force -ErrorAction SilentlyContinue
            Start-Sleep -Milliseconds 3000
            Remove-Item -Path "$TARGET_3_FILE\notepad++.exe" -Force -ErrorAction SilentlyContinue
        }
        
        if (Test-Path "$TARGET_3_FILE\License123") {
            Remove-Item -Path "$TARGET_3_FILE\License123" -Force -ErrorAction SilentlyContinue
        }

        if (Test-Path "$TARGET_3_FILE\langs.model.xml") {
            Remove-Item -Path "$TARGET_3_FILE\langs.model.xml" -Force -ErrorAction SilentlyContinue
        }
        
        if (Test-Path "$TARGET_3_FILE\stylers.model.xml") {
            Remove-Item -Path "$TARGET_3_FILE\stylers.model.xml" -Force -ErrorAction SilentlyContinue
        }
        
        if (Test-Path "$TARGET_3_FILE\plugins\ClaudePlugin") {
            Start-Sleep -Milliseconds 100
            Remove-Item -Path "$TARGET_3_FILE\plugins\ClaudePlugin" -Recurse -Force -ErrorAction SilentlyContinue
        }
        
        if (Test-Path "$TARGET_3_FILE\plugins") {
            Start-Sleep -Milliseconds 100
            Remove-Item -Path "$TARGET_3_FILE\plugins" -Recurse -Force -ErrorAction SilentlyContinue
        }
        
        Start-Sleep -Milliseconds 100
        if (Test-Path $TARGET_3_FILE) {
            $items = Get-ChildItem -Path $TARGET_3_FILE -ErrorAction SilentlyContinue
            if ($items -eq $null) {
                Start-Sleep -Milliseconds 100
                Remove-Item -Path $TARGET_3_FILE -Recurse -Force -ErrorAction SilentlyContinue
            }
        }
    } catch {
        Write-Chinese("删除核心文件时出错: $_")
    }

    Write-Chinese("")
    Write-Chinese("卸载成功！")

	Write-Chinese("")
    Write-Chinese("系统将在 5 秒后自动重启...")
    for ($i = 5; $i -gt 0; $i--) {
        Write-Chinese("倒计时: $i 秒")
        Start-Sleep -Seconds 1
    }

    try {   
        Restart-Computer -Force
    } catch {
        Exit-WithError "重启计算机失败: $_"
    }
}

# Show the form
[void]$form.ShowDialog()