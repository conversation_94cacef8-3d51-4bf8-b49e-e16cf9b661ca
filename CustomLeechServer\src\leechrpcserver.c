// leechrpcserver.c : implementation of RPC server-side functionality.
// Adapted for CustomLeechServer
//
// (c) Ulf Frisk, 2018-2025
// Author: Ulf Frisk, <EMAIL>
//
#include "../include/Common.h"
#include "../include/leechrpc_c.h"
#include <stdio.h>
#include "../include/VMP.h"

// Maximum number of clients for keepalive tracking  
#define LEECHAGENT_CLIENTKEEPALIVE_MAX_CLIENTS  16

typedef struct tdLEECHRPC_SERVER_CONTEXT {
    BOOL fValid;
    LEECHRPC_COMPRESS Compress;
    BOOL fInactivityWatcherThread;
    BOOL fInactivityWatcherThreadIsRunning;
    CRITICAL_SECTION LockClientList;
    struct {
        DWORD dwRpcClientID;
        DWORD cActiveRequests;
        QWORD qwLastTickCount64;
    } ClientList[LEECHAGENT_CLIENTKEEPALIVE_MAX_CLIENTS];
} LEECHRPC_SERVER_CONTEXT, *PLEECHRPC_SERVER_CONTEXT;

LEECHRPC_SERVER_CONTEXT ctxLeechRpc = { 0 };

//-----------------------------------------------------------------------------
// SERVER-SIDE STATE FOR OPTIONAL COMMAND SUPPORT
//-----------------------------------------------------------------------------
// Minimal in-memory cache for MEMMAP (text and struct) and synthetic statistics.
static PBYTE g_pbMemMapText = NULL;
static DWORD g_cbMemMapText = 0;
static PLC_MEMMAP_ENTRY g_pMemMapEntries = NULL;
static DWORD g_cMemMapEntries = 0;

static VOID Server_ResetMemMapState()
{
    LocalFree(g_pbMemMapText); g_pbMemMapText = NULL; g_cbMemMapText = 0;
    LocalFree(g_pMemMapEntries); g_pMemMapEntries = NULL; g_cMemMapEntries = 0;
}

static BOOL Server_SetMemMapText(_In_reads_opt_(cb) PBYTE pb, _In_ DWORD cb)
{
    PBYTE p = NULL;
    if(cb && !pb) { return FALSE; }
    if(cb) {
        if(!(p = (PBYTE)LocalAlloc(0, cb))) { return FALSE; }
        memcpy(p, pb, cb);
    }
    LocalFree(g_pbMemMapText);
    g_pbMemMapText = p;
    g_cbMemMapText = cb;
    return TRUE;
}

static BOOL Server_SetMemMapStruct(_In_reads_opt_(c) PLC_MEMMAP_ENTRY p, _In_ DWORD c)
{
    PLC_MEMMAP_ENTRY pNew = NULL;
    if(c && !p) { return FALSE; }
    if(c) {
        if(!(pNew = (PLC_MEMMAP_ENTRY)LocalAlloc(0, c * sizeof(LC_MEMMAP_ENTRY)))) { return FALSE; }
        memcpy(pNew, p, c * sizeof(LC_MEMMAP_ENTRY));
    }
    LocalFree(g_pMemMapEntries);
    g_pMemMapEntries = pNew;
    g_cMemMapEntries = c;
    return TRUE;
}

//-----------------------------------------------------------------------------
// CLIENT TRACK / KEEPALIVE FUNCTIONALITY BELOW:
//-----------------------------------------------------------------------------

_Success_(return)
BOOL LeechRPC_ClientHandle_GetExisting(_In_ DWORD dwRpcClientID)
{
    VMProtectBegin("RPC::ClientGet");
    if(!dwRpcClientID) { VMProtectEnd(); return FALSE; }
    BOOL fExists = SessionManager_GetSession_C(dwRpcClientID);
    if(fExists) {
        SessionManager_IncrementActiveRequests_C(dwRpcClientID);
    }
    VMProtectEnd();
    return fExists;
}

_Success_(return)
BOOL LeechRPC_ClientHandle_New(_In_ DWORD dwRpcClientID)
{
    VMProtectBegin("RPC::ClientNew");
    if(!dwRpcClientID) { VMProtectEnd(); return FALSE; }
    BOOL r = SessionManager_AddSession_C(dwRpcClientID);
    VMProtectEnd();
    return r;
}

VOID LeechRPC_ClientHandle_Return(_In_ DWORD dwRpcClientID)
{
    VMProtectBegin("RPC::ClientRet");
    if(dwRpcClientID) {
        SessionManager_DecrementActiveRequests_C(dwRpcClientID);
    }
    VMProtectEnd();
}

VOID LeechRPC_ClientHandle_Close(_In_ DWORD dwRpcClientID, _In_ BOOL fReasonTimeout)
{
    VMProtectBegin("RPC::ClientClose");
    CHAR szTime[32];
    if(dwRpcClientID) {
        SessionManager_RemoveSession_C(dwRpcClientID);
        
        LeechSvc_GetTimeStamp(szTime);
        if(fReasonTimeout) {
            printf("[%s] CLOSE: Client ID %08lX timeout.\n", szTime, (unsigned long)dwRpcClientID);
        } else {
            printf("[%s] CLOSE: Client ID %08lX\n", szTime, (unsigned long)dwRpcClientID);
        }
    }
    VMProtectEnd();
}

VOID LeechRPC_ClientHandle_CloseAll()
{
    // Session cleanup is now handled by SessionManager
    printf("All client sessions will be cleaned up by SessionManager\n");
}
//----
//-------------------------------------------------------------------------
// GENERAL FUNCTIONALITY BELOW:
//-----------------------------------------------------------------------------

// Ensure functions are properly exported for C++ linking
#ifdef __cplusplus
extern "C" {
#endif

//-----------------------------------------------------------------------------
// READSCATTER PERFORMANCE OPTIMIZATION - Memory Pool and Zero-Copy Implementation
//-----------------------------------------------------------------------------
// Memory pool for high-performance data buffers - reduces allocation overhead
#define READSCATTER_BUFFER_POOL_SIZE 4
static struct {
    PBYTE pbBuffer;
    DWORD cbSize;
    BOOL fInUse;
    CRITICAL_SECTION csLock;
} g_ReadScatterBufferPool[READSCATTER_BUFFER_POOL_SIZE] = {0};
static BOOL g_fBufferPoolInitialized = FALSE;

static VOID InitReadScatterBufferPool(VOID)
{
    if(g_fBufferPoolInitialized) return;
    
    for(int i = 0; i < READSCATTER_BUFFER_POOL_SIZE; i++) {
        g_ReadScatterBufferPool[i].cbSize = 0x2000 << 12; // 32MB max buffer
        g_ReadScatterBufferPool[i].pbBuffer = (PBYTE)VirtualAlloc(NULL, 
            g_ReadScatterBufferPool[i].cbSize, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
        g_ReadScatterBufferPool[i].fInUse = FALSE;
        InitializeCriticalSection(&g_ReadScatterBufferPool[i].csLock);
    }
    g_fBufferPoolInitialized = TRUE;
}

static PBYTE AcquireBufferFromPool(DWORD cbRequired)
{
    if(!g_fBufferPoolInitialized) InitReadScatterBufferPool();
    
    for(int i = 0; i < READSCATTER_BUFFER_POOL_SIZE; i++) {
        if(TryEnterCriticalSection(&g_ReadScatterBufferPool[i].csLock)) {
            if(!g_ReadScatterBufferPool[i].fInUse && 
               g_ReadScatterBufferPool[i].cbSize >= cbRequired) {
                g_ReadScatterBufferPool[i].fInUse = TRUE;
                return g_ReadScatterBufferPool[i].pbBuffer;
            }
            LeaveCriticalSection(&g_ReadScatterBufferPool[i].csLock);
        }
    }
    return NULL; // Pool exhausted, fallback to LocalAlloc
}

static VOID ReleaseBufferToPool(PBYTE pbBuffer)
{
    for(int i = 0; i < READSCATTER_BUFFER_POOL_SIZE; i++) {
        if(g_ReadScatterBufferPool[i].pbBuffer == pbBuffer) {
            g_ReadScatterBufferPool[i].fInUse = FALSE;
            LeaveCriticalSection(&g_ReadScatterBufferPool[i].csLock);
            return;
        }
    }
}

static VOID CleanupReadScatterBufferPool(VOID)
{
    if(!g_fBufferPoolInitialized) return;
    
    for(int i = 0; i < READSCATTER_BUFFER_POOL_SIZE; i++) {
        if(g_ReadScatterBufferPool[i].pbBuffer) {
            VirtualFree(g_ReadScatterBufferPool[i].pbBuffer, 0, MEM_RELEASE);
            DeleteCriticalSection(&g_ReadScatterBufferPool[i].csLock);
        }
    }
    g_fBufferPoolInitialized = FALSE;
}

VOID __cdecl LeechRpcOnLoadInitialize()
{
    VMProtectBegin("RPC::OnLoad");
    ctxLeechRpc.fValid = TRUE;
    LeechRPC_CompressInitialize(&ctxLeechRpc.Compress);
    InitializeCriticalSection(&ctxLeechRpc.LockClientList);
    VMProtectEnd();
}

VOID __cdecl LeechRpcOnUnloadClose()
{
    VMProtectBegin("RPC::OnUnload");
    LeechRPC_ClientHandle_CloseAll();
    CleanupReadScatterBufferPool(); // Cleanup memory pool
    DeleteCriticalSection(&ctxLeechRpc.LockClientList);
    LeechRPC_CompressClose(&ctxLeechRpc.Compress);
    ZeroMemory(&ctxLeechRpc, sizeof(LEECHRPC_SERVER_CONTEXT));
    VMProtectEnd();
}



// Command handlers adapted for DriverInterface

error_status_t LeechRpc_CommandReadScatter(_In_ PLEECHRPC_MSG_BIN pReq, long *pcbOut, byte **ppbOut)
{
    VMProtectBeginVirtualization("RPC::ReadScatter");
    BOOL fOK;
    PLEECHRPC_MSG_BIN pRsp = NULL;
    PMEM_SCATTER pMEM_Src, pMEM_Dst;
    PPMEM_SCATTER ppMEMs = NULL;
    DWORD i, cMEMs, cbMax;
    PBYTE pbDataDst;
    DWORD cbDataOffset = 0, cbRead = 0;
    DWORD cbRsp;
    BOOL fUsingPool = FALSE;
    
    cMEMs = (DWORD)pReq->qwData[0];
    cbMax = (DWORD)pReq->qwData[1];
    
    // 1: verify incoming result
    fOK = (pReq->cb == cMEMs * sizeof(MEM_SCATTER)) && (cMEMs <= 0x2000) && (cbMax <= (cMEMs << 12));
    if(!fOK) { goto fail; }
    
    // 2: Pre-calculate response size and allocate in one go - ZERO COPY OPTIMIZATION
    cbRsp = sizeof(LEECHRPC_MSG_BIN) + cMEMs * sizeof(MEM_SCATTER) + cbMax;
    if(!(pRsp = (PLEECHRPC_MSG_BIN)LocalAlloc(LMEM_ZEROINIT, cbRsp))) { goto fail; }
    
    // 3: Setup response header immediately
    pRsp->cbMsg = cbRsp;
    pRsp->dwMagic = LEECHRPC_MSGMAGIC;
    pRsp->fMsgResult = TRUE;
    pRsp->tpMsg = LEECHRPC_MSGTYPE_READSCATTER_RSP;
    
    // 4: Copy MEM_SCATTER structures to response
    memcpy(pRsp->pb, pReq->pb, pReq->cb);
    pbDataDst = pRsp->pb + pReq->cb; // Data buffer starts here - ZERO COPY TARGET
    
    // 5: Allocate pointer array and setup direct pointers to response buffer
    if(!(ppMEMs = (PPMEM_SCATTER)LocalAlloc(LMEM_ZEROINIT, cMEMs * sizeof(PMEM_SCATTER)))) { goto fail; }
    
    // 6: COMBINED LOOP OPTIMIZATION - Single pass to setup everything
    pMEM_Dst = (PMEM_SCATTER)pRsp->pb;
    for(i = 0; i < cMEMs; i++) {
        // Point directly to response buffer - eliminates later memcpy!
        pMEM_Dst->pb = pbDataDst + cbDataOffset;
        cbDataOffset += pMEM_Dst->cb;
        ppMEMs[i] = pMEM_Dst;
        pMEM_Dst = pMEM_Dst + 1;
    }
    if(cbDataOffset > cbMax) { goto fail; }
    
    // 7: Call driver interface - data goes directly to response buffer
    DriverInterface_ReadScatter_C(cMEMs, ppMEMs);
    
    // 8: COMBINED LOOP - Count successful reads in single pass  
    pMEM_Dst = (PMEM_SCATTER)pRsp->pb;
    for(i = 0, cbRead = 0; i < cMEMs; i++) {
        if(pMEM_Dst->f) {
            cbRead += pMEM_Dst->cb;
        }
        pMEM_Dst = pMEM_Dst + 1;
    }
    
    // 9: Adjust final response size
    pRsp->cb = pReq->cb + cbRead;
    pRsp->cbMsg = sizeof(LEECHRPC_MSG_BIN) + pReq->cb + cbRead;
    pRsp->qwData[0] = cMEMs;
    
    // 10: SMART COMPRESSION - Only compress if data is large and likely compressible
    BOOL fShouldCompress = !(pReq->flags & LEECHRPC_FLAG_NOCOMPRESS) && 
                          (cbRead > (2 * 1024 * 1024)) && // Only compress >2MB
                          (cbRead < cbMax * 0.8); // Skip if mostly successful reads (less compressible)
    
    if(fShouldCompress) {
        LeechRPC_Compress(&ctxLeechRpc.Compress, pRsp, FALSE);
    }
    
    *pcbOut = pRsp->cbMsg;
    *ppbOut = (PBYTE)pRsp;
    LocalFree(ppMEMs);
    VMProtectEnd();
    return 0;
    
fail:
    *pcbOut = 0;
    *ppbOut = NULL;
    LocalFree(pRsp);
    LocalFree(ppMEMs);
    VMProtectEnd();
    return (error_status_t)-1;
}

error_status_t LeechRpc_CommandWriteScatter(_In_ PLEECHRPC_MSG_BIN pReq, long *pcbOut, byte **ppbOut)
{
    VMProtectBeginVirtualization("RPC::WriteScatter");
    PBOOL pfRsp;
    PLEECHRPC_MSG_BIN pRsp = NULL;
    PMEM_SCATTER pMEM, pMEMs;
    PPMEM_SCATTER ppMEMs = NULL;
    DWORD i, cMEMs, cbRsp;
    PBYTE pbData = NULL;
    
    cMEMs = (DWORD)pReq->qwData[0];
    
    // 1: verify and fixup incoming data 
    pMEMs = (PMEM_SCATTER)pReq->pb;
    pbData = pReq->pb + cMEMs * sizeof(MEM_SCATTER);
    if(pReq->cb != cMEMs * (sizeof(MEM_SCATTER) + 0x1000)) { goto fail; }
    if(!(ppMEMs = (PPMEM_SCATTER)LocalAlloc(LMEM_ZEROINIT, cMEMs * sizeof(PMEM_SCATTER)))) { goto fail; }
    for(i = 0; i < cMEMs; i++) {
        ppMEMs[i] = pMEM = pMEMs + i;
        if((pMEM->cb > 0x1000) || (pMEM->iStack > MEM_SCATTER_STACK_SIZE - 4)) { goto fail; }
        pMEM->pb = pbData;
        pbData += pMEM->cb;
    }
    
    // 2: call DriverInterface & return result
    DriverInterface_WriteScatter_C(cMEMs, ppMEMs);
    cbRsp = sizeof(LEECHRPC_MSG_BIN) + cMEMs * sizeof(BOOL);
    if(!(pRsp = (PLEECHRPC_MSG_BIN)LocalAlloc(LMEM_ZEROINIT, cbRsp))) { goto fail; }
    pRsp->cbMsg = cbRsp;
    pRsp->dwMagic = LEECHRPC_MSGMAGIC;
    pRsp->fMsgResult = TRUE;
    pRsp->tpMsg = LEECHRPC_MSGTYPE_WRITESCATTER_RSP;
    pRsp->cb = cMEMs * sizeof(BOOL);
    pfRsp = (PBOOL)pRsp->pb;
    for(i = 0; i < cMEMs; i++) {
        pfRsp[i] = pMEMs[i].f;
    }
    pRsp->qwData[0] = cMEMs;
    LeechRPC_Compress(&ctxLeechRpc.Compress, pRsp, (pReq->flags & LEECHRPC_FLAG_NOCOMPRESS));
    *pcbOut = pRsp->cbMsg;
    *ppbOut = (PBYTE)pRsp;
    LocalFree(ppMEMs);
    VMProtectEnd();
    return 0;
    
fail:
    *pcbOut = 0;
    *ppbOut = NULL;
    LocalFree(pRsp);
    LocalFree(ppMEMs);
    VMProtectEnd();
    return (error_status_t)-1;
}

error_status_t LeechRpc_CommandOpen(_In_ PLEECHRPC_MSG_OPEN pReq, long *pcbOut, byte **ppbOut)
{
    VMProtectBegin("RPC::Open");
    DWORD cbRsp;
    CHAR szTime[32];
    PLEECHRPC_MSG_OPEN pRsp = NULL;
    
    // For CustomLeechServer, we always return success if driver interface is available
    BOOL fValidOpen = TRUE;  // Assume driver interface is available through C++ bridge
    
    if(fValidOpen && !LeechRPC_ClientHandle_New(pReq->dwRpcClientID)) {
        fValidOpen = FALSE;
    }
    
    cbRsp = sizeof(LEECHRPC_MSG_OPEN);
    if(!(pRsp = (PLEECHRPC_MSG_OPEN)LocalAlloc(LMEM_ZEROINIT, cbRsp))) {
        *pcbOut = 0;
        *ppbOut = NULL;
        return (error_status_t)-1;
    }
    
    pRsp->cbMsg = cbRsp;
    pRsp->dwMagic = LEECHRPC_MSGMAGIC;
    pRsp->fMsgResult = TRUE;
    pRsp->fValidOpen = fValidOpen;
    
    if(pRsp->fValidOpen) {
        LeechSvc_GetTimeStamp(szTime);
        printf("[%s] OPEN: Client ID %08lX\n", szTime, (unsigned long)pReq->dwRpcClientID);

        // On successful logical open, persist generated device name for licensing
        {
            extern void CLS_OnLoginSuccess_WriteDeviceName(void);
            CLS_OnLoginSuccess_WriteDeviceName();
        }
        
        // Setup basic config response
        memcpy(&pRsp->cfg, &pReq->cfg, sizeof(LC_CONFIG));
        pRsp->cfg.fRemoteDisableCompress = pRsp->cfg.fRemoteDisableCompress || !ctxLeechRpc.Compress.fValid;
        pRsp->cfg.fWritable = TRUE;  // Our driver interface supports writing
        pRsp->cfg.fVolatile = TRUE;  // Live memory access
        pRsp->cfg.fRemote = TRUE;    // This is a remote connection
        strcpy_s(pRsp->cfg.szDeviceName, MAX_PATH, "custom-driver");
        // Provide physical address max early to help DTB validation
        {
            extern QWORD DriverInterface_GetPhysicalMax_C();
            QWORD paMax = DriverInterface_GetPhysicalMax_C();
            if(paMax == 0) {
                // Fallback to a large safe upper bound if driver doesn't provide one
                paMax = 0x0000FFFFFFFFFFFFULL;
            }
            pRsp->cfg.paMax = paMax;
        }
        pRsp->flags = LEECHRPC_FLAG_FNEXIST_ReadScatterMEM | LEECHRPC_FLAG_FNEXIST_WriteScatterMEM | LEECHRPC_FLAG_FNEXIST_Close | LEECHRPC_FLAG_FNEXIST_GetOption;
    }
    
    pRsp->tpMsg = LEECHRPC_MSGTYPE_OPEN_RSP;
    *pcbOut = pRsp->cbMsg;
    *ppbOut = (PBYTE)pRsp;
    VMProtectEnd();
    return 0;
}

// Main message dispatch function - adapted from LeechAgent
error_status_t LeechRpc_ReservedSubmitCommand_C(
    /* [in] */ long cbIn,
    /* [size_is][in] */ byte *pbIn,
    /* [out] */ long *pcbOut,
    /* [size_is][size_is][out] */ byte **ppbOut)
{
    VMProtectBeginMutation("RPC::Dispatch");
    BOOL fClientExists = FALSE;
    BOOL fFreeReqBin = FALSE;
    error_status_t status = 0;
    PLEECHRPC_MSG_HDR pReq = NULL;
    PLEECHRPC_MSG_HDR pRsp = NULL;
    PLEECHRPC_MSG_OPEN pReqOpen = NULL;
    PLEECHRPC_MSG_DATA pReqData = NULL;
    PLEECHRPC_MSG_DATA pRspData = NULL;
    PLEECHRPC_MSG_BIN pReqBin = NULL;
    PLEECHRPC_MSG_BIN pRspBin = NULL;
    
    // 1: sanity checks in incoming data
    if(!ctxLeechRpc.fValid) { return status; }
    if(cbIn < sizeof(LEECHRPC_MSG_HDR)) { return status; }
    pReq = (PLEECHRPC_MSG_HDR)pbIn;
    if((pReq->dwMagic != LEECHRPC_MSGMAGIC) || (pReq->tpMsg > LEECHRPC_MSGTYPE_MAX) || (pReq->cbMsg < sizeof(LEECHRPC_MSG_HDR))) { return status; }
    
    fClientExists = LeechRPC_ClientHandle_GetExisting(pReq->dwRpcClientID);
    if(!fClientExists && !((pReq->tpMsg == LEECHRPC_MSGTYPE_PING_REQ) || (pReq->tpMsg == LEECHRPC_MSGTYPE_OPEN_REQ) || (pReq->tpMsg == LEECHRPC_MSGTYPE_CLOSE_REQ))) { 
        goto fail; 
    }
    
    switch(pReq->tpMsg) {
        case LEECHRPC_MSGTYPE_PING_REQ:
        case LEECHRPC_MSGTYPE_CLOSE_REQ:
        case LEECHRPC_MSGTYPE_KEEPALIVE_REQ:
            if(pReq->cbMsg != sizeof(LEECHRPC_MSG_HDR)) { goto fail; }
            break;
        case LEECHRPC_MSGTYPE_OPEN_REQ:
            if(pReq->cbMsg != sizeof(LEECHRPC_MSG_OPEN)) { goto fail; }
            pReqOpen = (PLEECHRPC_MSG_OPEN)pReq;
            break;
        case LEECHRPC_MSGTYPE_GETOPTION_REQ:
        case LEECHRPC_MSGTYPE_SETOPTION_REQ:
            if(pReq->cbMsg != sizeof(LEECHRPC_MSG_DATA)) { goto fail; }
            pReqData = (PLEECHRPC_MSG_DATA)pReq;
            break;
        case LEECHRPC_MSGTYPE_READSCATTER_REQ:
        case LEECHRPC_MSGTYPE_WRITESCATTER_REQ:
        case LEECHRPC_MSGTYPE_COMMAND_REQ:
            if(pReq->cbMsg != sizeof(LEECHRPC_MSG_BIN) + ((PLEECHRPC_MSG_BIN)pReq)->cb) { goto fail; }
            if(((PLEECHRPC_MSG_BIN)pReq)->cbDecompress) {
                if(!LeechRPC_Decompress(&ctxLeechRpc.Compress, (PLEECHRPC_MSG_BIN)pReq, &pReqBin)) { goto fail; }
                fFreeReqBin = TRUE;
            } else {
                pReqBin = ((PLEECHRPC_MSG_BIN)pReq);
            }
            break;
        default:
            goto fail;
    }
    
    // 2: dispatch
    switch(pReq->tpMsg) {
        case LEECHRPC_MSGTYPE_PING_REQ:
            if(!(pRsp = (PLEECHRPC_MSG_HDR)LocalAlloc(0, sizeof(LEECHRPC_MSG_HDR)))) { goto fail; }
            pRsp->cbMsg = sizeof(LEECHRPC_MSG_HDR);
            pRsp->dwMagic = LEECHRPC_MSGMAGIC;
            pRsp->fMsgResult = TRUE;
            pRsp->tpMsg = LEECHRPC_MSGTYPE_PING_RSP;
            *pcbOut = pRsp->cbMsg;
            *ppbOut = (PBYTE)pRsp;
            goto finish;
            
        case LEECHRPC_MSGTYPE_KEEPALIVE_REQ:
            if(!(pRsp = (PLEECHRPC_MSG_HDR)LocalAlloc(0, sizeof(LEECHRPC_MSG_HDR)))) { goto fail; }
            pRsp->cbMsg = sizeof(LEECHRPC_MSG_HDR);
            pRsp->dwMagic = LEECHRPC_MSGMAGIC;
            pRsp->fMsgResult = TRUE;
            pRsp->tpMsg = LEECHRPC_MSGTYPE_KEEPALIVE_RSP;
            *pcbOut = pRsp->cbMsg;
            *ppbOut = (PBYTE)pRsp;
            goto finish;
            
        case LEECHRPC_MSGTYPE_OPEN_REQ:
            if(pReqOpen) {
                LeechRpc_CommandOpen(pReqOpen, pcbOut, ppbOut);
            }
            goto finish;
            
        case LEECHRPC_MSGTYPE_READSCATTER_REQ:
            status = LeechRpc_CommandReadScatter(pReqBin, pcbOut, ppbOut);
            if(fFreeReqBin) { LocalFree(pReqBin); pReqBin = NULL; }
            goto finish;
            
        case LEECHRPC_MSGTYPE_WRITESCATTER_REQ:
            status = LeechRpc_CommandWriteScatter(pReqBin, pcbOut, ppbOut);
            if(fFreeReqBin) { LocalFree(pReqBin); pReqBin = NULL; }
            goto finish;
            
        case LEECHRPC_MSGTYPE_CLOSE_REQ:
            LeechRPC_ClientHandle_Return(pReq->dwRpcClientID);
            LeechRPC_ClientHandle_Close(pReq->dwRpcClientID, FALSE);
            if(!(pRsp = (PLEECHRPC_MSG_HDR)LocalAlloc(0, sizeof(LEECHRPC_MSG_HDR)))) { goto fail; }
            pRsp->cbMsg = sizeof(LEECHRPC_MSG_HDR);
            pRsp->dwMagic = LEECHRPC_MSGMAGIC;
            pRsp->fMsgResult = TRUE;
            pRsp->tpMsg = LEECHRPC_MSGTYPE_CLOSE_RSP;
            *pcbOut = pRsp->cbMsg;
            *ppbOut = (PBYTE)pRsp;
            goto finish;
            
        case LEECHRPC_MSGTYPE_GETOPTION_REQ:
            // Use enhanced GETOPTION handler with DTB support
            {
                extern error_status_t LeechRpc_CommandGetOption_DTB(
                    _In_ PLEECHRPC_MSG_DATA pReqData,
                    _Out_ long *pcbOut,
                    _Out_ byte **ppbOut);
                    
                status = LeechRpc_CommandGetOption_DTB(pReqData, pcbOut, ppbOut);
                goto finish;
            }
            
        case LEECHRPC_MSGTYPE_SETOPTION_REQ:
            if(!(pRsp = (PLEECHRPC_MSG_HDR)LocalAlloc(0, sizeof(LEECHRPC_MSG_HDR)))) { goto fail; }
            pRsp->cbMsg = sizeof(LEECHRPC_MSG_HDR);
            pRsp->dwMagic = LEECHRPC_MSGMAGIC;
            pRsp->fMsgResult = FALSE;
            pRsp->tpMsg = LEECHRPC_MSGTYPE_SETOPTION_RSP;
            *pcbOut = pRsp->cbMsg;
            *ppbOut = (PBYTE)pRsp;
            goto finish;
            
        case LEECHRPC_MSGTYPE_COMMAND_REQ:
            {
                QWORD fCMD = pReqBin ? pReqBin->qwData[0] : 0;
                DWORD cbIn = pReqBin ? pReqBin->cb : 0;
                PBYTE pbIn = pReqBin ? pReqBin->pb : NULL;
                BOOL fOK = FALSE;
                PBYTE pbOut = NULL;
                DWORD cbOut = 0;

                // Handle selected LC_CMD_* minimal set
                switch(fCMD) {
                    case LC_CMD_MEMMAP_SET:
                        // Store text memmap; succeed even if empty
                        fOK = Server_SetMemMapText(pbIn, cbIn);
                        break;
                    case LC_CMD_MEMMAP_GET:
                        if(g_cbMemMapText) {
                            if((pbOut = (PBYTE)LocalAlloc(0, g_cbMemMapText))) {
                                memcpy(pbOut, g_pbMemMapText, g_cbMemMapText);
                                cbOut = g_cbMemMapText;
                                fOK = TRUE;
                            }
                        } else {
                            // Return empty blob to indicate no preset map
                            cbOut = 0; fOK = TRUE;
                        }
                        break;
                    case LC_CMD_MEMMAP_SET_STRUCT:
                        if(cbIn % sizeof(LC_MEMMAP_ENTRY)) {
                            fOK = FALSE;
                        } else {
                            fOK = Server_SetMemMapStruct((PLC_MEMMAP_ENTRY)pbIn, cbIn ? (cbIn / sizeof(LC_MEMMAP_ENTRY)) : 0);
                        }
                        break;
                    case LC_CMD_MEMMAP_GET_STRUCT:
                        if(g_cMemMapEntries && g_pMemMapEntries) {
                            cbOut = g_cMemMapEntries * sizeof(LC_MEMMAP_ENTRY);
                            if((pbOut = (PBYTE)LocalAlloc(0, cbOut))) {
                                memcpy(pbOut, g_pMemMapEntries, cbOut);
                                fOK = TRUE;
                            } else {
                                cbOut = 0;
                            }
                        } else {
                            // No struct map stored
                            cbOut = 0; fOK = TRUE;
                        }
                        break;
                    case LC_CMD_STATISTICS_GET:
                        {
                            // Return zeroed statistics with valid version/frequency
                            LC_STATISTICS st = { 0 };
                            LARGE_INTEGER freq;
                            st.dwVersion = LC_STATISTICS_VERSION;
                            if(QueryPerformanceFrequency(&freq)) {
                                st.qwFreq = (QWORD)freq.QuadPart;
                            }
                            cbOut = sizeof(LC_STATISTICS);
                            if((pbOut = (PBYTE)LocalAlloc(0, cbOut))) {
                                memcpy(pbOut, &st, cbOut);
                                fOK = TRUE;
                            } else {
                                cbOut = 0;
                            }
                        }
                        break;
                    case 0x4000060000000000ULL: // LC_CMD_PROCESS_LIST - 自定义进程列表命令
                        {
                            // 调用 DriverInterface 获取进程列表
                            extern PVOID LeechRpc_GetDriverInterface(void); // 声明外部C函数
                            extern BOOL DriverInterface_EnumerateProcesses_C(PVOID pDriver, PVOID* ppBuffer, PDWORD pcbSize); // 声明C包装函数
                            extern BOOL DriverInterface_IsInitialized_C(PVOID pDriver); // 声明C包装函数
                            
                            PVOID pDriver = LeechRpc_GetDriverInterface();
                            if (pDriver && DriverInterface_IsInitialized_C(pDriver)) {
                                PVOID pProcessBuffer = NULL;
                                DWORD cbProcessBuffer = 0;
                                
                                if (DriverInterface_EnumerateProcesses_C(pDriver, &pProcessBuffer, &cbProcessBuffer)) {
                                    cbOut = cbProcessBuffer;
                                    if ((pbOut = (PBYTE)LocalAlloc(0, cbOut))) {
                                        memcpy(pbOut, pProcessBuffer, cbOut);
                                        fOK = TRUE;
                                    } else {
                                        cbOut = 0;
                                    }
                                    free(pProcessBuffer); // 释放 DriverInterface 分配的内存
                                } else {
                                    fOK = FALSE;
                                    cbOut = 0;
                                }
                            } else {
                                fOK = FALSE;
                                cbOut = 0;
                            }
                        }
                        break;
                    default:
                        fOK = FALSE;
                        break;
                }

                // Build response
                if(!(pRspBin = (PLEECHRPC_MSG_BIN)LocalAlloc(LMEM_ZEROINIT, sizeof(LEECHRPC_MSG_BIN) + cbOut))) {
                    LocalFree(pbOut);
                    goto fail;
                }
                pRspBin->dwMagic = LEECHRPC_MSGMAGIC;
                pRspBin->tpMsg = LEECHRPC_MSGTYPE_COMMAND_RSP;
                pRspBin->fMsgResult = fOK;
                pRspBin->cb = cbOut;
                if(cbOut && pbOut) { memcpy(pRspBin->pb, pbOut, cbOut); }
                pRspBin->cbMsg = sizeof(LEECHRPC_MSG_BIN) + cbOut;
                LeechRPC_Compress(&ctxLeechRpc.Compress, pRspBin, (pReq->flags & LEECHRPC_FLAG_NOCOMPRESS));
                *pcbOut = pRspBin->cbMsg;
                *ppbOut = (PBYTE)pRspBin;
                LocalFree(pbOut);
                if(fFreeReqBin) { LocalFree(pReqBin); pReqBin = NULL; }
                goto finish;
            }
            
        default:
            goto fail;
    }
    
finish:
    if(pReq && fClientExists) { 
        LeechRPC_ClientHandle_Return(pReq->dwRpcClientID); 
    }
    VMProtectEnd();
    return status;
    
fail:
    if(fClientExists) {
        LeechRPC_ClientHandle_Return(pReq->dwRpcClientID);
    }
    if(fFreeReqBin) { 
        LocalFree(pReqBin); 
        pReqBin = NULL; 
    }
    *pcbOut = 0;
    *ppbOut = NULL;
    VMProtectEnd();
    return (error_status_t)-1;
}

// gRPC command handler wrapper
VOID __cdecl LeechGRPC_ReservedSubmitCommand(_In_opt_ PVOID ctx, _In_ PBYTE pbIn, _In_ SIZE_T cbIn, _Out_ PBYTE *ppbOut, _Out_ SIZE_T *pcbOut)
{
    VMProtectBeginVirtualization("RPC::GRPCWrapper");
    LeechRpc_ReservedSubmitCommand_C((long)cbIn, pbIn, (long*)pcbOut, ppbOut);
    VMProtectEnd();
}

#ifdef __cplusplus
}
#endif