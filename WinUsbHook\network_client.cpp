#include "network_client.h"
#include "console_ui.h"
#include <iostream>
#include <vector>

namespace NetworkClient {

    static bool g_initialized = false;

    bool Initialize() {
        if (g_initialized) return true;

        WSADATA wsaData;
        int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
        if (result != 0) {
            LOG_ERROR("WSAStartup failed with error: %d", result);
            return false;
        }

        g_initialized = true;
        LOG_SUCCESS("Network module initialized successfully");
        return true;
    }

    void Cleanup() {
        if (g_initialized) {
            WSACleanup();
            g_initialized = false;
            LOG_SUCCESS("Network module cleaned up");
        }
    }

    bool GetPrimaryIPv4AndPrefix(ULONG* ipHost, ULONG* prefix) {
        if (!ipHost || !prefix) return false;

        PIP_ADAPTER_ADDRESSES pAddrs = nullptr;
        ULONG bufLen = 0;
        
        // Get adapter buffer size
        GetAdaptersAddresses(AF_INET, GAA_FLAG_INCLUDE_PREFIX, NULL, pAddrs, &bufLen);
        if (bufLen == 0) return false;

        pAddrs = (PIP_ADAPTER_ADDRESSES)malloc(bufLen);
        if (!pAddrs) return false;

        bool found = false;
        if (GetAdaptersAddresses(AF_INET, GAA_FLAG_INCLUDE_PREFIX, NULL, pAddrs, &bufLen) == ERROR_SUCCESS) {
            for (PIP_ADAPTER_ADDRESSES pCurr = pAddrs; pCurr && !found; pCurr = pCurr->Next) {
               
                if (pCurr->IfType == IF_TYPE_SOFTWARE_LOOPBACK || 
                    pCurr->OperStatus != IfOperStatusUp) continue;

                for (PIP_ADAPTER_UNICAST_ADDRESS pUni = pCurr->FirstUnicastAddress; pUni; pUni = pUni->Next) {
                    if (pUni->Address.lpSockaddr->sa_family == AF_INET) {
                        sockaddr_in* pAddr = (sockaddr_in*)pUni->Address.lpSockaddr;
                        ULONG addr = ntohl(pAddr->sin_addr.S_un.S_addr);
                        
                        if ((addr & 0xFF000000) == 0x7F000000 ||  // 127.x.x.x
                            (addr & 0xFFFF0000) == 0xA9FE0000) {  // 169.254.x.x
                            continue;
                        }

                        *ipHost = addr;
                        *prefix = pUni->OnLinkPrefixLength;
                        found = true;
                        break;
                    }
                }
            }
        }

        free(pAddrs);
        return found;
    }

    bool VerifyServerHandshake(const char* serverIP, USHORT port, int timeoutMs) {
        if (!serverIP) return false;

        SOCKET sock = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
        if (sock == INVALID_SOCKET) return false;

        u_long nonBlock = 1;
        ioctlsocket(sock, FIONBIO, &nonBlock);

        sockaddr_in addr = {0};
        addr.sin_family = AF_INET;
        addr.sin_port = htons(port);
        if (InetPtonA(AF_INET, serverIP, &addr.sin_addr) != 1) {
            closesocket(sock);
            return false;
        }

        connect(sock, (sockaddr*)&addr, sizeof(addr));
        
        fd_set writeSet;
        FD_ZERO(&writeSet);
        FD_SET(sock, &writeSet);
        timeval timeout = {0, timeoutMs * 1000};

        if (select(0, NULL, &writeSet, NULL, &timeout) <= 0) {
            closesocket(sock);
            return false;
        }

        const char* handshakeReq = "MEMPROCFS_HANDSHAKE_Q";
        if (send(sock, handshakeReq, (int)strlen(handshakeReq), 0) == SOCKET_ERROR) {
            closesocket(sock);
            return false;
        }

        fd_set readSet;
        FD_ZERO(&readSet);
        FD_SET(sock, &readSet);
        timeout = {0, timeoutMs * 1000};

        if (select(0, &readSet, NULL, NULL, &timeout) <= 0) {
            closesocket(sock);
            return false;
        }

        char response[64] = {0};
        int bytesReceived = recv(sock, response, sizeof(response) - 1, 0);
        closesocket(sock);

        if (bytesReceived <= 0) return false;

        const char* expectedResp = "MEMPROCFS_HANDSHAKE_A";
        return (strcmp(response, expectedResp) == 0);
    }

    bool DiscoverServerInLAN(std::string& outServerIP, USHORT handshakePort) {
        ULONG ipHost = 0, prefix = 0;
        if (!GetPrimaryIPv4AndPrefix(&ipHost, &prefix)) {
            LOG_ERROR("Failed to get primary IPv4 address");
            return false;
        }

        // �������IP��Ϣ
        IN_ADDR localAddr;
        localAddr.s_addr = htonl(ipHost);
        char localIpStr[INET_ADDRSTRLEN] = {0};
        if (InetNtopA(AF_INET, &localAddr, localIpStr, sizeof(localIpStr))) {
            #ifdef DEBUGLOG
            LOG_INFO("Local IP: %s", localIpStr);
            #endif
        }

        // Scan /24 range (x.x.x.1 - x.x.x.254)
        ULONG net24 = ipHost & 0xFFFFFF00UL;
        const int timeoutMs = 200;

        #ifdef DEBUGLOG
        LOG_INFO("Scanning local network for servers...");
        #endif

        for (ULONG host = 1; host <= 254; ++host) {
            ULONG candidate = net24 | host;
            if (candidate == ipHost) continue; // Skip current host

            IN_ADDR addr;
            addr.s_addr = htonl(candidate);
            char ipStr[INET_ADDRSTRLEN] = {0};
            if (!InetNtopA(AF_INET, &addr, ipStr, sizeof(ipStr))) continue;

            if (VerifyServerHandshake(ipStr, handshakePort, timeoutMs)) {
                outServerIP = ipStr;
                LOG_SUCCESS("Found server: %s (This server will no longer accept other clients)", ipStr);
                return true;
            }
        }

        LOG_ERROR("No server found in local network");
        return false;
    }

    ConnectionResult ConnectToServer(const ConnectionConfig& config) {
        ConnectionResult result;

        if (!Initialize()) {
            result.errorMessage = "Failed to initialize network module";
            return result;
        }

        if (config.enableInteractiveMode) {
            // ��ʾ����ѡ��˵�
            ConsoleUI::PrintSeparator();
            ConsoleUI::PrintSection("Connection Options");
           
            LOG_INFO("1. Auto scan local network");
            LOG_INFO("2. Manually enter server IP address");
            
            
           
            LOG_INFO("Select option (1-2): ");
           
            fflush(stdout);

            int choice = 0;
            char input[16] = {0};
            if (fgets(input, sizeof(input), stdin)) {
                choice = atoi(input);
            }

            if (choice == 2) {
               
                LOG_INFO("Enter server IP address:");
               
                fflush(stdout);
                char ipInput[32] = {0};
                if (fgets(ipInput, sizeof(ipInput), stdin)) {
                    // Remove trailing newline
                    ipInput[strcspn(ipInput, "\r\n")] = 0;

                    LOG_INFO("Connecting to specified server: %s", ipInput);
                    
                    if (VerifyServerHandshake(ipInput, config.handshakePort, config.timeoutMs)) {
                        result.success = true;
                        result.serverIP = ipInput;
                        result.rpcUri = GenerateRpcUri(ipInput);
                        LOG_SUCCESS("Successfully connected to server: %s", ipInput);
                    } else {
                        result.errorMessage = "Failed to connect to specified server: " + std::string(ipInput);
                        LOG_ERROR("%s", result.errorMessage.c_str());
                    }
                }
            } else {
                // Auto scan (default option)
                
                LOG_INFO("Scanning local network for servers...");
                
                if (DiscoverServerInLAN(result.serverIP, config.handshakePort)) {
                    result.success = true;
                    result.rpcUri = GenerateRpcUri(result.serverIP);
                } else {
                    result.errorMessage = "No server found in local network";
                }
            }
        } else {
            // Direct scan mode
            if (DiscoverServerInLAN(result.serverIP, config.handshakePort)) {
                result.success = true;
                result.rpcUri = GenerateRpcUri(result.serverIP);
            } else {
                result.errorMessage = "Auto-discovery failed: no server found in LAN";
            }
        }

        return result;
    }

    ConnectionResult ConnectToSpecificServer(const std::string& serverIP, const ConnectionConfig& config) {
        ConnectionResult result;

        if (!Initialize()) {
            result.errorMessage = "Failed to initialize network module";
            return result;
        }

        if (VerifyServerHandshake(serverIP.c_str(), config.handshakePort, config.timeoutMs)) {
            result.success = true;
            result.serverIP = serverIP;
            result.rpcUri = GenerateRpcUri(serverIP);
        } else {
            result.errorMessage = "Failed to connect to server: " + serverIP;
        }

        return result;
    }

    std::string GenerateRpcUri(const std::string& serverIP, bool insecure, bool nocompress) {
        std::string uri = "rpc://";
        if (insecure) uri += "insecure:";
        uri += serverIP;
        if (nocompress) uri += ":nocompress";
        return uri;
    }

} // namespace NetworkClient