#pragma once

#include <windows.h>
#include <string>
#include <vector>

namespace PEUtils {

    // PEB结构体定义
    struct ModuleInfo {
        std::string name;
        std::string fullPath;
        HMODULE baseAddress;
        SIZE_T imageSize;
        PVOID entryPoint;
    };

    // PE导出信息
    struct ExportInfo {
        std::string name;
        WORD ordinal;
        PVOID address;
        bool isForwarder;
        std::string forwarderName;
    };

    // PE导入信息
    struct ImportInfo {
        std::string dllName;
        std::string functionName;
        WORD ordinal;
        PVOID address;
        bool isByOrdinal;
    };

    // PE节信息
    struct SectionInfo {
        std::string name;
        PVOID virtualAddress;
        DWORD virtualSize;
        DWORD rawSize;
        DWORD characteristics;
        bool isExecutable;
        bool isWritable;
        bool isReadable;
    };

    // PEB模块查找
    HMODULE FindModuleByName(const std::string& moduleName);
    HMODULE FindModuleByAddress(PVOID address);
    std::vector<ModuleInfo> EnumerateLoadedModules();
    ModuleInfo GetModuleInfo(HMODULE module);

    // PE解析
    bool IsValidPE(HMODULE module);
    PIMAGE_DOS_HEADER GetDosHeader(HMODULE module);
    PIMAGE_NT_HEADERS GetNtHeaders(HMODULE module);
    PIMAGE_SECTION_HEADER GetSectionHeaders(HMODULE module);

    // 导出表解析
    std::vector<ExportInfo> ParseExportTable(HMODULE module);
    PVOID GetExportAddress(HMODULE module, const std::string& functionName);
    PVOID GetExportAddressByOrdinal(HMODULE module, WORD ordinal);
    std::string ResolveForwarder(const std::string& forwarder);

    // 导入表解析
    std::vector<ImportInfo> ParseImportTable(HMODULE module);
    PVOID GetImportAddress(HMODULE module, const std::string& dllName, const std::string& functionName);
    bool PatchImportAddress(HMODULE module, const std::string& dllName, const std::string& functionName, PVOID newAddress, PVOID* oldAddress = nullptr);

    // 节操作
    std::vector<SectionInfo> GetSections(HMODULE module);
    SectionInfo* FindSection(HMODULE module, const std::string& sectionName);
    PVOID GetSectionAddress(HMODULE module, const std::string& sectionName);
    SIZE_T GetSectionSize(HMODULE module, const std::string& sectionName);

    // 地址转换
    DWORD RvaToFileOffset(HMODULE module, DWORD rva);
    DWORD FileOffsetToRva(HMODULE module, DWORD offset);
    PVOID RvaToVa(HMODULE module, DWORD rva);

    // PE入口点
    PVOID GetEntryPoint(HMODULE module);
    PVOID GetOriginalEntryPoint(HMODULE module);

    // 资源查找
    PVOID FindResource(HMODULE module, DWORD resourceId, const std::string& resourceType);
    std::vector<DWORD> EnumerateResources(HMODULE module, const std::string& resourceType);

    // 重定位表处理
    bool ProcessRelocations(HMODULE module, PVOID newBase);
    std::vector<PVOID> GetRelocationAddresses(HMODULE module);

    // TLS回调
    typedef VOID(NTAPI* PIMAGE_TLS_CALLBACK)(PVOID DllHandle, DWORD Reason, PVOID Reserved);
    std::vector<PIMAGE_TLS_CALLBACK> GetTlsCallbacks(HMODULE module);

    // 延迟导入
    struct DelayImportInfo {
        std::string dllName;
        std::string functionName;
        PVOID address;
        bool isLoaded;
    };
    std::vector<DelayImportInfo> ParseDelayImports(HMODULE module);

    // 异常表
    struct ExceptionInfo {
        PVOID startAddress;
        PVOID endAddress;
        PVOID unwindInfo;
    };
    std::vector<ExceptionInfo> GetExceptionTable(HMODULE module);

    // 调试信息
    struct DebugInfo {
        DWORD type;
        DWORD dataSize;
        PVOID data;
        std::string description;
    };
    std::vector<DebugInfo> GetDebugInfo(HMODULE module);

    // 实用功能
    std::string GetModulePath(HMODULE module);
    std::string GetModuleName(HMODULE module);
    DWORD GetModuleSize(HMODULE module);
    DWORD GetImageChecksum(HMODULE module);
    DWORD CalculateChecksum(HMODULE module);

    // PE Loader相关
    HMODULE ManualMapPE(const std::string& filePath);
    bool UnmapPE(HMODULE module);
    bool FixImports(HMODULE module);
    bool CallTlsCallbacks(HMODULE module, DWORD reason);

    // 基于PEB的API替代函数
    FARPROC GetProcAddressPEB(HMODULE hModule, const char* lpProcName);
    FARPROC GetProcAddressByModuleName(const char* moduleName, const char* functionName);
    HMODULE GetModuleHandlePEB(const char* lpModuleName);
    
    // 辅助函数
    bool SafeStringCompareA(const char* str1, const char* str2);
    bool SafeStringCompareIA(const char* str1, const char* str2);
    std::string GetSystemDirectoryPath();
    HMODULE LoadLibraryPEB(const char* dllName);
    FARPROC FindFunctionAddress(const char* moduleName, const char* functionName);
    void EnumerateModuleExports(HMODULE hModule);

} // namespace PEUtils