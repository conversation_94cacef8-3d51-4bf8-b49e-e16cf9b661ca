---
alwaysApply: true
description: MSRPC 回退方案结构与入口索引
---
# 方案概述
- 目标：在 DMA 设备不可用时，使用 leechcore 与 MemProcFS 的 vmm 子项目通过 MSRPC 完成主机通信。
- 入口解决方案： [CustomLeechServer.sln](mdc:CustomLeechServer/CustomLeechServer.sln)

# 关键路径与文件（就近定位）
- CustomLeechServer
  - 源码：
    - [src/main.cpp](mdc:CustomLeechServer/src/main.cpp)
    - [src/leechrpcserver_cpp.cpp](mdc:CustomLeechServer/src/leechrpcserver_cpp.cpp)
    - [src/leechrpcserver.c](mdc:CustomLeechServer/src/leechrpcserver.c)
    - [src/CustomLeechServer.cpp](mdc:CustomLeechServer/src/CustomLeechServer.cpp)
  - 头文件：
    - [include/Common.h](mdc:CustomLeechServer/include/Common.h)
    - [include/CustomLeechServer.h](mdc:CustomLeechServer/include/CustomLeechServer.h)
    - [include/DriverInterface.h](mdc:CustomLeechServer/include/DriverInterface.h)
    - [include/leechrpc.h](mdc:CustomLeechServer/include/leechrpc.h)
  - 配置/文档：
    - [RPC_CONFIG.md](mdc:CustomLeechServer/RPC_CONFIG.md)
    - [RPC_CLIENT_CONFIG.md](mdc:CustomLeechServer/RPC_CLIENT_CONFIG.md)
    - [RPC_SYNC_STATUS.md](mdc:CustomLeechServer/RPC_SYNC_STATUS.md)

- LeechCore
  - 头文件与库：
    - [includes/leechcore.h](mdc:LeechCore/includes/leechcore.h)
    - [includes/leechgrpc.h](mdc:LeechCore/includes/leechgrpc.h)
    - 预编译库（按架构匹配）：
      - [includes/lib64/leechcore.lib](mdc:LeechCore/includes/lib64/leechcore.lib)
      - [includes/lib32/leechcore.lib](mdc:LeechCore/includes/lib32/leechcore.lib)
      - [includes/libarm64/leechcore.lib](mdc:LeechCore/includes/libarm64/leechcore.lib)
  - RPC/核心实现（只读参考）：
    - [leechcore/leechcore.c](mdc:LeechCore/leechcore/leechcore.c)
    - [leechcore/leechrpcclient.c](mdc:LeechCore/leechcore/leechrpcclient.c)
    - [leechcore/leechrpcshared.c](mdc:LeechCore/leechcore/leechrpcshared.c)

- MemProcFS
  - vmm 关键：
    - [vmm/vmmdll.c](mdc:MemProcFS/vmm/vmmdll.c)
    - [vmm/vmm.c](mdc:MemProcFS/vmm/vmm.c)
    - [vmm/ob/ob_core.c](mdc:MemProcFS/vmm/ob/ob_core.c)
  - 头文件：
    - [includes/vmmdll.h](mdc:MemProcFS/includes/vmmdll.h)

# 约定与边界
- 架构选择：无 DMA → 走 MSRPC；有 DMA → 保持原路径。
- 改动落点：尽量将 RPC 相关改动限于 `CustomLeechServer` 与 `LeechCore` 的 RPC 层，避免侵入 `vmm` 核心代码路径。
- 依赖保持最小化：遵循“简洁、轻量、可验证”的实现偏好，仅在必要处引入依赖。