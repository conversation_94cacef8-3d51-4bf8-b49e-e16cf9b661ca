---
description: Windows 构建指引（MSVC/clang-cl、架构/链接与 PowerShell 注意事项）
---
# Windows 构建指引

- 入口与目标
  - 以解决方案为主： [CustomLeechServer.sln](mdc:CustomLeechServer/CustomLeechServer.sln)
  - 使用 VS 方案配置矩阵（Debug/Release x86/x64），与依赖库架构严格匹配。

- 工具链
  - MSVC 与 clang-cl 均可；为降低漂移，子项目编译选项保持一致。
  - 仅在交付约束需要时考虑静态 CRT；默认遵循解决方案设置。

- 头文件/库路径
  - 包含目录：`CustomLeechServer/include`、`LeechCore/includes`、`MemProcFS/includes`。
  - 链接库：按架构选择 `LeechCore/includes/lib{32,64,arm64}/leechcore.lib` 等。
  - 严禁混用 32/64 位制品。

- PowerShell 注意事项
  - 路径分隔 `\\` 与 `/` 混用可能导致转义问题；长命令分步执行。
  - 引号与转义（如 `^`）按需处理；避免一行内堆叠复杂参数。